#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 视频处理器
专门处理视频相关的操作，从主模块中分离出来
"""

import cv2
import os
from typing import Optional, Tuple
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtCore import QObject, pyqtSignal

from .logger import get_logger
from .constants import CoverEditConstants
from .decorators import error_handler, performance_monitor
from .resource_manager import register_video_capture

class VideoProcessor(QObject):
    """视频处理器 - 专门处理视频相关操作"""
    
    # 信号定义
    video_loaded = pyqtSignal(str, int, int, float, int, float)  # 路径, 宽, 高, 帧率, 总帧数, 时长
    frame_extracted = pyqtSignal(int, QPixmap)  # 帧号, 图像
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("VideoProcessor")
        
        # 视频相关属性
        self.video_cap = None
        self.video_path = ""
        self.video_width = 0
        self.video_height = 0
        self.fps = 0.0
        self.total_frames = 0
        self.duration = 0.0
        
    @error_handler(show_dialog=True)
    @performance_monitor(threshold_ms=200.0)
    def load_video(self, file_path: str) -> bool:
        """
        加载视频文件
        
        Args:
            file_path: 视频文件路径
            
        Returns:
            是否加载成功
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"视频文件不存在: {file_path}")
            
            # 检查文件格式
            _, ext = os.path.splitext(file_path.lower())
            if ext not in CoverEditConstants.SUPPORTED_VIDEO_FORMATS:
                raise ValueError(f"不支持的视频格式: {ext}")
            
            # 释放之前的视频资源
            self.release_video()
            
            # 打开视频文件
            self.video_cap = cv2.VideoCapture(file_path)
            if not self.video_cap.isOpened():
                # 尝试使用不同的后端
                self.logger.warning("默认后端无法打开视频，尝试其他后端...")
                self.video_cap = cv2.VideoCapture(file_path, cv2.CAP_FFMPEG)
                if not self.video_cap.isOpened():
                    raise Exception("无法打开视频文件（已尝试多种后端）")
            
            # 注册视频捕获资源
            register_video_capture(self.video_cap, f"主视频: {os.path.basename(file_path)}")
            
            # 获取视频信息
            self.video_path = file_path
            self.video_width = int(self.video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.video_height = int(self.video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.fps = self.video_cap.get(cv2.CAP_PROP_FPS)
            self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算时长
            if self.fps > 0:
                self.duration = self.total_frames / self.fps
            else:
                self.duration = 0.0
            
            # 验证视频参数
            if self.video_width <= 0 or self.video_height <= 0:
                raise ValueError(f"无效的视频分辨率: {self.video_width}x{self.video_height}")
            
            if self.total_frames <= 0:
                raise ValueError(f"无效的视频帧数: {self.total_frames}")
            
            # 记录视频信息
            self.logger.info(f"视频加载成功: {os.path.basename(file_path)}")
            self.logger.info(f"  - 分辨率: {self.video_width}x{self.video_height}")
            self.logger.info(f"  - 帧数: {self.total_frames}")
            self.logger.info(f"  - 帧率: {self.fps:.2f}")
            self.logger.info(f"  - 时长: {self.duration:.1f}秒 ({self.duration/60:.1f}分钟)")
            
            # 检查异常情况
            if self.duration > CoverEditConstants.MAX_VIDEO_DURATION:
                self.logger.warning(f"视频时长超过建议值 ({self.duration:.1f}秒)")
            
            # 发送信号
            self.video_loaded.emit(
                self.video_path, self.video_width, self.video_height,
                self.fps, self.total_frames, self.duration
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"视频加载失败: {e}")
            self.error_occurred.emit(str(e))
            return False
    
    @performance_monitor(threshold_ms=50.0)
    def get_frame(self, frame_number: int) -> Optional[QPixmap]:
        """
        获取指定帧的图像
        
        Args:
            frame_number: 帧号
            
        Returns:
            帧图像，失败返回None
        """
        try:
            if not self.video_cap or not self.video_cap.isOpened():
                self.logger.warning("视频未加载或已关闭")
                return None
            
            # 验证帧号
            if frame_number < 0 or frame_number >= self.total_frames:
                self.logger.warning(f"帧号超出范围: {frame_number} (总帧数: {self.total_frames})")
                return None
            
            # 设置帧位置
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # 读取帧
            ret, frame = self.video_cap.read()
            if not ret:
                self.logger.warning(f"无法读取第 {frame_number} 帧")
                return None
            
            # 转换为QPixmap
            pixmap = self._cv_frame_to_qpixmap(frame)
            if pixmap and not pixmap.isNull():
                self.frame_extracted.emit(frame_number, pixmap)
                return pixmap
            else:
                self.logger.warning(f"帧转换失败: {frame_number}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取帧失败: {e}")
            return None
    
    def _cv_frame_to_qpixmap(self, cv_frame) -> Optional[QPixmap]:
        """
        将OpenCV帧转换为QPixmap
        
        Args:
            cv_frame: OpenCV帧数据
            
        Returns:
            QPixmap对象
        """
        try:
            # 转换颜色空间 BGR -> RGB
            rgb_frame = cv2.cvtColor(cv_frame, cv2.COLOR_BGR2RGB)
            
            # 获取图像信息
            height, width, channel = rgb_frame.shape
            bytes_per_line = channel * width
            
            # 创建QImage
            q_image = QImage(
                rgb_frame.data, width, height, 
                bytes_per_line, QImage.Format.Format_RGB888
            )
            
            # 转换为QPixmap
            pixmap = QPixmap.fromImage(q_image)
            
            # 注册资源
            from .cover_edit_resource_manager import register_pixmap
            register_pixmap(pixmap, f"视频帧 {width}x{height}")
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"帧格式转换失败: {e}")
            return None
    
    def get_frame_at_time(self, time_seconds: float) -> Optional[QPixmap]:
        """
        获取指定时间的帧
        
        Args:
            time_seconds: 时间（秒）
            
        Returns:
            帧图像
        """
        if self.fps <= 0:
            return None
        
        frame_number = int(time_seconds * self.fps)
        return self.get_frame(frame_number)
    
    def get_video_info(self) -> dict:
        """
        获取视频信息
        
        Returns:
            视频信息字典
        """
        return {
            'path': self.video_path,
            'width': self.video_width,
            'height': self.video_height,
            'fps': self.fps,
            'total_frames': self.total_frames,
            'duration': self.duration,
            'aspect_ratio': self.video_width / self.video_height if self.video_height > 0 else 1.0,
            'is_loaded': self.video_cap is not None and self.video_cap.isOpened()
        }
    
    def is_video_loaded(self) -> bool:
        """检查视频是否已加载"""
        return (self.video_cap is not None and 
                self.video_cap.isOpened() and 
                self.total_frames > 0)
    
    def release_video(self):
        """释放视频资源"""
        try:
            if self.video_cap:
                # 从资源管理器中释放
                from .cover_edit_resource_manager import get_resource_manager
                get_resource_manager().release_resource(self.video_cap)
                
                # 释放OpenCV资源
                self.video_cap.release()
                self.video_cap = None
                
                self.logger.debug("视频资源已释放")
            
            # 重置属性
            self.video_path = ""
            self.video_width = 0
            self.video_height = 0
            self.fps = 0.0
            self.total_frames = 0
            self.duration = 0.0
            
        except Exception as e:
            self.logger.error(f"释放视频资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.release_video()
        except Exception:
            pass  # 析构函数中不抛出异常

# ============================================================================
# 便捷函数
# ============================================================================

def create_video_processor() -> VideoProcessor:
    """创建视频处理器实例"""
    return VideoProcessor()

# 全局视频处理器实例（可选）
_global_video_processor = None

def get_global_video_processor() -> VideoProcessor:
    """获取全局视频处理器实例"""
    global _global_video_processor
    if _global_video_processor is None:
        _global_video_processor = VideoProcessor()
    return _global_video_processor
