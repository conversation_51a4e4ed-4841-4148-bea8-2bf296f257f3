#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理器
负责处理所有视频相关的业务逻辑，保持与原模块完全一致的行为
"""

import os
import cv2
from typing import Optional
from PyQt6.QtWidgets import QFileDialog, QMessageBox
from PyQt6.QtGui import QPixmap, QImage
from PyQt6.QtCore import Qt

# 导入常量和工具
from cover_edit.utils.constants import CoverEditConstants
from cover_edit.utils.decorators import error_handler, performance_monitor
from cover_edit.utils.resource_manager import register_video_capture

class VideoHandler:
    """
    视频处理器
    🔒 保证：与原模块的视频处理逻辑完全一致
    """
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
    
    @error_handler(show_dialog=True)
    @performance_monitor(threshold_ms=200.0)
    def load_video(self, file_path: str = None) -> bool:
        """
        加载视频文件
        🔒 保证：与原模块的load_video方法行为完全一致
        """
        try:
            if not file_path:
                # 🔒 文件选择对话框 - 与原模块完全一致
                file_path, _ = QFileDialog.getOpenFileName(
                    self.parent,
                    "选择视频文件",
                    "",
                    "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv *.wmv *.m4v *.3gp);;所有文件 (*)"
                )
                
                if not file_path:
                    return False
            
            # 🔒 验证文件存在 - 与原模块完全一致
            if not os.path.exists(file_path):
                QMessageBox.warning(self.parent, "警告", f"文件不存在: {file_path}")
                return False
            
            # 🔒 释放之前的视频资源 - 与原模块完全一致
            if self.parent.video_cap:
                self.parent.video_cap.release()
                self.parent.video_cap = None
            
            # 🔒 打开视频文件 - 与原模块完全一致
            self.parent.video_cap = cv2.VideoCapture(file_path)
            if not self.parent.video_cap.isOpened():
                QMessageBox.critical(self.parent, "错误", "无法打开视频文件")
                return False
            
            # 🔒 安全地注册视频捕获资源
            try:
                register_video_capture(self.parent.video_cap, f"主视频: {os.path.basename(file_path)}")
            except Exception as e:
                self.logger.warning(f"注册视频资源失败: {e}")
                # 继续执行，不影响主要功能
            
            # 🔒 获取视频信息 - 与原模块完全一致
            self.parent.video_path = file_path
            self.parent.video_width = int(self.parent.video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.parent.video_height = int(self.parent.video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.parent.fps = self.parent.video_cap.get(cv2.CAP_PROP_FPS)
            self.parent.total_frames = int(self.parent.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 🔒 计算视频时长 - 与原模块完全一致
            if self.parent.fps > 0:
                self.parent.video_duration = self.parent.total_frames / self.parent.fps
            else:
                self.parent.video_duration = 0
            
            # 🔒 更新UI状态 - 与原模块完全一致
            self._update_ui_after_video_load()
            
            # 🔒 显示第一帧 - 与原模块完全一致
            self.show_frame(0)
            
            self.logger.info(f"视频加载成功: {os.path.basename(file_path)}")
            self.logger.info(f"分辨率: {self.parent.video_width}x{self.parent.video_height}")
            self.logger.info(f"帧数: {self.parent.total_frames}, 帧率: {self.parent.fps:.2f}")
            self.logger.info(f"时长: {self.parent.video_duration:.1f}秒")
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载视频失败: {e}")
            QMessageBox.critical(self.parent, "错误", f"加载视频时出错: {str(e)}")
            return False
    
    @performance_monitor(threshold_ms=50.0)
    def show_frame(self, frame_number: int) -> bool:
        """
        显示指定帧
        🔒 保证：与原模块的show_frame方法行为完全一致
        """
        try:
            if not self.parent.video_cap or not self.parent.video_cap.isOpened():
                return False
            
            # 🔒 验证帧号范围 - 与原模块完全一致
            if frame_number < 0 or frame_number >= self.parent.total_frames:
                return False
            
            # 🔒 设置帧位置 - 与原模块完全一致
            self.parent.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            # 🔒 读取帧 - 与原模块完全一致
            ret, frame = self.parent.video_cap.read()
            if not ret:
                return False
            
            # 🔒 转换为QPixmap - 与原模块完全一致
            pixmap = self._cv_frame_to_qpixmap(frame)
            if pixmap and hasattr(self.parent, 'video_preview') and self.parent.video_preview:
                self.parent.video_preview.setPixmap(pixmap)
                self.parent.current_frame = frame_number
                return True
            elif pixmap:
                # 如果预览组件不存在，记录警告但不失败
                self.logger.warning("video_preview组件不存在，无法显示帧")
                self.parent.current_frame = frame_number
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"显示帧失败: {e}")
            return False
    
    def _cv_frame_to_qpixmap(self, cv_frame) -> Optional[QPixmap]:
        """
        将OpenCV帧转换为QPixmap
        🔒 保证：与原模块的转换逻辑完全一致
        """
        try:
            # 🔒 转换颜色空间 - 与原模块完全一致
            rgb_frame = cv2.cvtColor(cv_frame, cv2.COLOR_BGR2RGB)
            
            # 🔒 获取图像信息 - 与原模块完全一致
            height, width, channel = rgb_frame.shape
            bytes_per_line = channel * width
            
            # 🔒 创建QImage - 与原模块完全一致
            q_image = QImage(
                rgb_frame.data, width, height, 
                bytes_per_line, QImage.Format.Format_RGB888
            )
            
            # 🔒 转换为QPixmap - 与原模块完全一致
            pixmap = QPixmap.fromImage(q_image)
            
            # 🔧 优化：不在这里缩放，让PreviewLabel自己处理缩放
            # 这样可以避免重复缩放导致的模糊问题
            # PreviewLabel会使用缓存机制来优化缩放
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"帧格式转换失败: {e}")
            return None
    
    def _update_ui_after_video_load(self):
        """更新视频加载后的UI状态"""
        
        # 🔒 启用相关按钮 - 与原模块完全一致
        buttons_to_enable = [
            'snapshot_btn', 'smart_snapshot_btn', 'export_btn',
            'text_edit_btn_export', 'export_settings_btn_export',
            'add_text_btn', 'import_cover_btn'
        ]

        for btn_name in buttons_to_enable:
            if hasattr(self.parent, btn_name):
                btn = getattr(self.parent, btn_name)
                if btn:
                    btn.setEnabled(True)

        # 🚀 修复：加载视频到媒体播放器
        if hasattr(self.parent, 'load_media_player'):
            self.parent.load_media_player(self.parent._video_path)

        # 🔒 发送视频加载信号 - 与原模块完全一致
        if hasattr(self.parent, 'video_loaded'):
            self.parent.video_loaded.emit(self.parent._video_path)
        
        # 🔒 更新进度滑块 - 与原模块完全一致
        if hasattr(self.parent, 'position_slider'):
            self.parent.position_slider.setMaximum(self.parent.total_frames - 1)
            self.parent.position_slider.setValue(0)
    
    def get_current_frame_image(self) -> Optional[QPixmap]:
        """获取当前帧的图像"""
        if hasattr(self.parent, 'video_preview') and self.parent.video_preview.pixmap():
            return self.parent.video_preview.pixmap()
        return None
    
    def is_video_loaded(self) -> bool:
        """检查是否已加载视频"""
        return (self.parent.video_cap is not None and 
                self.parent.video_cap.isOpened() and 
                self.parent.total_frames > 0)
