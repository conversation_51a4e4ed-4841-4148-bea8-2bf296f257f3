#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 模块装饰器工具
提供统一的错误处理、性能监控、资源管理等装饰器
"""

import functools
import time
import traceback
from typing import Callable, Any, Optional
from contextlib import contextmanager

from PyQt6.QtWidgets import QMessageBox, QApplication
from .logger import get_logger
from .constants import CoverEditConstants

# ============================================================================
# 错误处理装饰器
# ============================================================================

def error_handler(show_dialog: bool = True, 
                 return_value: Any = None,
                 logger_name: str = "ErrorHandler"):
    """
    统一的错误处理装饰器
    
    Args:
        show_dialog: 是否显示错误对话框
        return_value: 发生错误时的返回值
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            
            try:
                return func(*args, **kwargs)
                
            except Exception as e:
                # 记录详细的错误信息
                error_msg = f"函数 {func.__name__} 执行失败: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"错误详情:\n{traceback.format_exc()}")
                
                # 显示用户友好的错误对话框
                if show_dialog and QApplication.instance():
                    try:
                        msg_box = QMessageBox()
                        msg_box.setIcon(QMessageBox.Icon.Critical)
                        msg_box.setWindowTitle("操作失败")
                        msg_box.setText(f"执行 {func.__name__} 时发生错误")
                        msg_box.setInformativeText(str(e))
                        msg_box.setDetailedText(traceback.format_exc())
                        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                        msg_box.exec()
                    except Exception as dialog_error:
                        logger.error(f"显示错误对话框失败: {dialog_error}")
                
                return return_value
                
        return wrapper
    return decorator

def safe_execute(default_return=None, logger_name: str = "SafeExecute"):
    """
    安全执行装饰器 - 捕获所有异常但不显示对话框
    适用于后台任务和非关键操作
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(f"函数 {func.__name__} 执行失败，使用默认返回值: {e}")
                return default_return
                
        return wrapper
    return decorator

# ============================================================================
# 性能监控装饰器
# ============================================================================

def performance_monitor(threshold_ms: float = 100.0, 
                       logger_name: str = "Performance"):
    """
    性能监控装饰器
    
    Args:
        threshold_ms: 性能警告阈值（毫秒）
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                end_time = time.time()
                duration_ms = (end_time - start_time) * 1000
                
                if duration_ms > CoverEditConstants.PERFORMANCE_CRITICAL_THRESHOLD:
                    logger.critical(f"函数 {func.__name__} 执行时间严重过长: {duration_ms:.2f}ms")
                elif duration_ms > threshold_ms:
                    logger.warning(f"函数 {func.__name__} 执行时间过长: {duration_ms:.2f}ms")
                else:
                    logger.debug(f"函数 {func.__name__} 执行时间: {duration_ms:.2f}ms")

        return wrapper  # 🚀 修复：缩进问题
    return decorator

def memory_monitor(logger_name: str = "Memory"):
    """
    内存监控装饰器
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            
            try:
                import psutil
                import os
                
                process = psutil.Process(os.getpid())
                
                # 记录执行前内存
                before_memory = process.memory_info().rss / 1024 / 1024
                
                result = func(*args, **kwargs)
                
                # 记录执行后内存
                after_memory = process.memory_info().rss / 1024 / 1024
                memory_diff = after_memory - before_memory
                
                if memory_diff > 50:  # 内存增长超过50MB
                    logger.warning(f"函数 {func.__name__} 内存增长: {memory_diff:.1f}MB "
                                 f"(前: {before_memory:.1f}MB, 后: {after_memory:.1f}MB)")
                else:
                    logger.debug(f"函数 {func.__name__} 内存变化: {memory_diff:.1f}MB")
                
                return result
                
            except ImportError:
                logger.debug("psutil未安装，跳过内存监控")
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"内存监控失败: {e}")
                return func(*args, **kwargs)
                
        return wrapper
    return decorator

# ============================================================================
# 资源管理装饰器
# ============================================================================

def auto_cleanup(cleanup_attrs: list = None, logger_name: str = "Cleanup"):
    """
    自动清理资源装饰器
    
    Args:
        cleanup_attrs: 需要清理的属性名列表
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            logger = get_logger(logger_name)
            
            try:
                return func(self, *args, **kwargs)
            finally:
                # 清理指定的资源
                if cleanup_attrs:
                    for attr_name in cleanup_attrs:
                        if hasattr(self, attr_name):
                            attr = getattr(self, attr_name)
                            try:
                                if hasattr(attr, 'release'):
                                    attr.release()
                                    logger.debug(f"已释放资源: {attr_name}")
                                elif hasattr(attr, 'clear'):
                                    attr.clear()
                                    logger.debug(f"已清理资源: {attr_name}")
                                elif hasattr(attr, 'close'):
                                    attr.close()
                                    logger.debug(f"已关闭资源: {attr_name}")
                            except Exception as e:
                                logger.warning(f"清理资源 {attr_name} 失败: {e}")
                                
        return wrapper
    return decorator

# ============================================================================
# 重试装饰器
# ============================================================================

def retry(max_attempts: int = 3, 
          delay_seconds: float = 1.0,
          exceptions: tuple = (Exception,),
          logger_name: str = "Retry"):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay_seconds: 重试间隔（秒）
        exceptions: 需要重试的异常类型
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name)
            
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，"
                                     f"{delay_seconds}秒后重试")
                        time.sleep(delay_seconds)
                    else:
                        logger.error(f"函数 {func.__name__} 重试 {max_attempts} 次后仍然失败")
                        
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
            
        return wrapper
    return decorator

# ============================================================================
# 缓存装饰器
# ============================================================================

def simple_cache(maxsize: int = 128, logger_name: str = "Cache"):
    """
    简单的缓存装饰器
    
    Args:
        maxsize: 最大缓存大小
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        cache = {}
        cache_order = []
        logger = get_logger(logger_name)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 创建缓存键
            cache_key = str(args) + str(sorted(kwargs.items()))
            
            # 检查缓存
            if cache_key in cache:
                logger.debug(f"缓存命中: {func.__name__}")
                return cache[cache_key]
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            cache[cache_key] = result
            cache_order.append(cache_key)
            
            # 清理过期缓存
            if len(cache) > maxsize:
                oldest_key = cache_order.pop(0)
                del cache[oldest_key]
                logger.debug(f"清理过期缓存: {func.__name__}")
            
            logger.debug(f"缓存存储: {func.__name__}")
            return result
            
        # 添加清理缓存的方法
        wrapper.clear_cache = lambda: cache.clear() or cache_order.clear()
        wrapper.cache_info = lambda: {"size": len(cache), "maxsize": maxsize}
        
        return wrapper
    return decorator

# ============================================================================
# 上下文管理器
# ============================================================================

@contextmanager
def error_context(operation_name: str, logger_name: str = "Context"):
    """
    错误上下文管理器
    
    Args:
        operation_name: 操作名称
        logger_name: 日志器名称
    """
    logger = get_logger(logger_name)
    logger.info(f"开始执行: {operation_name}")
    
    try:
        yield
        logger.info(f"成功完成: {operation_name}")
    except Exception as e:
        logger.error(f"执行失败: {operation_name} - {e}")
        raise
    finally:
        logger.debug(f"清理完成: {operation_name}")

@contextmanager
def performance_context(operation_name: str, 
                       threshold_ms: float = 100.0,
                       logger_name: str = "Performance"):
    """
    性能监控上下文管理器
    
    Args:
        operation_name: 操作名称
        threshold_ms: 性能警告阈值（毫秒）
        logger_name: 日志器名称
    """
    logger = get_logger(logger_name)
    start_time = time.time()
    
    try:
        yield
    finally:
        duration_ms = (time.time() - start_time) * 1000
        if duration_ms > threshold_ms:
            logger.warning(f"操作 {operation_name} 耗时: {duration_ms:.2f}ms")
        else:
            logger.debug(f"操作 {operation_name} 耗时: {duration_ms:.2f}ms")

# ============================================================================
# 组合装饰器
# ============================================================================

def robust_operation(show_error_dialog: bool = True,
                    performance_threshold: float = 100.0,
                    max_retries: int = 1,
                    logger_name: str = "RobustOperation"):
    """
    组合装饰器：错误处理 + 性能监控 + 重试
    
    Args:
        show_error_dialog: 是否显示错误对话框
        performance_threshold: 性能警告阈值（毫秒）
        max_retries: 最大重试次数
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        # 按顺序应用装饰器
        decorated_func = func
        decorated_func = performance_monitor(performance_threshold, logger_name)(decorated_func)
        if max_retries > 1:
            decorated_func = retry(max_retries, logger_name=logger_name)(decorated_func)
        decorated_func = error_handler(show_error_dialog, logger_name=logger_name)(decorated_func)
        
        return decorated_func
    return decorator

# ============================================================================
# 使用示例
# ============================================================================

if __name__ == "__main__":
    # 示例：使用各种装饰器
    
    @error_handler(show_dialog=False)
    @performance_monitor(threshold_ms=50.0)
    def example_function():
        """示例函数"""
        time.sleep(0.1)  # 模拟耗时操作
        return "成功"
    
    @robust_operation(show_error_dialog=False, performance_threshold=50.0, max_retries=3)
    def example_robust_function():
        """示例健壮函数"""
        import random
        if random.random() < 0.7:  # 70%概率失败
            raise Exception("随机失败")
        return "成功"
    
    # 测试函数
    print("测试普通装饰器:")
    result = example_function()
    print(f"结果: {result}")
    
    print("\n测试组合装饰器:")
    try:
        result = example_robust_function()
        print(f"结果: {result}")
    except Exception as e:
        print(f"最终失败: {e}")
