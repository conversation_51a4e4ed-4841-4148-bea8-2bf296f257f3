#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出工作线程管理器
负责管理视频导出相关的线程，保持与原模块完全一致的行为
"""

import os
import time
import tempfile
from typing import Optional, Dict, Any
from PyQt6.QtWidgets import QMessageBox, QFileDialog
from PyQt6.QtCore import QThread, pyqtSignal
from PyQt6.QtGui import QPixmap

# 导入常量
from cover_edit.utils.constants import CoverEditConstants
from cover_edit.utils.decorators import error_handler, performance_monitor

class VideoExportWorker(QThread):
    """
    视频导出工作线程
    🔒 保证：与原模块的VideoExportWorker完全一致
    """
    progress_updated = pyqtSignal(int)
    export_finished = pyqtSignal(str, bool, str)

    def __init__(self, video_path: str, output_path: str, cover_image: Optional[QPixmap],
                 fps: float, start_frame: int, end_frame: int, ffmpeg_path: str,
                 export_settings: Optional[Dict[str, Any]] = None, encoder: str = 'libx264',
                 video_width: int = 1920, video_height: int = 1080):
        super().__init__()
        self.video_path = video_path
        self.output_path = output_path
        self.cover_image = cover_image
        self.fps = fps
        self.start_frame = start_frame
        self.end_frame = end_frame
        self.ffmpeg_path = ffmpeg_path
        self.encoder = encoder
        self.export_settings = export_settings or {}
        self.video_width = video_width
        self.video_height = video_height

        # 🚀 优化：初始化logger
        from cover_edit.utils.logger import get_logger
        self.logger = get_logger("VideoExportWorker")
        self.logger.info(f"VideoExportWorker接收到的编码器: {encoder}")

    def run(self):
        """执行导出"""
        try:
            self.logger.info("开始视频导出线程")

            # 🔒 检查FFmpeg路径 - 与原模块完全一致
            if not self.ffmpeg_path or not os.path.exists(self.ffmpeg_path):
                self.export_finished.emit(self.output_path, False, "FFmpeg路径无效")
                return

            # 🔒 保存封面到临时文件 - 与原模块完全一致
            temp_cover_path = None
            if self.cover_image and not self.cover_image.isNull():
                temp_cover_path = self._save_cover_to_temp()
                if not temp_cover_path:
                    self.export_finished.emit(self.output_path, False, "保存封面失败")
                    return

            # 🔒 构建FFmpeg命令 - 与原模块完全一致
            cmd = self._build_ffmpeg_command(temp_cover_path)

            # 🔒 执行FFmpeg命令 - 与原模块完全一致
            success = self._execute_ffmpeg_command(cmd)

            # 🔒 清理临时文件 - 与原模块完全一致
            if temp_cover_path and os.path.exists(temp_cover_path):
                try:
                    os.remove(temp_cover_path)
                except Exception as e:
                    self.logger.warning(f"清理临时文件失败: {e}")

            # 🔒 发送完成信号 - 与原模块完全一致
            if success:
                self.export_finished.emit(self.output_path, True, "")
            else:
                self.export_finished.emit(self.output_path, False, "FFmpeg执行失败")

        except Exception as e:
            self.logger.error(f"导出线程异常: {e}")
            self.export_finished.emit(self.output_path, False, str(e))

    def _save_cover_to_temp(self) -> Optional[str]:
        """保存封面到临时文件"""
        try:
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()

            if self.cover_image.save(temp_path, 'PNG'):
                self.logger.debug(f"封面保存到临时文件: {temp_path}")
                return temp_path
            else:
                self.logger.error("保存封面到临时文件失败")
                return None

        except Exception as e:
            self.logger.error(f"保存临时封面失败: {e}")
            return None

    def _build_ffmpeg_command(self, cover_path: Optional[str]) -> list:
        """构建FFmpeg命令 - 使用三步法正确合成封面"""
        try:
            if cover_path:
                # 🚀 使用三步法：参考原版cover_edit的成功实现
                return self._build_three_step_command(cover_path)
            else:
                # 没有封面时的简单命令
                return self._build_simple_command()

        except Exception as e:
            self.logger.error(f"构建FFmpeg命令失败: {e}")
            return []

    def _build_three_step_command(self, cover_path: str) -> list:
        """三步法构建命令 - 参考原版实现，使用导出设置参数"""
        try:
            # 计算时间参数
            start_time = self.start_frame / self.fps
            duration = (self.end_frame - self.start_frame) / self.fps

            # 🚀 修复：正确获取导出设置
            if hasattr(self.export_settings, 'get_settings'):
                settings_dict = self.export_settings.get_settings()
                self.logger.info(f"从导出设置模块获取设置: {settings_dict}")
            elif isinstance(self.export_settings, dict):
                settings_dict = self.export_settings
                self.logger.info(f"使用字典形式的导出设置: {settings_dict}")
            else:
                # 🚀 修复：默认启用GPU加速
                settings_dict = {
                    'use_gpu': True,  # 默认启用GPU
                    'quality': 'high',
                    'width': 1440,
                    'height': 2560,
                    'fps': 30.0,
                    'target_bitrate': 8000,
                    'preset': 'medium'
                }
                self.logger.warning(f"导出设置为空，使用默认设置: {settings_dict}")

            # 🚀 修复：使用导出设置中的分辨率和帧率
            target_width = settings_dict.get('width', self.video_width)
            target_height = settings_dict.get('height', self.video_height)
            target_fps = settings_dict.get('fps', self.fps)

            self.logger.info(f"导出参数: {target_width}x{target_height} @ {target_fps}fps")

            # 🚀 新增：智能比例处理
            scale_filter = self._get_smart_scale_filter(
                self.video_width, self.video_height,
                target_width, target_height
            )

            # 🚀 修复：统一使用5帧以内的封面显示时长
            # 计算5帧对应的时长，但不超过0.2秒
            max_frames = 5
            frame_duration = min(max_frames / target_fps, 0.2)  # 5帧时长，最大0.2秒

            # 🚀 特殊修复：ts格式使用更短的封面时长
            is_ts_format = self.video_path.lower().endswith(('.ts', '.m2ts', '.mts'))
            if is_ts_format:
                # ts格式使用3帧或0.1秒，取较小值
                frame_duration = min(3.0 / target_fps, 0.1)
                self.logger.info(f"TS格式特殊处理 - 封面时长: {frame_duration}秒 ({frame_duration * target_fps:.1f}帧)")
            else:
                self.logger.info(f"标准格式封面时长: {frame_duration}秒 ({frame_duration * target_fps:.1f}帧)")

            # 🚀 特殊修复：ts格式使用不同的FFmpeg命令
            if is_ts_format:
                # ts格式使用特殊的处理方式，避免封面时长问题
                cmd = [
                    self.ffmpeg_path, '-y',
                    # 输入1：封面图片（精确控制帧数）
                    '-loop', '1', '-t', str(frame_duration), '-r', str(target_fps), '-i', cover_path,
                    # 输入2：原视频（裁剪时间段）
                    '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                    # 🚀 ts格式特殊滤镜：强制帧数控制
                    '-filter_complex',
                    f'[0:v]{scale_filter},trim=duration={frame_duration}[cover];'
                    f'[1:v]{scale_filter}[main];'
                    f'[cover][main]concat=n=2:v=1[outv]',
                    # 映射输出
                    '-map', '[outv]',
                    '-map', '1:a?',  # 音频轨道可选
                    # 🚀 ts格式特殊参数
                    '-r', str(target_fps),
                    '-avoid_negative_ts', 'make_zero',
                    '-fflags', '+genpts',
                ]
            else:
                # 标准格式使用原有命令
                cmd = [
                    self.ffmpeg_path, '-y',
                    # 输入1：封面图片（循环显示）
                    '-loop', '1', '-t', str(frame_duration), '-i', cover_path,
                    # 输入2：原视频（裁剪时间段）
                    '-ss', str(start_time), '-t', str(duration), '-i', self.video_path,
                    # 🚀 修复：滤镜使用智能比例处理
                    '-filter_complex',
                    f'[0:v]{scale_filter}[cover];'
                    f'[1:v]{scale_filter}[main];'
                    f'[cover][main]concat=n=2:v=1[outv]',
                    # 映射输出
                    '-map', '[outv]',
                    '-map', '1:a?',  # 音频轨道可选
                    # 🚀 修复：设置输出帧率
                    '-r', str(target_fps),
                ]



            # 🚀 修复：添加完整的编码设置
            use_gpu = settings_dict.get('use_gpu', False)
            if use_gpu:
                gpu_encoder = self._get_gpu_encoder()
                if gpu_encoder:
                    cmd.extend(['-c:v', gpu_encoder])
                    self.logger.info(f"启用GPU编码器: {gpu_encoder}")
                else:
                    cmd.extend(['-c:v', self.encoder])
                    self.logger.warning("GPU编码器不可用，使用CPU编码")
            else:
                cmd.extend(['-c:v', self.encoder])

            # 🚀 修复：音频编码设置
            audio_codec = settings_dict.get('audio_codec', 'AAC')
            if audio_codec == 'AAC':
                cmd.extend(['-c:a', 'aac'])
                # 音频比特率
                audio_bitrate = settings_dict.get('audio_bitrate', 128)
                cmd.extend(['-b:a', f'{audio_bitrate}k'])
                # 音频采样率
                audio_sample_rate = settings_dict.get('audio_sample_rate', 44100)
                cmd.extend(['-ar', str(audio_sample_rate)])
            else:
                cmd.extend(['-c:a', 'aac'])  # 默认使用AAC

            # 🚀 修复：质量设置 - 根据分辨率动态调整码率
            quality_preset = settings_dict.get('quality_preset', '高质量 (推荐)')
            bitrate_mode = settings_dict.get('bitrate_mode', 'VBR')

            # 🚀 修复：根据分辨率计算合适的码率 - 支持4K
            pixel_count = target_width * target_height
            if pixel_count >= 2160 * 3840:  # 4K及以上
                base_bitrate = 25000  # 25Mbps
                high_bitrate = 35000  # 35Mbps
                ultra_bitrate = 50000 # 50Mbps
            elif pixel_count >= 1440 * 2560:  # 2K/1440p
                base_bitrate = 15000  # 15Mbps
                high_bitrate = 20000  # 20Mbps
                ultra_bitrate = 25000 # 25Mbps
            elif pixel_count >= 1080 * 1920:  # 1080p/FHD
                base_bitrate = 10000  # 10Mbps
                high_bitrate = 15000  # 15Mbps
                ultra_bitrate = 20000 # 20Mbps
            else:  # 720p及以下
                base_bitrate = 6000   # 6Mbps
                high_bitrate = 8000   # 8Mbps
                ultra_bitrate = 12000 # 12Mbps

            # 🚀 修复：提供真正的高码率选项
            if '超高质量' in quality_preset:
                # 超高质量使用高码率VBR，确保足够的码率
                cmd.extend(['-b:v', f'{ultra_bitrate}k', '-maxrate', f'{int(ultra_bitrate * 1.2)}k', '-bufsize', f'{ultra_bitrate * 2}k'])
                self.logger.info(f"超高质量模式: 目标码率 {ultra_bitrate}kbps")
            elif '高质量' in quality_preset:
                # 高质量使用高码率VBR
                cmd.extend(['-b:v', f'{high_bitrate}k', '-maxrate', f'{int(high_bitrate * 1.2)}k', '-bufsize', f'{high_bitrate * 2}k'])
                self.logger.info(f"高质量模式: 目标码率 {high_bitrate}kbps")
            elif '中等质量' in quality_preset:
                # 中等质量使用基础码率
                cmd.extend(['-b:v', f'{base_bitrate}k', '-maxrate', f'{int(base_bitrate * 1.2)}k', '-bufsize', f'{base_bitrate * 2}k'])
                self.logger.info(f"中等质量模式: 目标码率 {base_bitrate}kbps")
            elif '低质量' in quality_preset:
                # 低质量使用较低码率
                low_bitrate = int(base_bitrate * 0.6)
                cmd.extend(['-b:v', f'{low_bitrate}k', '-maxrate', f'{int(low_bitrate * 1.2)}k', '-bufsize', f'{low_bitrate * 2}k'])
                self.logger.info(f"低质量模式: 目标码率 {low_bitrate}kbps")
            elif 'CRF' in quality_preset:
                # CRF恒定质量模式
                crf_value = settings_dict.get('crf_value', 18)
                cmd.extend(['-crf', str(crf_value)])
                self.logger.info(f"CRF恒定质量模式: CRF {crf_value}")
            elif bitrate_mode == 'CBR':
                # 恒定码率
                target_bitrate = settings_dict.get('target_bitrate', high_bitrate)
                cmd.extend(['-b:v', f'{target_bitrate}k', '-minrate', f'{target_bitrate}k', '-maxrate', f'{target_bitrate}k'])
                self.logger.info(f"CBR恒定码率模式: {target_bitrate}kbps")
            elif bitrate_mode == 'VBR':
                # 可变码率模式
                target_bitrate = settings_dict.get('target_bitrate', high_bitrate)
                max_bitrate = settings_dict.get('max_bitrate', int(target_bitrate * 1.5))
                cmd.extend(['-b:v', f'{target_bitrate}k', '-maxrate', f'{max_bitrate}k', '-bufsize', f'{target_bitrate * 2}k'])
                self.logger.info(f"VBR可变码率模式: 目标 {target_bitrate}kbps, 最大 {max_bitrate}kbps")
            else:
                # 默认使用高质量码率
                cmd.extend(['-b:v', f'{high_bitrate}k', '-maxrate', f'{int(high_bitrate * 1.2)}k', '-bufsize', f'{high_bitrate * 2}k'])
                self.logger.info(f"默认高质量模式: 目标码率 {high_bitrate}kbps")

            # 🚀 修复：性能预设
            preset = settings_dict.get('preset', 'medium')
            cmd.extend(['-preset', preset])

            # 🚀 修复：像素格式
            cmd.extend(['-pix_fmt', 'yuv420p'])

            # 其他优化参数
            cmd.extend(['-avoid_negative_ts', 'make_zero'])

            # 输出文件
            cmd.append(self.output_path)

            return cmd

        except Exception as e:
            self.logger.error(f"构建三步法命令失败: {e}")
            return []

    def _build_simple_command(self) -> list:
        """构建简单命令（无封面）- 使用导出设置参数"""
        try:
            # 计算时间参数
            start_time = self.start_frame / self.fps
            duration = (self.end_frame - self.start_frame) / self.fps

            # 🚀 修复：正确获取导出设置（与三步法保持一致）
            if hasattr(self.export_settings, 'get_settings'):
                settings_dict = self.export_settings.get_settings()
                self.logger.info(f"从导出设置模块获取设置: {settings_dict}")
            elif isinstance(self.export_settings, dict):
                settings_dict = self.export_settings
                self.logger.info(f"使用字典形式的导出设置: {settings_dict}")
            else:
                # 🚀 修复：默认启用GPU加速
                settings_dict = {
                    'use_gpu': True,  # 默认启用GPU
                    'quality': 'high',
                    'width': 1440,
                    'height': 2560,
                    'fps': 30.0,
                    'target_bitrate': 8000,
                    'preset': 'medium'
                }
                self.logger.warning(f"导出设置为空，使用默认设置: {settings_dict}")

            # 🚀 修复：使用导出设置中的分辨率和帧率
            target_width = settings_dict.get('width', self.video_width)
            target_height = settings_dict.get('height', self.video_height)
            target_fps = settings_dict.get('fps', self.fps)

            cmd = [
                self.ffmpeg_path, '-y',
                '-ss', str(start_time),
                '-t', str(duration),
                '-i', self.video_path
            ]

            # 🚀 修复：添加智能视频滤镜进行比例处理和帧率调整
            if target_width != self.video_width or target_height != self.video_height or target_fps != self.fps:
                # 🚀 新增：使用智能比例处理
                scale_filter = self._get_smart_scale_filter(
                    self.video_width, self.video_height,
                    target_width, target_height
                )
                cmd.extend(['-vf', scale_filter])
                cmd.extend(['-r', str(target_fps)])

            # 添加编码设置（复用三步法的逻辑）
            use_gpu = settings_dict.get('use_gpu', False)
            if use_gpu:
                gpu_encoder = self._get_gpu_encoder()
                if gpu_encoder:
                    cmd.extend(['-c:v', gpu_encoder])
                else:
                    cmd.extend(['-c:v', self.encoder])
            else:
                cmd.extend(['-c:v', self.encoder])

            # 音频编码设置
            audio_codec = settings_dict.get('audio_codec', 'AAC')
            if audio_codec == 'AAC':
                cmd.extend(['-c:a', 'aac'])
                audio_bitrate = settings_dict.get('audio_bitrate', 128)
                cmd.extend(['-b:a', f'{audio_bitrate}k'])
                audio_sample_rate = settings_dict.get('audio_sample_rate', 44100)
                cmd.extend(['-ar', str(audio_sample_rate)])
            else:
                cmd.extend(['-c:a', 'aac'])

            # 🚀 修复：质量设置（复用三步法的高码率逻辑）
            quality_preset = settings_dict.get('quality_preset', '高质量 (推荐)')
            bitrate_mode = settings_dict.get('bitrate_mode', 'VBR')

            # 🚀 修复：根据分辨率计算合适的码率 - 支持4K
            pixel_count = target_width * target_height
            if pixel_count >= 2160 * 3840:  # 4K及以上
                base_bitrate = 25000
                high_bitrate = 35000
                ultra_bitrate = 50000
            elif pixel_count >= 1440 * 2560:  # 2K/1440p
                base_bitrate = 15000
                high_bitrate = 20000
                ultra_bitrate = 25000
            elif pixel_count >= 1080 * 1920:  # 1080p/FHD
                base_bitrate = 10000
                high_bitrate = 15000
                ultra_bitrate = 20000
            else:  # 720p及以下
                base_bitrate = 6000
                high_bitrate = 8000
                ultra_bitrate = 12000

            if '超高质量' in quality_preset:
                cmd.extend(['-b:v', f'{ultra_bitrate}k', '-maxrate', f'{int(ultra_bitrate * 1.2)}k', '-bufsize', f'{ultra_bitrate * 2}k'])
            elif '高质量' in quality_preset:
                cmd.extend(['-b:v', f'{high_bitrate}k', '-maxrate', f'{int(high_bitrate * 1.2)}k', '-bufsize', f'{high_bitrate * 2}k'])
            elif '中等质量' in quality_preset:
                cmd.extend(['-b:v', f'{base_bitrate}k', '-maxrate', f'{int(base_bitrate * 1.2)}k', '-bufsize', f'{base_bitrate * 2}k'])
            elif '低质量' in quality_preset:
                low_bitrate = int(base_bitrate * 0.6)
                cmd.extend(['-b:v', f'{low_bitrate}k', '-maxrate', f'{int(low_bitrate * 1.2)}k', '-bufsize', f'{low_bitrate * 2}k'])
            elif 'CRF' in quality_preset:
                crf_value = settings_dict.get('crf_value', 18)
                cmd.extend(['-crf', str(crf_value)])
            else:
                cmd.extend(['-b:v', f'{high_bitrate}k', '-maxrate', f'{int(high_bitrate * 1.2)}k', '-bufsize', f'{high_bitrate * 2}k'])

            # 性能预设
            preset = settings_dict.get('preset', 'medium')
            cmd.extend(['-preset', preset])

            # 像素格式
            cmd.extend(['-pix_fmt', 'yuv420p'])

            cmd.append(self.output_path)

            return cmd

        except Exception as e:
            self.logger.error(f"构建简单命令失败: {e}")
            return []

    def _get_gpu_encoder(self) -> Optional[str]:
        """检测可用的GPU编码器 - 参考原版实现"""
        try:
            import subprocess

            # 🚀 参考原版：先检测编码器列表
            try:
                result = subprocess.run([self.ffmpeg_path, '-encoders'],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    encoders = result.stdout
                    self.logger.debug(f"FFmpeg编码器列表获取成功")

                    # 🚀 参考原版：实际测试GPU编码器可用性
                    gpu_encoders = ['h264_nvenc', 'h264_qsv', 'h264_amf']

                    for encoder in gpu_encoders:
                        if encoder in encoders:
                            # 实际测试编码器是否可用
                            if self._test_gpu_encoder(encoder):
                                self.logger.info(f"✅ GPU编码器可用: {encoder}")
                                return encoder
                            else:
                                self.logger.debug(f"GPU编码器 {encoder} 测试失败")
                        else:
                            self.logger.debug(f"GPU编码器 {encoder} 不在列表中")

            except subprocess.TimeoutExpired:
                self.logger.warning("检测GPU编码器超时")
            except Exception as e:
                self.logger.warning(f"检测GPU编码器失败: {e}")

            self.logger.info("未找到可用的GPU编码器，将使用CPU编码")
            return None

        except Exception as e:
            self.logger.error(f"GPU编码器检测异常: {e}")
            return None

    def _test_gpu_encoder(self, encoder: str) -> bool:
        """测试GPU编码器是否真正可用 - 参考原版实现"""
        try:
            import subprocess

            # 🚀 参考原版：使用测试源进行实际编码测试
            test_cmd = [
                self.ffmpeg_path,
                '-f', 'lavfi',
                '-i', 'testsrc=duration=1:size=320x240:rate=1',
                '-c:v', encoder,
                '-f', 'null',
                '-'
            ]

            result = subprocess.run(test_cmd, capture_output=True, timeout=15)

            if result.returncode == 0:
                self.logger.debug(f"GPU编码器 {encoder} 测试通过")
                return True
            else:
                self.logger.debug(f"GPU编码器 {encoder} 测试失败: {result.stderr.decode('utf-8', errors='ignore')}")
                return False

        except subprocess.TimeoutExpired:
            self.logger.warning(f"GPU编码器 {encoder} 测试超时")
            return False
        except Exception as e:
            self.logger.warning(f"GPU编码器 {encoder} 测试异常: {e}")
            return False

    def _execute_ffmpeg_command(self, cmd: list) -> bool:
        """执行FFmpeg命令"""
        try:
            import subprocess
            import time
            import re

            self.logger.info(f"FFmpeg命令: {' '.join(cmd)}")

            # 🔒 启动FFmpeg进程 - 与原模块完全一致
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            # 🚀 优化：真正的FFmpeg进度解析
            progress = 0
            last_update_time = time.time()
            total_duration = getattr(self, 'end_frame', 0) - getattr(self, 'start_frame', 0)
            if total_duration <= 0:
                total_duration = 100  # 默认值

            # 启动线程读取stderr（FFmpeg的进度输出）
            import threading
            stderr_lines = []

            def read_stderr():
                try:
                    for line in iter(process.stderr.readline, ''):
                        if line:
                            stderr_lines.append(line.strip())
                except:
                    pass

            stderr_thread = threading.Thread(target=read_stderr, daemon=True)
            stderr_thread.start()

            while process.poll() is None:
                if self.isInterruptionRequested():
                    process.terminate()
                    return False

                current_time = time.time()

                # 🚀 修复：改进进度更新逻辑
                real_progress = self._parse_ffmpeg_progress(stderr_lines)

                if real_progress is not None:
                    # 🚀 修复：使用真实进度，但确保单调递增
                    if real_progress > progress:
                        progress = real_progress
                        last_update_time = current_time
                        self.progress_updated.emit(int(progress))
                elif current_time - last_update_time >= 2.0:  # 🚀 修复：降低模拟进度更新频率
                    # 如果无法解析真实进度，使用更保守的模拟进度
                    if progress < 80:
                        progress = min(progress + 1, 80)  # 🚀 修复：更保守的增长
                    elif progress < 95:
                        progress = min(progress + 0.2, 95)  # 🚀 修复：接近完成时更慢

                    last_update_time = current_time
                    self.progress_updated.emit(int(progress))

                self.msleep(200)

            # 🔒 等待完成并获取结果 - 与原模块完全一致
            stdout, stderr = process.communicate()

            # 合并所有stderr输出
            all_stderr = '\n'.join(stderr_lines) + '\n' + (stderr or '')

            if process.returncode == 0:
                self.progress_updated.emit(100)
                self.logger.info("FFmpeg执行成功")
                return True
            else:
                self.logger.error(f"FFmpeg执行失败: {all_stderr}")
                return False

        except Exception as e:
            self.logger.error(f"执行FFmpeg命令失败: {e}")
            return False

    def _parse_ffmpeg_progress(self, stderr_lines: list) -> float:
        """
        🚀 修复：改进FFmpeg进度解析，提高准确性
        返回0-100的进度百分比，如果无法解析返回None
        """
        try:
            import re

            # 🚀 修复：使用更灵活的正则表达式
            # 支持不同的时间格式：HH:MM:SS.ss 或 MM:SS.ss 或 SS.ss
            time_patterns = [
                r'time=(\d{1,2}):(\d{2}):(\d{2}\.?\d*)',  # HH:MM:SS.ss
                r'time=(\d{1,2}):(\d{2}\.?\d*)',          # MM:SS.ss
                r'time=(\d+\.?\d*)',                      # SS.ss
            ]

            duration_patterns = [
                r'Duration: (\d{1,2}):(\d{2}):(\d{2}\.?\d*)',  # HH:MM:SS.ss
                r'Duration: (\d{1,2}):(\d{2}\.?\d*)',          # MM:SS.ss
            ]

            total_seconds = None
            current_seconds = None

            # 🚀 修复：扩大搜索范围，从更多行中查找信息
            recent_lines = stderr_lines[-20:] if len(stderr_lines) > 20 else stderr_lines

            # 首先查找总时长（通常在开头）
            for line in stderr_lines:
                if total_seconds is None:
                    for pattern in duration_patterns:
                        duration_match = re.search(pattern, line)
                        if duration_match:
                            groups = duration_match.groups()
                            if len(groups) == 3:  # HH:MM:SS.ss
                                h, m, s = groups
                                total_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                            elif len(groups) == 2:  # MM:SS.ss
                                m, s = groups
                                total_seconds = int(m) * 60 + float(s)
                            break
                if total_seconds:
                    break

            # 然后查找当前时间（从最新的行开始）
            for line in reversed(recent_lines):
                if current_seconds is None:
                    for i, pattern in enumerate(time_patterns):
                        time_match = re.search(pattern, line)
                        if time_match:
                            groups = time_match.groups()
                            if i == 0 and len(groups) == 3:  # HH:MM:SS.ss
                                h, m, s = groups
                                current_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                            elif i == 1 and len(groups) == 2:  # MM:SS.ss
                                m, s = groups
                                current_seconds = int(m) * 60 + float(s)
                            elif i == 2 and len(groups) == 1:  # SS.ss
                                s = groups[0]
                                current_seconds = float(s)
                            break
                if current_seconds is not None:
                    break

            # 🚀 修复：添加更多调试信息
            if total_seconds is not None and current_seconds is not None:
                self.logger.debug(f"FFmpeg进度: {current_seconds:.1f}s / {total_seconds:.1f}s")

                # 计算进度百分比
                if total_seconds > 0:
                    progress = (current_seconds / total_seconds) * 100
                    # 🚀 修复：确保进度在合理范围内
                    progress = max(0, min(progress, 99))  # 限制在0-99%
                    return progress

            return None

        except Exception as e:
            self.logger.debug(f"解析FFmpeg进度失败: {e}")
            return None

    def _get_smart_scale_filter(self, src_width: int, src_height: int,
                               target_width: int, target_height: int) -> str:
        """
        获取智能缩放滤镜
        检测视频比例，如果与目标比例不匹配，使用黑边填充或裁剪
        """
        try:
            # 计算原始和目标比例
            src_ratio = src_width / src_height
            target_ratio = target_width / target_height

            self.logger.info(f"视频比例分析: 原始={src_width}x{src_height} (比例:{src_ratio:.3f}), "
                           f"目标={target_width}x{target_height} (比例:{target_ratio:.3f})")

            # 比例差异阈值（5%以内认为相同）
            ratio_threshold = 0.05
            ratio_diff = abs(src_ratio - target_ratio) / target_ratio

            if ratio_diff <= ratio_threshold:
                # 比例相近，直接缩放
                self.logger.info("视频比例相近，使用直接缩放")
                return f"scale={target_width}:{target_height}"

            # 🚀 修复：对所有比例都应用智能处理，不限制于9:16和16:9
            self.logger.info("应用智能比例处理")
            return self._get_aspect_ratio_filter(
                src_width, src_height, target_width, target_height,
                src_ratio, target_ratio
            )

        except Exception as e:
            self.logger.error(f"生成智能缩放滤镜失败: {e}")
            # 回退到简单缩放
            return f"scale={target_width}:{target_height}"

    def _get_aspect_ratio_filter(self, src_width: int, src_height: int,
                                target_width: int, target_height: int,
                                src_ratio: float, target_ratio: float) -> str:
        """
        获取比例处理滤镜（支持所有比例转换）
        """
        try:
            # 🚀 修复：检测正方形视频，使用黑边填充
            is_square = abs(src_ratio - 1.0) < 0.05  # 正方形阈值

            if is_square:
                self.logger.info("正方形视频：使用黑边填充保持完整内容")
                return f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"

            # 🚀 修复：计算比例差异，决定处理策略
            ratio_diff_threshold = 0.15  # 15%差异阈值，更严格的判断
            ratio_diff = abs(src_ratio - target_ratio) / max(src_ratio, target_ratio)

            if ratio_diff < ratio_diff_threshold:
                # 比例差异很小，使用保持比例缩放+黑边填充
                self.logger.info(f"比例差异很小({ratio_diff:.2f})：使用保持比例缩放+黑边填充")
                return f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"

            # 🚀 修复：比例差异较大，使用智能裁剪
            if src_ratio > target_ratio:
                # 原视频更宽，裁剪左右部分
                self.logger.info(f"宽视频转窄比例：裁剪左右部分 (原始比例:{src_ratio:.3f} → 目标比例:{target_ratio:.3f})")
                crop_width = int(src_height * target_ratio)
                crop_x = max(0, (src_width - crop_width) // 2)
                return f"crop={crop_width}:{src_height}:{crop_x}:0,scale={target_width}:{target_height}"
            else:
                # 原视频更高，裁剪上下部分
                self.logger.info(f"高视频转宽比例：裁剪上下部分 (原始比例:{src_ratio:.3f} → 目标比例:{target_ratio:.3f})")
                crop_height = int(src_width / target_ratio)
                crop_y = max(0, (src_height - crop_height) // 2)
                return f"crop={src_width}:{crop_height}:0:{crop_y},scale={target_width}:{target_height}"

        except Exception as e:
            self.logger.error(f"生成比例处理滤镜失败: {e}")
            # 回退到保持比例缩放+黑边
            return f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black"

















class ExportWorkerManager:
    """
    导出工作线程管理器
    🔒 保证：与原模块的导出逻辑完全一致
    """

    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
        self.current_export_thread = None

    @error_handler(show_dialog=True)
    def export_video(self, batch_output_path=None):
        """
        导出视频 - 支持批量处理模式
        🔒 保证：与原模块的export_video方法行为完全一致，同时支持批量处理
        """
        try:
            self.logger.info("🚀 ExportWorkerManager.export_video被调用")
            self.logger.info(f"📁 batch_output_path: {batch_output_path}")
            self.logger.info(f"🎭 批量处理模式: {'是' if batch_output_path else '否'}")

            # 🔒 检查视频是否已加载 - 与原模块完全一致
            if not hasattr(self.parent, 'video_cap') or not self.parent.video_cap or not self.parent.video_cap.isOpened():
                self.logger.warning("没有视频文件，退出导出")
                QMessageBox.warning(self.parent, "警告", "请先加载视频文件")
                return

            # 🔒 检查是否有封面 - 与原模块完全一致
            cover_image = None
            if hasattr(self.parent, 'cover_layers') and self.parent.cover_layers:
                # 创建合成封面
                if hasattr(self.parent, 'image_handler'):
                    cover_image = self.parent.image_handler.create_composite_image()

            if not cover_image:
                if batch_output_path:
                    # 批量处理模式：没有封面时跳过
                    self.logger.warning("批量处理模式：没有封面，跳过导出")
                    return
                else:
                    # 手动模式：询问用户
                    reply = QMessageBox.question(
                        self.parent, "确认",
                        "没有封面图片，是否继续导出？",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )
                    if reply == QMessageBox.StandardButton.No:
                        return

            # 🔧 修复：批量处理模式下使用预设的输出路径
            if batch_output_path:
                # 批量处理模式：使用传入的输出路径
                output_path = batch_output_path
                self.logger.info(f"✅ 批量处理模式，使用预设输出路径: {output_path}")
            else:
                # 手动导出模式：弹出文件选择对话框
                self.logger.info("🎭 手动导出模式，弹出文件选择对话框")
                output_path, _ = QFileDialog.getSaveFileName(
                    self.parent,
                    "保存视频文件",
                    "",
                    "MP4文件 (*.mp4);;AVI文件 (*.avi);;所有文件 (*)"
                )

                if not output_path:
                    return

            # 🔒 获取导出参数 - 与原模块完全一致
            start_frame = getattr(self.parent, 'cropped_start_frames', 0)
            end_frame = getattr(self.parent, 'total_frames', 0) - getattr(self.parent, 'cropped_end_frames', 0)
            fps = getattr(self.parent, 'fps', 30.0)

            # 🔒 查找FFmpeg - 与原模块完全一致
            ffmpeg_path = self._find_ffmpeg_path()
            if not ffmpeg_path:
                QMessageBox.critical(self.parent, "错误", "未找到FFmpeg，无法导出视频")
                return

            # 🔒 禁用导出按钮 - 与原模块完全一致
            if hasattr(self.parent, 'export_btn'):
                self.parent.export_btn.setEnabled(False)
                self.parent.export_btn.setText("导出中...")

            # 🔒 创建并启动导出线程 - 与原模块完全一致
            self.current_export_thread = VideoExportWorker(
                video_path=self.parent.video_path,
                output_path=output_path,
                cover_image=cover_image,
                fps=fps,
                start_frame=start_frame,
                end_frame=end_frame,
                ffmpeg_path=ffmpeg_path,
                export_settings=getattr(self.parent, 'export_settings', {}),
                encoder='libx264'
            )

            # 🔒 连接信号 - 与原模块完全一致
            self.current_export_thread.progress_updated.connect(self._on_progress_updated)
            self.current_export_thread.export_finished.connect(self._on_export_finished)

            # 🔒 启动线程 - 与原模块完全一致
            self.current_export_thread.start()

        except Exception as e:
            self.logger.error(f"启动导出失败: {e}")
            QMessageBox.critical(self.parent, CoverEditConstants.MSG_ERROR, f"启动导出失败: {str(e)}")
            self._reset_export_ui()

    def _find_ffmpeg_path(self) -> str:
        """查找FFmpeg路径"""
        # 🔒 检查bin目录 - 与原模块完全一致
        bin_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'bin', 'ffmpeg.exe')
        if os.path.exists(bin_path):
            return bin_path

        # 🔒 检查系统PATH - 与原模块完全一致
        import subprocess
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=5)
            if result.returncode == 0:
                return 'ffmpeg'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass

        return ""

    def _on_progress_updated(self, progress: int):
        """处理进度更新"""
        if hasattr(self.parent, 'export_progress'):
            self.parent.export_progress.setValue(progress)
            self.parent.export_progress.setVisible(True)

    def _on_export_finished(self, output_path: str, success: bool, error_msg: str):
        """处理导出完成"""
        try:
            self._reset_export_ui()

            if success:
                self.logger.info(f"视频导出成功: {output_path}")
                QMessageBox.information(self.parent, "成功", f"视频导出完成！\n保存位置: {output_path}")
            else:
                self.logger.error(f"视频导出失败: {error_msg}")
                QMessageBox.critical(self.parent, "错误", f"视频导出失败！\n错误信息: {error_msg}")

        except Exception as e:
            self.logger.error(f"处理导出完成失败: {e}")

    def _reset_export_ui(self):
        """重置导出UI状态"""
        try:
            if hasattr(self.parent, 'export_btn'):
                self.parent.export_btn.setEnabled(True)
                self.parent.export_btn.setText("导出视频")

            if hasattr(self.parent, 'export_progress'):
                self.parent.export_progress.setVisible(False)

        except Exception as e:
            self.logger.error(f"重置导出UI失败: {e}")
