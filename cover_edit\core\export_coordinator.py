#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出协调器 - 统一管理所有导出逻辑
解决重复实现和逻辑冲突问题
"""

import os
import tempfile
from typing import Optional, Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtWidgets import QMessageBox, QFileDialog
from PyQt6.QtGui import QPixmap

from cover_edit.utils.logger import get_logger
from cover_edit.utils.decorators import error_handler, performance_monitor
from cover_edit.workers.export_worker import VideoExportWorker


class ExportCoordinator(QObject):
    """导出协调器 - 统一管理导出流程"""
    
    # 信号定义
    export_started = pyqtSignal(str)  # 导出路径
    export_progress = pyqtSignal(int)  # 进度百分比
    export_finished = pyqtSignal(bool, str, str)  # 成功, 路径, 消息
    
    def __init__(self, parent_widget):
        super().__init__()
        self.parent = parent_widget
        self.logger = get_logger("ExportCoordinator")
        
        # 导出状态
        self.is_exporting = False
        self.current_export_worker = None
        self.is_batch_mode = False  # 🚀 新增：批量模式标志
        
        # 导出配置
        self.export_config = {
            'default_quality': 'high',
            'default_encoder': 'libx264',
            'temp_dir': tempfile.gettempdir()
        }
    
    @error_handler(show_dialog=True)
    @performance_monitor(threshold_ms=100.0)
    def export_video(self, batch_output_path: Optional[str] = None) -> bool:
        """
        统一的视频导出入口

        Args:
            batch_output_path: 批量处理时的输出路径

        Returns:
            是否成功启动导出
        """
        try:
            # 🚀 修复：检测是否为批量处理模式
            is_batch_mode = batch_output_path is not None
            self.is_batch_mode = is_batch_mode  # 保存批量模式状态

            # 检查导出状态
            if self.is_exporting:
                self.logger.warning("已有导出任务在进行中")
                if not is_batch_mode:  # 只在非批量模式下显示对话框
                    QMessageBox.warning(self.parent, "警告", "已有导出任务在进行中，请等待完成")
                return False
            
            # 验证前置条件
            if not self._validate_export_conditions(is_batch_mode):
                return False
            
            # 获取导出路径
            output_path = self._get_output_path(batch_output_path)
            if not output_path:
                return False

            # 准备导出参数
            export_params = self._prepare_export_params(output_path, is_batch_mode)
            if not export_params:
                return False
            
            # 启动导出
            return self._start_export(export_params)
            
        except Exception as e:
            self.logger.error(f"导出视频失败: {e}")
            if not getattr(self, 'is_batch_mode', False):
                QMessageBox.critical(self.parent, "错误", f"导出失败: {str(e)}")
            return False
    
    def _validate_export_conditions(self, is_batch_mode: bool = False) -> bool:
        """验证导出前置条件"""
        # 检查视频是否已加载
        if not hasattr(self.parent, 'video_cap') or not self.parent.video_cap:
            self.logger.warning("视频文件未加载")
            if not is_batch_mode:
                QMessageBox.warning(self.parent, "警告", "请先加载视频文件")
            return False

        if not self.parent.video_cap.isOpened():
            self.logger.warning("视频文件未正确加载")
            if not is_batch_mode:
                QMessageBox.warning(self.parent, "警告", "视频文件未正确加载")
            return False

        # 检查是否有封面图层
        if not hasattr(self.parent, 'cover_layers') or not self.parent.cover_layers:
            self.logger.warning("未添加封面图片")
            if not is_batch_mode:
                QMessageBox.warning(self.parent, "警告", "请先添加封面图片")
            return False
        
        return True
    
    def _get_output_path(self, batch_output_path: Optional[str]) -> Optional[str]:
        """获取输出路径"""
        if batch_output_path:
            # 批量处理模式
            return batch_output_path
        
        # 交互模式 - 让用户选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self.parent,
            "保存视频",
            "",
            "视频文件 (*.mp4);;所有文件 (*)"
        )
        
        if file_path and not file_path.endswith('.mp4'):
            file_path += '.mp4'
        
        return file_path
    
    def _prepare_export_params(self, output_path: str, is_batch_mode: bool = False) -> Optional[Dict[str, Any]]:
        """准备导出参数"""
        try:
            # 获取视频信息
            video_path = getattr(self.parent, 'video_path', '')
            if not video_path:
                self.logger.error("视频路径为空")
                return None
            
            # 获取帧范围
            start_frame = getattr(self.parent, 'cropped_start_frames', 0)
            end_frame = getattr(self.parent, 'total_frames', 0) - getattr(self.parent, 'cropped_end_frames', 0)
            
            # 获取帧率
            fps = getattr(self.parent, 'fps', 30.0)
            
            # 获取封面图像
            cover_image = self._get_composite_cover()
            if not cover_image:
                self.logger.error("无法获取封面图像")
                return None
            
            # 获取FFmpeg路径
            from ..utils.path_manager import get_ffmpeg_path
            ffmpeg_path = get_ffmpeg_path()
            if not ffmpeg_path:
                self.logger.error("未找到FFmpeg，无法导出视频")
                if not is_batch_mode:
                    QMessageBox.warning(self.parent, "警告", "未找到FFmpeg，无法导出视频")
                return None
            
            return {
                'video_path': video_path,
                'output_path': output_path,
                'cover_image': cover_image,
                'fps': fps,
                'start_frame': start_frame,
                'end_frame': end_frame,
                'ffmpeg_path': ffmpeg_path,
                'export_settings': self._get_export_settings_dict(),
                'encoder': self.export_config['default_encoder'],
                'video_width': getattr(self.parent, 'video_width', 1920),
                'video_height': getattr(self.parent, 'video_height', 1080)
            }
            
        except Exception as e:
            self.logger.error(f"准备导出参数失败: {e}")
            return None

    def _get_export_settings_dict(self) -> dict:
        """获取导出设置字典"""
        try:
            # 🚀 修复：正确获取导出设置
            if hasattr(self.parent, 'export_settings') and self.parent.export_settings:
                if hasattr(self.parent.export_settings, 'get_settings'):
                    settings = self.parent.export_settings.get_settings()
                    self.logger.info(f"从导出设置模块获取设置: GPU={settings.get('use_gpu', True)}")
                    return settings
                elif isinstance(self.parent.export_settings, dict):
                    self.logger.info(f"使用字典形式的导出设置")
                    return self.parent.export_settings

            # 🚀 修复：使用父模块的导出设置获取方法
            if hasattr(self.parent, '_get_export_settings'):
                settings = self.parent._get_export_settings()
                self.logger.info(f"从父模块获取导出设置: GPU={settings.get('use_gpu', True)}")
                return settings

            # 默认设置
            default_settings = {
                'use_gpu': True,  # 默认启用GPU
                'quality': 'high',
                'width': 1440,
                'height': 2560,
                'fps': 30.0,
                'target_bitrate': 8000,
                'preset': 'medium',
                'audio_codec': 'AAC',
                'audio_bitrate': 128
            }
            self.logger.warning(f"使用默认导出设置: {default_settings}")
            return default_settings

        except Exception as e:
            self.logger.error(f"获取导出设置失败: {e}")
            return {'use_gpu': True, 'quality': 'high', 'width': 1440, 'height': 2560, 'fps': 30.0}
    
    def _get_composite_cover(self) -> Optional[QPixmap]:
        """获取合成封面图像"""
        try:
            if hasattr(self.parent, 'cover_layers') and self.parent.cover_layers:
                # 获取最新的可见图层
                for layer in reversed(self.parent.cover_layers):
                    if layer.get('visible', True) and layer.get('pixmap'):
                        return layer['pixmap']
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取合成封面失败: {e}")
            return None
    
    def _start_export(self, export_params: Dict[str, Any]) -> bool:
        """启动导出"""
        try:
            # 设置导出状态
            self.is_exporting = True
            
            # 更新UI状态
            self._update_export_ui(True)
            
            # 创建导出工作线程
            self.current_export_worker = VideoExportWorker(**export_params)
            
            # 连接信号
            self.current_export_worker.progress_updated.connect(self._on_progress_updated)
            self.current_export_worker.export_finished.connect(self._on_export_finished)
            
            # 启动导出
            self.current_export_worker.start()
            
            # 发送开始信号
            self.export_started.emit(export_params['output_path'])
            
            self.logger.info(f"开始导出视频: {export_params['output_path']}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动导出失败: {e}")
            self.is_exporting = False
            self._update_export_ui(False)
            return False
    
    def _update_export_ui(self, exporting: bool):
        """更新导出相关的UI状态"""
        try:
            if hasattr(self.parent, 'export_btn'):
                self.parent.export_btn.setEnabled(not exporting)
                self.parent.export_btn.setText("导出中..." if exporting else "导出视频")
            
            if hasattr(self.parent, 'export_progress'):
                self.parent.export_progress.setVisible(exporting)
                if not exporting:
                    self.parent.export_progress.setValue(0)
                    
        except Exception as e:
            self.logger.error(f"更新导出UI失败: {e}")
    
    def _on_progress_updated(self, progress: int):
        """处理进度更新"""
        try:
            if hasattr(self.parent, 'export_progress'):
                self.parent.export_progress.setValue(progress)
            
            self.export_progress.emit(progress)
            self.logger.debug(f"导出进度: {progress}%")
            
        except Exception as e:
            self.logger.error(f"更新导出进度失败: {e}")
    
    def _on_export_finished(self, output_path: str, success: bool, message: str):
        """处理导出完成"""
        try:
            # 重置状态
            self.is_exporting = False
            self.current_export_worker = None
            
            # 更新UI
            self._update_export_ui(False)
            
            # 显示结果（批量模式下不显示对话框）
            if success:
                self.logger.info(f"视频导出成功: {output_path}")
                if not self.is_batch_mode:
                    QMessageBox.information(self.parent, "成功", f"视频导出完成!\n保存位置: {output_path}")
            else:
                self.logger.error(f"视频导出失败: {message}")
                if not self.is_batch_mode:
                    QMessageBox.critical(self.parent, "失败", f"视频导出失败: {message}")
            
            # 发送完成信号
            self.export_finished.emit(success, output_path, message)
            
        except Exception as e:
            self.logger.error(f"处理导出完成失败: {e}")
    
    def cancel_export(self) -> bool:
        """取消当前导出"""
        try:
            if not self.is_exporting or not self.current_export_worker:
                return False
            
            # 终止工作线程
            self.current_export_worker.terminate()
            self.current_export_worker.wait(3000)  # 等待3秒
            
            # 重置状态
            self.is_exporting = False
            self.current_export_worker = None
            
            # 更新UI
            self._update_export_ui(False)
            
            self.logger.info("导出已取消")
            return True
            
        except Exception as e:
            self.logger.error(f"取消导出失败: {e}")
            return False
