#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 模块常量定义
将原本散布在代码中的硬编码数字提取为常量
"""

class CoverEditConstants:
    """封面编辑模块常量定义"""
    
    # ============================================================================
    # UI 相关常量
    # ============================================================================
    
    # 窗口和面板尺寸
    MIN_WINDOW_WIDTH = 1200
    MIN_WINDOW_HEIGHT = 800
    MIN_PREVIEW_WIDTH = 400
    MIN_PREVIEW_HEIGHT = 300
    MAX_PREVIEW_WIDTH = 800
    MAX_PREVIEW_HEIGHT = 600

    # 🚀 修复：添加兼容性常量
    MIN_PREVIEW_SIZE = (MIN_PREVIEW_WIDTH, MIN_PREVIEW_HEIGHT)
    MAX_PREVIEW_SIZE = (MAX_PREVIEW_WIDTH, MAX_PREVIEW_HEIGHT)
    
    # 分割器比例
    DEFAULT_SPLITTER_RATIO = [500, 500]  # 左右面板1:1分配
    
    # 控件尺寸
    BUTTON_MIN_WIDTH = 120
    BUTTON_MIN_HEIGHT = 45
    SLIDER_MIN_WIDTH = 200
    INPUT_MIN_HEIGHT = 30
    
    # ============================================================================
    # 视频相关常量
    # ============================================================================
    
    # 视频参数
    DEFAULT_FPS = 30.0
    MIN_FPS = 1.0
    MAX_FPS = 120.0
    MAX_VIDEO_DURATION = 3600  # 最大支持1小时视频
    
    # 默认裁剪设置 - 与原版完全一致
    DEFAULT_CROP_START = 15    # 默认裁剪开头15帧
    DEFAULT_CROP_END = 10      # 默认裁剪结尾10帧
    
    # 视频分辨率
    DEFAULT_VIDEO_WIDTH = 1440
    DEFAULT_VIDEO_HEIGHT = 2560
    DEFAULT_ASPECT_RATIO = 9/16  # 竖屏比例
    DEFAULT_VIDEO_SIZE = (DEFAULT_VIDEO_WIDTH, DEFAULT_VIDEO_HEIGHT)
    
    # 支持的视频格式
    SUPPORTED_VIDEO_FORMATS = [
        '.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp',
        '.3g2', '.webm', '.ogv', '.ogg', '.mpg', '.mpeg', '.m2v', '.m4p',
        '.m4b', '.f4v', '.f4p', '.f4a', '.f4b', '.vob', '.lrv', '.mxf',
        '.roq', '.nsv', '.amv', '.rm', '.rmvb', '.asf', '.ts', '.mts', '.m2ts'
    ]

    # 🚀 修复：兼容性别名
    VIDEO_EXTENSIONS = SUPPORTED_VIDEO_FORMATS
    
    # ============================================================================
    # 图像相关常量
    # ============================================================================
    
    # 支持的图像格式
    SUPPORTED_IMAGE_FORMATS = [
        '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp'
    ]

    # 🚀 修复：兼容性别名
    IMAGE_EXTENSIONS = SUPPORTED_IMAGE_FORMATS
    
    # 图像处理参数
    MAX_IMAGE_WIDTH = 4096
    MAX_IMAGE_HEIGHT = 4096
    IMAGE_QUALITY = 95  # JPEG质量
    PNG_COMPRESSION = 6  # PNG压缩级别
    
    # ============================================================================
    # 文字相关常量
    # ============================================================================
    
    # 字体参数
    DEFAULT_FONT_NAME = "微软雅黑"
    DEFAULT_FONT_SIZE = 24
    MIN_FONT_SIZE = 12
    MAX_FONT_SIZE = 100
    
    # 文字样式
    DEFAULT_STROKE_WIDTH = 2
    MIN_STROKE_WIDTH = 0
    MAX_STROKE_WIDTH = 10
    DEFAULT_BG_OPACITY = 50
    MIN_BG_OPACITY = 0
    MAX_BG_OPACITY = 100
    
    # 默认颜色 (RGB)
    DEFAULT_FILL_COLOR = (255, 255, 255)    # 白色
    DEFAULT_STROKE_COLOR = (0, 0, 0)        # 黑色
    DEFAULT_BG_COLOR = (0, 0, 0)            # 黑色背景
    
    # ============================================================================
    # 文件相关常量
    # ============================================================================
    
    # 文件大小限制
    MIN_FILE_SIZE = 100 * 1024      # 100KB
    MAX_FILE_SIZE = 2 * 1024**3     # 2GB
    
    # 文件检查参数
    FILE_CHECK_TIMEOUT = 5.0        # 文件检查超时时间(秒)
    FILE_READY_CHECK_SIZE = 1024    # 文件就绪检查读取字节数
    
    # ============================================================================
    # 批量处理常量
    # ============================================================================
    
    # 批量处理限制
    MAX_BATCH_SIZE = 50             # 最大批量处理文件数
    BATCH_CHECK_INTERVAL = 2000     # 批量检查间隔(毫秒)
    PROCESSING_TIMEOUT = 300        # 单个文件处理超时(秒)
    
    # 文件监控参数
    WATCH_FOLDER_CHECK_INTERVAL = 1000  # 文件夹监控间隔(毫秒)
    FILE_STABLE_TIME = 3.0              # 文件稳定时间(秒)
    
    # ============================================================================
    # 导出相关常量
    # ============================================================================
    
    # 导出格式
    DEFAULT_OUTPUT_FORMAT = 'MP4'
    SUPPORTED_OUTPUT_FORMATS = ['MP4', 'AVI', 'MOV', 'MKV']
    
    # 编码器
    DEFAULT_VIDEO_CODEC = 'H.264'
    SUPPORTED_VIDEO_CODECS = ['H.264', 'H.265', 'VP9', 'AV1']
    
    # 质量预设
    DEFAULT_QUALITY_PRESET = '高质量'
    QUALITY_PRESETS = {
        '低质量': {'crf': 28, 'preset': 'fast'},
        '中等质量': {'crf': 23, 'preset': 'medium'},
        '高质量': {'crf': 18, 'preset': 'slow'},
        '最高质量': {'crf': 15, 'preset': 'veryslow'}
    }
    
    # 码率设置
    DEFAULT_BITRATE = 8000          # kbps
    MIN_BITRATE = 500               # kbps
    MAX_BITRATE = 50000             # kbps
    
    # ============================================================================
    # 性能相关常量
    # ============================================================================
    
    # 内存管理
    MAX_CACHE_SIZE = 100            # 最大缓存项目数
    MEMORY_WARNING_THRESHOLD = 500  # 内存警告阈值(MB)
    MEMORY_CRITICAL_THRESHOLD = 1000 # 内存严重警告阈值(MB)
    
    # 性能监控
    PERFORMANCE_WARNING_THRESHOLD = 100.0  # 性能警告阈值(毫秒)
    PERFORMANCE_CRITICAL_THRESHOLD = 500.0 # 性能严重警告阈值(毫秒)
    
    # 线程池设置
    MAX_WORKER_THREADS = 4          # 最大工作线程数
    THREAD_TIMEOUT = 30.0           # 线程超时时间(秒)
    
    # ============================================================================
    # 样式相关常量
    # ============================================================================
    
    # 主题颜色
    THEME_COLORS = {
        'background': '#181A1F',
        'text': '#D0D0D0',
        'border': '#303238',
        'hover': '#2C313A',
        'preview_bg': '#2b2b2b',
        'button_bg': '#3A3D44',
        'input_bg': '#1E2025'
    }
    
    # 按钮颜色
    BUTTON_COLORS = {
        'video_split': '#61AFEF',      # 蓝色
        'video_transcode': '#98C379',  # 绿色
        'cover_edit': '#D19A66',       # 橙色
        'audio_extract': '#C678DD'     # 紫色
    }
    
    # ============================================================================
    # 样式定义
    # ============================================================================
    
    # 预览区域样式
    PREVIEW_STYLE = """
        QLabel {
            border: 2px solid #555;
            background-color: #2b2b2b;
            color: white;
            font-size: 14px;
            text-align: center;
        }
    """
    
    # 分割器样式
    SPLITTER_STYLE = """
        QSplitter::handle {
            background-color: #303238;
            width: 2px;
        }
        QSplitter::handle:hover {
            background-color: #61AFEF;
        }
    """

    # 视频控件样式
    VIDEO_WIDGET_STYLE = """
        QVideoWidget {
            background-color: #181A1F;
            border: 2px dashed #5C6370;
            border-radius: 6px;
        }
    """
    
    # 按钮样式模板
    BUTTON_STYLE_TEMPLATE = """
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 16px;
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: {pressed_color};
        }}
        QPushButton:disabled {{
            background-color: #555;
            color: #888;
        }}
    """
    
    # ============================================================================
    # 消息常量 - 兼容性
    # ============================================================================

    # 🚀 修复：添加兼容性消息常量
    MSG_ERROR = "错误"
    MSG_WARNING = "警告"
    MSG_SUCCESS = "成功"
    MSG_INFO = "信息"

    # ============================================================================
    # 错误消息常量
    # ============================================================================

    # 错误消息
    ERROR_MESSAGES = {
        'video_load_failed': '视频加载失败',
        'image_load_failed': '图像加载失败',
        'export_failed': '视频导出失败',
        'file_not_found': '文件不存在',
        'invalid_format': '不支持的文件格式',
        'insufficient_memory': '内存不足',
        'processing_timeout': '处理超时',
        'permission_denied': '权限不足'
    }
    
    # 成功消息
    SUCCESS_MESSAGES = {
        'video_loaded': '视频加载成功',
        'image_loaded': '图像加载成功',
        'export_completed': '视频导出完成',
        'batch_completed': '批量处理完成'
    }
    
    # 信息消息
    INFO_MESSAGES = {
        'processing': '正在处理...',
        'waiting': '等待中...',
        'ready': '就绪',
        'cancelled': '已取消'
    }

# ============================================================================
# 便捷访问函数
# ============================================================================

def get_theme_color(color_name: str) -> str:
    """获取主题颜色"""
    return CoverEditConstants.THEME_COLORS.get(color_name, '#FFFFFF')

def get_button_color(button_name: str) -> str:
    """获取按钮颜色"""
    return CoverEditConstants.BUTTON_COLORS.get(button_name, '#3A3D44')

def is_supported_video_format(file_path: str) -> bool:
    """检查是否为支持的视频格式"""
    import os
    _, ext = os.path.splitext(file_path.lower())
    return ext in CoverEditConstants.SUPPORTED_VIDEO_FORMATS

def is_supported_image_format(file_path: str) -> bool:
    """检查是否为支持的图像格式"""
    import os
    _, ext = os.path.splitext(file_path.lower())
    return ext in CoverEditConstants.SUPPORTED_IMAGE_FORMATS

def get_error_message(error_key: str) -> str:
    """获取错误消息"""
    return CoverEditConstants.ERROR_MESSAGES.get(error_key, '未知错误')

def get_success_message(success_key: str) -> str:
    """获取成功消息"""
    return CoverEditConstants.SUCCESS_MESSAGES.get(success_key, '操作成功')
