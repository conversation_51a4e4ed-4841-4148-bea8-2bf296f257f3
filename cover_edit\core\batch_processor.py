#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全新的简洁批量处理器
重新设计，简单易维护
"""

import os
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from PyQt6.QtCore import QTimer, QFileSystemWatcher, pyqtSignal, QObject

from ..utils.logger import get_logger
from ..utils.decorators import error_handler


class SimpleBatchProcessor(QObject):
    """
    简洁的批量处理器
    
    功能：
    1. 监控指定文件夹
    2. 发现新的视频文件时自动处理
    3. 处理完成后导出到指定文件夹
    """
    
    # 信号定义
    file_found = pyqtSignal(str)  # 发现新文件
    processing_started = pyqtSignal(str)  # 开始处理文件
    processing_finished = pyqtSignal(str, bool)  # 处理完成 (文件路径, 是否成功)
    batch_status_changed = pyqtSignal(bool)  # 批量处理状态改变
    log_message = pyqtSignal(str, str)  # 日志消息 (消息, 类型)
    
    def __init__(self, parent_widget):
        super().__init__()
        self.parent = parent_widget
        self.logger = get_logger("SimpleBatchProcessor")
        
        # 批量处理状态
        self.is_running = False
        self.watch_folder = ""
        self.output_folder = ""
        
        # 文件监控器
        self.file_watcher = None
        
        # 处理队列
        self.processing_queue = []
        self.current_processing_file = None

        # 🚀 新增：导出信号连接标志
        self._export_signals_connected = False

        # 🚀 新增：处理记录管理
        self.processed_files = set()  # 已处理的文件
        self.failed_files = set()     # 处理失败的文件
        self.processing_record_file = None  # 处理记录文件路径

        # 🚀 修复：重置模式标志，重置后的处理不记录到已处理列表
        self._reset_mode = False
        
        # 🚀 修复：支持的视频格式，添加ts格式
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.ts', '.m2ts', '.mts']
        
        # 处理定时器（避免频繁处理）
        self.process_timer = QTimer()
        self.process_timer.setSingleShot(True)
        self.process_timer.timeout.connect(self._process_next_file)

        # 🚀 修复：新文件扫描定时器
        self.scan_timer = QTimer()
        self.scan_timer.setSingleShot(True)
        self.scan_timer.timeout.connect(self._scan_new_files)
        
        self.logger.info("简洁批量处理器初始化完成")
    
    @error_handler(show_dialog=False)
    def start_batch_processing(self, watch_folder: str, output_folder: str) -> bool:
        """
        开始批量处理
        
        Args:
            watch_folder: 监控文件夹路径
            output_folder: 输出文件夹路径
            
        Returns:
            是否成功启动
        """
        try:
            # 验证文件夹
            if not os.path.exists(watch_folder):
                self.logger.error(f"监控文件夹不存在: {watch_folder}")
                return False
                
            if not os.path.exists(output_folder):
                self.logger.error(f"输出文件夹不存在: {output_folder}")
                return False
            
            # 保存设置
            self.watch_folder = watch_folder
            self.output_folder = output_folder

            # 🚀 新增：初始化处理记录
            self._init_processing_record()
            
            # 更新状态（必须在扫描文件之前设置）
            self.is_running = True

            # 设置文件监控器
            self._setup_file_watcher()

            # 扫描现有文件
            self._scan_existing_files()

            # 发送状态变化信号
            self.batch_status_changed.emit(True)
            
            self.logger.info(f"批量处理已启动")
            self.logger.info(f"监控文件夹: {watch_folder}")
            self.logger.info(f"输出文件夹: {output_folder}")
            
            self.log_message.emit("🚀 批量处理已启动", "info")
            self.log_message.emit(f"📁 监控文件夹: {watch_folder}", "info")
            self.log_message.emit(f"📁 输出文件夹: {output_folder}", "info")
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动批量处理失败: {e}")
            return False
    
    @error_handler(show_dialog=False)
    def stop_batch_processing(self) -> bool:
        """停止批量处理"""
        try:
            # 停止文件监控
            if self.file_watcher:
                self.file_watcher.removePaths(self.file_watcher.files())
                self.file_watcher.removePaths(self.file_watcher.directories())
                self.file_watcher = None
            
            # 停止处理定时器
            self.process_timer.stop()

            # 🚀 修复：停止扫描定时器
            self.scan_timer.stop()
            
            # 清空队列
            self.processing_queue.clear()
            self.current_processing_file = None
            
            # 更新状态
            self.is_running = False
            self.batch_status_changed.emit(False)
            
            self.logger.info("批量处理已停止")
            self.log_message.emit("⏹️ 批量处理已停止", "info")
            
            return True
            
        except Exception as e:
            self.logger.error(f"停止批量处理失败: {e}")
            return False
    
    def _setup_file_watcher(self):
        """设置文件监控器"""
        try:
            if self.file_watcher:
                self.file_watcher.removePaths(self.file_watcher.files())
                self.file_watcher.removePaths(self.file_watcher.directories())
            
            self.file_watcher = QFileSystemWatcher()
            self.file_watcher.addPath(self.watch_folder)
            self.file_watcher.directoryChanged.connect(self._on_directory_changed)
            
            self.logger.debug(f"文件监控器已设置: {self.watch_folder}")
            
        except Exception as e:
            self.logger.error(f"设置文件监控器失败: {e}")
    
    def _scan_existing_files(self):
        """扫描现有文件"""
        try:
            video_files = []
            
            for file_path in Path(self.watch_folder).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    file_path_str = str(file_path)
                    # 🚀 修复：检查是否已处理过
                    if file_path_str not in self.processed_files:
                        video_files.append(file_path_str)
                    else:
                        self.logger.debug(f"跳过已处理文件: {os.path.basename(file_path_str)}")
            
            if video_files:
                self.logger.info(f"发现 {len(video_files)} 个待处理文件")
                self.log_message.emit(f"📋 发现 {len(video_files)} 个待处理文件", "info")
                
                for file_path in video_files:
                    self._add_to_queue(file_path)
                
                # 开始处理
                self._start_processing()
            else:
                self.logger.info("没有发现待处理文件")
                self.log_message.emit("📋 没有发现待处理文件", "info")
                
        except Exception as e:
            self.logger.error(f"扫描现有文件失败: {e}")
    
    def _on_directory_changed(self, path: str):
        """目录变化事件"""
        try:
            self.logger.debug(f"目录变化: {path}")

            # 🚀 修复：延迟扫描新文件，避免频繁触发
            self.scan_timer.start(2000)  # 2秒延迟扫描新文件

        except Exception as e:
            self.logger.error(f"处理目录变化事件失败: {e}")

    def _scan_new_files(self):
        """扫描新添加的文件"""
        try:
            if not self.is_running:
                return

            self.logger.debug("扫描新添加的文件...")

            new_files = []
            for file_path in Path(self.watch_folder).iterdir():
                if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                    file_path_str = str(file_path)

                    # 🚀 修复：检查是否为新文件（未处理且不在队列中）
                    if (file_path_str not in self.processed_files and
                        file_path_str not in self.processing_queue and
                        file_path_str != self.current_processing_file):
                        new_files.append(file_path_str)

            if new_files:
                self.logger.info(f"发现 {len(new_files)} 个新文件")
                self.log_message.emit(f"📁 发现 {len(new_files)} 个新文件", "info")

                # 添加到处理队列
                for file_path in new_files:
                    self._add_to_queue(file_path)

                # 如果当前没有在处理文件，开始处理
                if not self.current_processing_file:
                    self._start_processing()
            else:
                self.logger.debug("没有发现新文件")

        except Exception as e:
            self.logger.error(f"扫描新文件失败: {e}")

    def _add_to_queue(self, file_path: str):
        """添加文件到处理队列"""
        try:
            if file_path not in self.processing_queue and file_path != self.current_processing_file:
                self.processing_queue.append(file_path)
                filename = os.path.basename(file_path)
                self.logger.info(f"添加到处理队列: {filename}")
                self.file_found.emit(file_path)
                
        except Exception as e:
            self.logger.error(f"添加文件到队列失败: {e}")
    
    def _start_processing(self):
        """开始处理队列中的文件"""
        if not self.is_running or self.current_processing_file or not self.processing_queue:
            return
        
        # 开始处理下一个文件
        self._process_next_file()
    
    def _process_next_file(self):
        """处理下一个文件"""
        try:
            self.logger.debug(f"_process_next_file 被调用")
            self.logger.debug(f"is_running: {self.is_running}")
            self.logger.debug(f"current_processing_file: {self.current_processing_file}")
            self.logger.debug(f"queue_length: {len(self.processing_queue)}")

            if not self.is_running:
                self.logger.warning("批量处理未运行，跳过处理")
                return

            if self.current_processing_file:
                self.logger.warning(f"已有文件在处理中: {self.current_processing_file}")
                return

            if not self.processing_queue:
                self.logger.info("处理队列为空")
                return

            # 获取下一个文件
            file_path = self.processing_queue.pop(0)
            self.current_processing_file = file_path
            filename = os.path.basename(file_path)

            self.logger.info(f"开始处理: {filename}")
            self.log_message.emit(f"🎬 开始处理: {filename}", "processing")
            self.processing_started.emit(file_path)

            # 🚀 修复：异步处理文件，等待导出完成
            self._process_single_file_async(file_path)
            
        except Exception as e:
            self.logger.error(f"处理文件失败: {e}")
            if self.current_processing_file:
                self._finish_processing(self.current_processing_file, False)
    
    def _process_single_file_async(self, file_path: str):
        """
        异步处理单个文件
        启动处理后等待导出完成信号
        """
        try:
            # 生成输出路径
            output_path = self._generate_output_path(file_path)
            if not output_path:
                self._finish_processing(file_path, False)
                return

            # 调用父模块的加载方法
            if hasattr(self.parent, 'load_video'):
                self.logger.info(f"🎬 加载视频文件: {os.path.basename(file_path)}")
                self.parent.load_video(file_path)
            elif hasattr(self.parent, 'load_video_file'):
                self.logger.info(f"🎬 加载视频文件: {os.path.basename(file_path)}")
                self.parent.load_video_file(file_path)
            else:
                self.logger.warning("父模块没有视频加载方法")
                self._finish_processing(file_path, False)
                return

            # 连接导出完成信号（如果还没连接）
            self._connect_export_signals()

            # 调用导出方法
            if hasattr(self.parent, 'export_video'):
                self.logger.info(f"📤 开始导出到: {output_path}")
                success = self.parent.export_video(batch_output_path=output_path)
                if not success:
                    self.logger.error("导出启动失败")
                    self._finish_processing(file_path, False)
                else:
                    # 🚀 修复：如果没有导出完成信号，设置超时机制
                    if not self._export_signals_connected:
                        self.logger.info("没有导出完成信号，启用超时机制")
                        QTimer.singleShot(3000, lambda: self._check_export_timeout(file_path))
            elif hasattr(self.parent, 'export_worker_manager'):
                self.logger.info(f"📤 使用工作管理器导出到: {output_path}")
                success = self.parent.export_worker_manager.export_video(batch_output_path=output_path)
                if not success:
                    self.logger.error("导出启动失败")
                    self._finish_processing(file_path, False)
                else:
                    # 🚀 修复：如果没有导出完成信号，设置超时机制
                    if not self._export_signals_connected:
                        self.logger.info("没有导出完成信号，启用超时机制")
                        QTimer.singleShot(3000, lambda: self._check_export_timeout(file_path))
            else:
                self.logger.error("父模块没有导出方法")
                self._finish_processing(file_path, False)

        except Exception as e:
            self.logger.error(f"异步处理文件失败: {e}")
            self._finish_processing(file_path, False)

    def _connect_export_signals(self):
        """连接导出完成信号"""
        try:
            # 检查是否已经连接过
            if hasattr(self, '_export_signals_connected') and self._export_signals_connected:
                return

            # 连接导出协调器的信号
            if hasattr(self.parent, 'export_coordinator'):
                coordinator = self.parent.export_coordinator
                if hasattr(coordinator, 'export_finished'):
                    coordinator.export_finished.connect(self._on_export_finished)
                    self.logger.debug("已连接导出完成信号")
                    self._export_signals_connected = True
                    return

            # 连接导出工作管理器的信号
            if hasattr(self.parent, 'export_worker_manager'):
                manager = self.parent.export_worker_manager
                if hasattr(manager, 'export_finished'):
                    manager.export_finished.connect(self._on_export_finished)
                    self.logger.debug("已连接导出工作管理器信号")
                    self._export_signals_connected = True
                    return

            self.logger.warning("未找到导出完成信号")

        except Exception as e:
            self.logger.error(f"连接导出信号失败: {e}")

    def _on_export_finished(self, success: bool, output_path: str, message: str = ""):
        """处理导出完成信号"""
        try:
            if not self.current_processing_file:
                return

            self.logger.info(f"导出完成: {success}, 路径: {output_path}")

            # 完成当前文件处理
            self._finish_processing(self.current_processing_file, success)

        except Exception as e:
            self.logger.error(f"处理导出完成信号失败: {e}")

    def _check_export_timeout(self, file_path: str):
        """检查导出超时"""
        try:
            # 检查是否还是当前处理的文件
            if self.current_processing_file == file_path:
                self.logger.info(f"导出超时，假设完成: {os.path.basename(file_path)}")
                # 假设导出成功完成
                self._finish_processing(file_path, True)

        except Exception as e:
            self.logger.error(f"检查导出超时失败: {e}")

    def _init_processing_record(self):
        """初始化处理记录"""
        try:
            # 🚀 新增：处理记录文件路径
            import tempfile
            record_dir = Path(tempfile.gettempdir()) / "cover_edit_batch"
            record_dir.mkdir(exist_ok=True)

            # 基于监控文件夹生成唯一的记录文件名
            import hashlib
            folder_hash = hashlib.md5(self.watch_folder.encode()).hexdigest()[:8]
            self.processing_record_file = record_dir / f"batch_record_{folder_hash}.json"

            # 加载现有记录
            self._load_processing_record()

            self.logger.info(f"处理记录文件: {self.processing_record_file}")

        except Exception as e:
            self.logger.error(f"初始化处理记录失败: {e}")

    def _load_processing_record(self):
        """加载处理记录"""
        try:
            if self.processing_record_file and self.processing_record_file.exists():
                import json
                with open(self.processing_record_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.processed_files = set(data.get('processed_files', []))
                self.failed_files = set(data.get('failed_files', []))

                self.logger.info(f"加载处理记录: 成功{len(self.processed_files)}个, 失败{len(self.failed_files)}个")
            else:
                self.logger.info("未找到处理记录文件，从空记录开始")

        except Exception as e:
            self.logger.error(f"加载处理记录失败: {e}")
            self.processed_files = set()
            self.failed_files = set()

    def _save_processing_record(self):
        """保存处理记录"""
        try:
            if self.processing_record_file:
                import json
                data = {
                    'processed_files': list(self.processed_files),
                    'failed_files': list(self.failed_files),
                    'last_updated': str(datetime.now())
                }

                with open(self.processing_record_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                self.logger.debug("处理记录已保存")

        except Exception as e:
            self.logger.error(f"保存处理记录失败: {e}")

    def reset_processing_record(self):
        """重置处理记录"""
        try:
            # 🚀 修复：清空所有相关记录
            self.processed_files.clear()
            self.failed_files.clear()

            # 🚀 修复：清空处理队列和当前处理状态
            self.processing_queue.clear()
            self.current_processing_file = None

            # 删除记录文件
            if self.processing_record_file and self.processing_record_file.exists():
                self.processing_record_file.unlink()
                self.logger.info(f"删除记录文件: {self.processing_record_file}")

            self.logger.info("处理记录已重置")
            self.log_message.emit("🔄 处理记录已重置", "info")
            self.log_message.emit("现在可以重新处理之前的文件", "info")

            # 🚀 修复：启用重置模式，重置后的处理不记录到已处理列表
            self._reset_mode = True
            self.logger.info("启用重置模式：处理结果不会被记录")

            # 🚀 修复：重置后重新扫描文件
            if self.is_running and self.watch_folder:
                self.logger.info("重新扫描监控文件夹...")
                self.log_message.emit("🔍 重新扫描监控文件夹", "info")
                QTimer.singleShot(1000, self._scan_existing_files)  # 1秒后重新扫描

        except Exception as e:
            self.logger.error(f"重置处理记录失败: {e}")
            self.log_message.emit(f"❌ 重置失败: {e}", "error")

    def _process_single_file(self, file_path: str) -> bool:
        """
        处理单个文件
        这里调用父模块的处理逻辑
        """
        try:
            # 生成输出路径
            output_path = self._generate_output_path(file_path)
            if not output_path:
                return False
            
            # 调用父模块的加载和导出方法
            if hasattr(self.parent, 'load_video'):
                self.logger.info(f"🎬 加载视频文件: {os.path.basename(file_path)}")
                self.parent.load_video(file_path)
            elif hasattr(self.parent, 'load_video_file'):
                self.logger.info(f"🎬 加载视频文件: {os.path.basename(file_path)}")
                self.parent.load_video_file(file_path)
            else:
                self.logger.warning("父模块没有视频加载方法")

            # 调用导出方法
            if hasattr(self.parent, 'export_video'):
                self.logger.info(f"📤 开始导出到: {output_path}")
                return self.parent.export_video(batch_output_path=output_path)
            elif hasattr(self.parent, 'export_worker_manager'):
                self.logger.info(f"📤 使用工作管理器导出到: {output_path}")
                return self.parent.export_worker_manager.export_video(batch_output_path=output_path)
            else:
                self.logger.error("父模块没有导出方法")
                return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理单个文件失败: {e}")
            return False
    
    def _generate_output_path(self, input_path: str) -> Optional[str]:
        """生成输出路径"""
        try:
            import uuid
            import re

            input_file = Path(input_path)
            original_name = input_file.stem

            # 🚀 修复：生成新的文件名规则
            # 1. 提取前10个字符（中文、字母、数字）
            clean_name = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', original_name)
            prefix = clean_name[:10] if len(clean_name) >= 10 else clean_name

            # 2. 生成短UUID（8位）
            short_uuid = str(uuid.uuid4()).replace('-', '')[:8]

            # 3. 组合新文件名
            new_name = f"{prefix}_{short_uuid}"

            output_file = Path(self.output_folder) / f"{new_name}{input_file.suffix}"

            self.logger.debug(f"输出路径生成: {original_name} -> {new_name}")
            return str(output_file)

        except Exception as e:
            self.logger.error(f"生成输出路径失败: {e}")
            return None
    
    def _finish_processing(self, file_path: str, success: bool):
        """完成文件处理"""
        try:
            filename = os.path.basename(file_path)
            
            if success:
                self.logger.info(f"处理成功: {filename}")
                self.log_message.emit(f"✅ 处理成功: {filename}", "success")
                # 🚀 修复：只在非重置模式下记录成功处理的文件
                if not self._reset_mode:
                    self.processed_files.add(file_path)
                    self._save_processing_record()
                else:
                    self.logger.info(f"重置模式：不记录处理结果 - {filename}")
            else:
                self.logger.error(f"处理失败: {filename}")
                self.log_message.emit(f"❌ 处理失败: {filename}", "error")
                # 🚀 修复：只在非重置模式下记录失败的文件
                if not self._reset_mode:
                    self.failed_files.add(file_path)
                else:
                    self.logger.info(f"重置模式：不记录失败结果 - {filename}")
            
            # 清除当前处理文件
            self.current_processing_file = None

            # 发送完成信号
            self.processing_finished.emit(file_path, success)

            # 🚀 修复：检查是否还有文件需要处理，更新状态
            if self.processing_queue:
                # 还有文件需要处理，继续下一个
                QTimer.singleShot(1000, self._process_next_file)  # 1秒后处理下一个
            else:
                # 🚀 修复：所有文件处理完成，发送状态更新信号
                self.logger.info("所有文件处理完成，批量处理进入就绪状态")
                self.log_message.emit("🎉 所有文件处理完成", "success")

                # 发送状态更新信号，表示批量处理完成但仍在运行（监控状态）
                self._update_batch_status()

            # 🚀 修复：无论是否有队列，都检查重置模式状态
            if self._reset_mode and not self.processing_queue and not self.current_processing_file:
                self._reset_mode = False
                self.logger.info("重置模式处理完成，恢复正常记录模式")
                self.log_message.emit("🔄 重置模式完成，恢复正常记录", "info")
                
        except Exception as e:
            self.logger.error(f"完成处理失败: {e}")

    def _update_batch_status(self):
        """更新批量处理状态"""
        try:
            # 🚀 新增：发送自定义状态信号，表示处理完成但仍在监控
            if self.is_running:
                if self.current_processing_file:
                    # 有文件在处理中
                    filename = os.path.basename(self.current_processing_file)
                    self.log_message.emit(f"📋 状态更新: 处理中 - {filename}", "status")
                elif self.processing_queue:
                    # 有文件在队列中等待
                    self.log_message.emit(f"📋 状态更新: 等待处理 ({len(self.processing_queue)} 个文件)", "status")
                else:
                    # 没有文件处理，但仍在监控
                    self.log_message.emit("📋 状态更新: 监控中 (就绪)", "status")
            else:
                # 批量处理已停止
                self.log_message.emit("📋 状态更新: 已停止", "status")

        except Exception as e:
            self.logger.error(f"更新批量处理状态失败: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取批量处理状态"""
        return {
            'is_running': self.is_running,
            'watch_folder': self.watch_folder,
            'output_folder': self.output_folder,
            'queue_length': len(self.processing_queue),
            'current_file': self.current_processing_file
        }


# 为了兼容性，保留旧的类名
BatchProcessor = SimpleBatchProcessor
