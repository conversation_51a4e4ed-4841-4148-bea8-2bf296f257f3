#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 工具模块
包含所有支持 cover_edit 模块的工具和辅助类
"""

# 导入所有工具模块
from .constants import CoverEditConstants, get_theme_color, get_button_color
from .logger import get_logger, PerformanceLogger, MemoryLogger
from .decorators import error_handler, performance_monitor, robust_operation
from .resource_manager import get_resource_manager, register_pixmap, register_video_capture
from .video_processor import VideoProcessor
from .image_processor import ImageProcessor
from .export_manager import ExportManager
from .export_settings import VideoExportSettings
from .path_manager import (get_project_root, get_cover_dir, get_bin_dir,
                          get_ffmpeg_path, get_ffprobe_path, find_font_file)

# 新增优化组件
from .resource_cleaner import (register_video_capture_for_cleanup, register_temp_file_for_cleanup,
                              register_thread_for_cleanup, cleanup_all_resources, get_cleanup_stats)
from .thread_safe_manager import (safe_ui_update, safe_batch_operation, safe_queue_operation,
                                 is_batch_processing, create_safe_batch_processor)
from .validation_manager import (validate_video_info, validate_video_file,
                                validate_image_file, validate_export_params)

__all__ = [
    'CoverEditConstants', 'get_theme_color', 'get_button_color',
    'get_logger', 'PerformanceLogger', 'MemoryLogger',
    'error_handler', 'performance_monitor', 'robust_operation',
    'get_resource_manager', 'register_pixmap', 'register_video_capture',
    'VideoProcessor', 'ImageProcessor', 'ExportManager',
    'VideoExportSettings',
    'get_project_root', 'get_cover_dir', 'get_bin_dir',
    'get_ffmpeg_path', 'get_ffprobe_path', 'find_font_file',
    # 新增优化组件
    'register_video_capture_for_cleanup', 'register_temp_file_for_cleanup',
    'register_thread_for_cleanup', 'cleanup_all_resources', 'get_cleanup_stats',
    'safe_ui_update', 'safe_batch_operation', 'safe_queue_operation',
    'is_batch_processing', 'create_safe_batch_processor',
    'validate_video_info', 'validate_video_file', 'validate_image_file', 'validate_export_params'
]
