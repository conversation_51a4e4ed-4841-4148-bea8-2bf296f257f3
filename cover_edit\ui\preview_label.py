#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预览标签组件
完整迁移自原模块的PreviewLabel类
"""

import os
from typing import Optional, Tuple
from PyQt6.QtWidgets import QLabel, QSizePolicy
from PyQt6.QtCore import Qt, pyqtSignal, QPoint
from PyQt6.QtGui import QPixmap, QPainter, QColor, QFont, QFontMetrics, QMouseEvent, QKeyEvent

# 导入常量
from cover_edit.utils.constants import CoverEditConstants

class PreviewLabel(QLabel):
    """
    完整的预览标签类 - 从原模块完整迁移
    支持图像显示和文本编辑
    """

    # 信号定义
    text_added = pyqtSignal(str, int, int)  # 文本, x, y

    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_properties()
        self._setup_ui()
        # 🏗️ 新设计：不再需要文字输入框设置

    def _init_properties(self):
        """初始化属性 - 与原模块完全一致"""
        self.original_pixmap = None
        self.aspect_ratio = None
        # 🏗️ 新设计：不再需要文字相关属性

        # 字体设置
        self.font_settings = {
            'font_name': 'Microsoft YaHei UI',
            'font_size': 24,
            'font_style': 'normal',
            'font_color': QColor(255, 255, 255)
        }

        # 🏗️ 新设计：不再需要文字编辑相关属性

        # 填充模式
        self.fill_mode = False  # 是否使用填充模式

    def _setup_ui(self):
        """初始化UI设置 - 与原模块完全一致"""
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumSize(*CoverEditConstants.MIN_PREVIEW_SIZE)

    # 🏗️ 新设计：不再需要文字输入框设置方法

    # 🏗️ 新设计：不再需要文字输入框样式方法

    def setPixmap(self, pixmap: QPixmap):
        """设置图像 - 与原模块完全一致"""
        if pixmap and not pixmap.isNull():
            self.original_pixmap = pixmap
            self.aspect_ratio = pixmap.width() / pixmap.height()

            # 根据填充模式决定如何显示图像
            if self.fill_mode:
                # 填充模式：拉伸图像填满整个标签
                scaled_pixmap = pixmap.scaled(
                    self.size(),
                    Qt.AspectRatioMode.IgnoreAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
            else:
                # 适应模式：保持宽高比
                scaled_pixmap = pixmap.scaled(
                    self.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

            super().setPixmap(scaled_pixmap)
        else:
            self.original_pixmap = None
            self.aspect_ratio = None
            super().setPixmap(pixmap)

    def mousePressEvent(self, event: QMouseEvent):
        """鼠标点击事件 - 🏗️ 新设计：不再处理文字编辑"""
        # 🏗️ 新设计：文字编辑已改为独立窗口模式，不再在预览区域处理
        super().mousePressEvent(event)

    # 🏗️ 新设计：不再需要文字编辑开始方法

    # 🏗️ 新设计：文字功能已改为独立窗口模式，移除这些方法

    # 🏗️ 新设计：不再需要文字编辑结束和文字变化方法

    def keyPressEvent(self, event: QKeyEvent):
        """键盘事件 - 🏗️ 新设计：不再处理文字编辑"""
        # 🏗️ 新设计：文字编辑已改为独立窗口模式，不再在预览区域处理
        super().keyPressEvent(event)

    def set_fill_mode(self, fill_mode: bool):
        """设置填充模式 - 与原模块完全一致"""
        self.fill_mode = fill_mode
        if self.original_pixmap:
            self.setPixmap(self.original_pixmap)

    def get_display_size(self) -> Tuple[int, int]:
        """获取显示尺寸 - 与原模块完全一致"""
        if self.pixmap():
            return self.pixmap().width(), self.pixmap().height()
        return self.width(), self.height()

    def contextMenuEvent(self, event):
        """右键菜单事件 - 与原模块完全一致"""
        # 暂时屏蔽右键菜单
        pass

    def set_pixmap(self, pixmap):
        """设置图像并保持宽高比 - 优化版本防止模糊"""
        self.original_pixmap = pixmap
        if not pixmap.isNull():
            self.aspect_ratio = pixmap.width() / pixmap.height()

        # 🔧 优化：清除缓存，因为原始图像已更改
        if hasattr(self, '_scaled_cache'):
            self._scaled_cache.clear()

        self.update_pixmap()

    def update_pixmap(self):
        """更新显示的图像，保持比例并适合预览区大小 - 优化版本防止模糊"""
        if not (hasattr(self, 'original_pixmap') and self.original_pixmap and not self.original_pixmap.isNull()):
            return

        # 获取预览区域大小
        label_size = self.size()
        if label_size.width() <= 0 or label_size.height() <= 0:
            return

        # 🔧 优化：使用缓存机制避免重复缩放导致的模糊
        cache_key = f"{label_size.width()}x{label_size.height()}"

        # 检查缓存
        if not hasattr(self, '_scaled_cache'):
            self._scaled_cache = {}

        if cache_key in self._scaled_cache:
            # 使用缓存的高质量缩放版本
            super().setPixmap(self._scaled_cache[cache_key])
            return

        # 🔧 优化：始终从原始高质量图像缩放，而不是从当前显示的图像
        scaled_pixmap = self.original_pixmap.scaled(
            label_size,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

        # 缓存缩放结果
        self._scaled_cache[cache_key] = scaled_pixmap

        # 限制缓存大小，避免内存占用过多
        if len(self._scaled_cache) > 10:
            # 删除最旧的缓存项
            oldest_key = next(iter(self._scaled_cache))
            del self._scaled_cache[oldest_key]

        # 设置缩放后的图像
        super().setPixmap(scaled_pixmap)

    def clear(self):
        """清除图像和文本 - 与原模块完全一致"""
        self.original_pixmap = None
        self.aspect_ratio = None
        super().clear()  # 清除文本和pixmap

    def resizeEvent(self, event):
        """重写调整大小事件，保持宽高比 - 与原模块完全一致"""
        super().resizeEvent(event)
        self.update_pixmap()


class SimplePreviewLabel(QLabel):
    """
    简化版预览标签 - 避免循环导入
    只提供基本的图像显示功能
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.original_pixmap = None
        self.aspect_ratio = None
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumSize(400, 300)

    def setPixmap(self, pixmap: QPixmap):
        """设置图像并保持宽高比 - 优化版本防止模糊"""
        if pixmap and not pixmap.isNull():
            self.original_pixmap = pixmap
            self.aspect_ratio = pixmap.width() / pixmap.height()

            # 🔧 优化：清除缓存，因为原始图像已更改
            if hasattr(self, '_scaled_cache'):
                self._scaled_cache.clear()

            # 缩放图像保持宽高比
            scaled_pixmap = pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )
            super().setPixmap(scaled_pixmap)
        else:
            self.original_pixmap = None
            self.aspect_ratio = None
            super().setPixmap(pixmap)

    def resizeEvent(self, event):
        """重写调整大小事件，保持宽高比 - 优化版本防止模糊"""
        super().resizeEvent(event)
        if self.original_pixmap and not self.original_pixmap.isNull():
            # 🔧 优化：使用缓存机制避免重复缩放
            self._update_scaled_pixmap()

    def _update_scaled_pixmap(self):
        """更新缩放后的图像 - 使用缓存机制"""
        if not self.original_pixmap or self.original_pixmap.isNull():
            return

        # 获取当前尺寸
        current_size = self.size()
        if current_size.width() <= 0 or current_size.height() <= 0:
            return

        # 使用缓存机制
        cache_key = f"{current_size.width()}x{current_size.height()}"

        if not hasattr(self, '_scaled_cache'):
            self._scaled_cache = {}

        if cache_key in self._scaled_cache:
            # 使用缓存的高质量缩放版本
            super().setPixmap(self._scaled_cache[cache_key])
            return

        # 从原始图像缩放
        scaled_pixmap = self.original_pixmap.scaled(
            current_size,
            Qt.AspectRatioMode.KeepAspectRatio,
            Qt.TransformationMode.SmoothTransformation
        )

        # 缓存结果
        self._scaled_cache[cache_key] = scaled_pixmap

        # 限制缓存大小
        if len(self._scaled_cache) > 10:
            oldest_key = next(iter(self._scaled_cache))
            del self._scaled_cache[oldest_key]

        super().setPixmap(scaled_pixmap)
