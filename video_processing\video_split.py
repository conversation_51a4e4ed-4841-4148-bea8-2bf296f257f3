import os
import subprocess
import time
import threading
import platform
from PyQt6.QtWidgets import (QVBoxLayout, QLabel, QGroupBox, QHBoxLayout, 
                             QLineEdit, QPushButton, QComboBox,
                             QTextEdit, QProgressBar, QSizePolicy, QFileDialog,
                             QTabWidget, QWidget, QGridLayout, QMessageBox,
                             QFrame)
from PyQt6.QtGui import QFont, QTextCursor, QIntValidator
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from core.base_module import BaseModule
from core.path_manager import get_path, set_path

class SplitWorker(QThread):
    """视频分割工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, module, method, args):
        super().__init__()
        self.module = module
        self.method = method
        self.args = args
        self._is_cancelled = False

    def run(self):
        try:
            # 执行分割方法
            success = self.method(*self.args)
            self.finished.emit(success, "")
        except Exception as e:
            self.log_message.emit(f"> 严重错误: {str(e)}")
            self.finished.emit(False, str(e))
        finally:
            self.progress_updated.emit(100)

    def cancel(self):
        self._is_cancelled = True
        self.log_message.emit("> 用户请求取消操作...")


class VideoSplitModule(BaseModule):
    def init_ui(self):
        try:
            self.logger.info("初始化视频分割模块")
            
            layout = QVBoxLayout(self)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            self.setStyleSheet(self.get_style())
            
            # 标题
            title = QLabel(self.get_title())
            title.setStyleSheet(f"color: {self.get_color()}; font-size: 24px; font-weight: bold;")
            title.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(title)
            
            # 文件选择区域
            file_group = QGroupBox("选择视频文件")
            file_layout = QVBoxLayout(file_group)
            file_layout.setContentsMargins(15, 20, 15, 15)
            
            # 文件路径输入
            path_layout = QHBoxLayout()
            self.path_input = QLineEdit()
            self.path_input.setPlaceholderText("选择视频文件...")
            self.path_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            
            browse_btn = QPushButton("浏览...")
            browse_btn.setMinimumSize(100, 40)
            browse_btn.setStyleSheet(self._get_button_style())
            browse_btn.clicked.connect(self.select_file)
            
            path_layout.addWidget(self.path_input)
            path_layout.addWidget(browse_btn)
            file_layout.addLayout(path_layout)
            
            # 输出路径选择
            output_layout = QHBoxLayout()
            self.output_input = QLineEdit()
            self.output_input.setPlaceholderText("选择输出文件夹...")
            self.output_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            
            output_browse_btn = QPushButton("浏览...")
            output_browse_btn.setMinimumSize(100, 40)
            output_browse_btn.setStyleSheet(self._get_button_style())
            output_browse_btn.clicked.connect(self.select_output_folder)
            
            output_layout.addWidget(self.output_input)
            output_layout.addWidget(output_browse_btn)
            file_layout.addLayout(output_layout)
            
            # 视频信息显示
            self.info_label = QLabel("视频信息: 未加载")
            self.info_label.setStyleSheet("color: #88C0D0; font-size: 12px;")
            file_layout.addWidget(self.info_label)
            
            layout.addWidget(file_group)
            
            # 分割方式选项卡
            self.tab_widget = QTabWidget()
            self.tab_widget.setStyleSheet("""
                QTabWidget::pane {
                    border: 1px solid #3e4451;
                    border-radius: 4px;
                    background: #2c313c;
                }
                QTabBar::tab {
                    background: #353b45;
                    color: #abb2bf;
                    padding: 8px 16px;
                    border: 1px solid #3e4451;
                    border-bottom: none;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                    margin-right: 2px;
                }
                QTabBar::tab:selected {
                    background: #61AFEF;
                    color: white;
                    border-color: #61AFEF;
                }
                QTabBar::tab:hover {
                    background: #528CCF;
                }
            """)
            
            # 创建四个选项卡
            self.create_time_tab()
            self.create_count_tab()
            self.create_size_tab()
            self.create_scene_tab()
            
            layout.addWidget(self.tab_widget)
            
            # 输出格式选择
            format_layout = QHBoxLayout()
            format_label = QLabel("输出格式:")
            format_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
            
            self.format_combo = QComboBox()
            self.format_combo.addItems(["MP4", "AVI", "MOV", "MKV"])
            self.format_combo.setStyleSheet(self._get_combo_style())
            
            format_layout.addWidget(format_label)
            format_layout.addWidget(self.format_combo)
            format_layout.addStretch()
            
            # 编码选项
            codec_label = QLabel("视频编码:")
            codec_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
            
            self.codec_combo = QComboBox()
            self.codec_combo.addItems(["复制 (无重编码)", "H.264", "H.265", "VP9"])
            self.codec_combo.setStyleSheet(self._get_combo_style())
            
            format_layout.addWidget(codec_label)
            format_layout.addWidget(self.codec_combo)
            layout.addLayout(format_layout)
            
            # 进度条 (放在开始执行按钮上方)
            self.progress_bar = QProgressBar()
            self.progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #303238;
                    border-radius: 4px;
                    text-align: center;
                    background-color: #1E2025;
                    height: 20px;
                }
                QProgressBar::chunk {
                    background-color: #61AFEF;
                    border-radius: 3px;
                }
            """)
            self.progress_bar.setVisible(False)
            layout.addWidget(self.progress_bar)
            
            # 操作按钮 - 居中布局
            btn_frame = QWidget()
            btn_layout = QHBoxLayout(btn_frame)
            btn_layout.setContentsMargins(0, 10, 0, 0)
            btn_layout.addStretch()
            
            self.start_btn = QPushButton("开始执行")
            self.start_btn.setMinimumSize(160, 45)
            self.start_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {self.get_color()};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #528CCF;
                }}
            """)
            self.start_btn.clicked.connect(self.start_processing)
            
            self.cancel_btn = QPushButton("取消操作")
            self.cancel_btn.setMinimumSize(160, 45)
            self.cancel_btn.setStyleSheet("""
                QPushButton {
                    background-color: #E06C75;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #BE5046;
                }
                QPushButton:disabled {
                    background-color: #5C6370;
                }
            """)
            self.cancel_btn.setEnabled(False)
            self.cancel_btn.clicked.connect(self.cancel_processing)
            
            btn_layout.addWidget(self.cancel_btn)
            btn_layout.addWidget(self.start_btn)
            btn_layout.addStretch()
            
            layout.addWidget(btn_frame)
            
            # 分隔线
            separator = QFrame()
            separator.setFrameShape(QFrame.Shape.HLine)
            separator.setFrameShadow(QFrame.Shadow.Sunken)
            separator.setStyleSheet("background-color: #3A3D44;")
            layout.addWidget(separator)
            
            # 日志区域
            log_group = QGroupBox("处理日志")
            log_layout = QVBoxLayout(log_group)
            
            self.log_output = QTextEdit()
            self.log_output.setReadOnly(True)
            self.log_output.setStyleSheet("""
                QTextEdit {
                    background-color: #1E2025;
                    border: 1px solid #303238;
                    border-radius: 4px;
                    color: #ABB2BF;
                    font-family: 'Consolas', monospace;
                    font-size: 12px;
                    padding: 8px;
                }
            """)
            log_layout.addWidget(self.log_output)
            
            layout.addWidget(log_group, 1)  # 设置拉伸因子
            
            # 初始化工作线程
            self.worker = None
            self.is_processing = False
            
            self.logger.info("视频分割模块初始化完成")
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}", exc_info=True)
            # 显示错误UI
            layout = QVBoxLayout(self)
            error_label = QLabel(f"初始化错误: {str(e)}")
            error_label.setStyleSheet("color: red; font-size: 16px;")
            layout.addWidget(error_label)
    
    def create_time_tab(self):
        """创建按时间分割选项卡"""
        tab = QWidget()
        layout = QGridLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setHorizontalSpacing(15)
        layout.setVerticalSpacing(10)
        
        # 时间间隔
        time_label = QLabel("时间间隔 (秒):")
        time_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(time_label, 0, 0)
        
        # 使用QLineEdit输入框
        self.time_input = QLineEdit()
        self.time_input.setText("10")  # 默认10秒
        self.time_input.setValidator(QIntValidator(1, 3600))  # 限制输入1-3600的整数
        self.time_input.setStyleSheet(self._get_combo_style())
        self.time_input.textChanged.connect(self.update_segment_count)
        layout.addWidget(self.time_input, 0, 1)
        
        # 预估片段个数
        self.segment_count_label = QLabel("预估片段个数: 0")
        self.segment_count_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(self.segment_count_label, 0, 2)
        
        # 提示信息
        tip_label = QLabel("提示: 按固定时间间隔分割整个视频")
        tip_label.setStyleSheet("color: #888888; font-size: 12px;")
        layout.addWidget(tip_label, 1, 0, 1, 3)
        
        self.tab_widget.addTab(tab, "按时间分割")
    
    def create_count_tab(self):
        """创建按个数分割选项卡"""
        tab = QWidget()
        layout = QGridLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setHorizontalSpacing(15)
        layout.setVerticalSpacing(10)
        
        # 分割数量
        count_label = QLabel("分割数量:")
        count_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(count_label, 0, 0)
        
        # 使用QLineEdit输入框
        self.count_input = QLineEdit()
        self.count_input.setText("5")  # 默认5段
        self.count_input.setValidator(QIntValidator(2, 100))  # 限制输入2-100的整数
        self.count_input.setStyleSheet(self._get_combo_style())
        self.count_input.textChanged.connect(self.update_count_segment_count)
        layout.addWidget(self.count_input, 0, 1)
        
        # 预估每段时间
        self.count_time_label = QLabel("预估每段时间: 0秒")
        self.count_time_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(self.count_time_label, 0, 2)
        
        # 提示信息
        tip_label = QLabel("提示: 将视频平均分割为指定数量的片段")
        tip_label.setStyleSheet("color: #888888; font-size: 12px;")
        layout.addWidget(tip_label, 1, 0, 1, 3)
        
        self.tab_widget.addTab(tab, "按个数分割")
    
    def create_size_tab(self):
        """创建按大小分割选项卡 - 默认5MB"""
        tab = QWidget()
        layout = QGridLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setHorizontalSpacing(15)
        layout.setVerticalSpacing(10)
        
        # 分割大小
        size_label = QLabel("分割大小 (MB):")
        size_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(size_label, 0, 0)
        
        # 使用QLineEdit输入框
        self.size_input = QLineEdit()
        self.size_input.setText("5")  # 默认5MB
        self.size_input.setValidator(QIntValidator(1, 5000))  # 最小1MB，最大5000MB
        self.size_input.setStyleSheet(self._get_combo_style())
        self.size_input.textChanged.connect(self.update_size_segment_count)
        layout.addWidget(self.size_input, 0, 1)
        
        # 预估片段个数
        self.size_segment_label = QLabel("预估片段个数: 0")
        self.size_segment_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(self.size_segment_label, 0, 2)
        
        # 提示信息
        tip_label = QLabel("提示: 按文件大小分割视频，适用于需要控制文件体积的场景")
        tip_label.setStyleSheet("color: #888888; font-size: 12px;")
        layout.addWidget(tip_label, 1, 0, 1, 3)
        
        self.tab_widget.addTab(tab, "按大小分割")
    
    def create_scene_tab(self):
        """创建按场景分割选项卡 - 已修复阈值提示"""
        tab = QWidget()
        layout = QGridLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setHorizontalSpacing(15)
        layout.setVerticalSpacing(10)
        
        # 场景变化阈值 - 修改为1-100
        threshold_label = QLabel("场景变化阈值 (1-100):")
        threshold_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(threshold_label, 0, 0)
        
        # 使用QLineEdit输入框
        self.threshold_input = QLineEdit()
        self.threshold_input.setText("30")  # 默认30
        self.threshold_input.setValidator(QIntValidator(1, 100))  # 限制输入1-100的整数
        self.threshold_input.setStyleSheet(self._get_combo_style())
        layout.addWidget(self.threshold_input, 0, 1)
        
        # 预估场景个数 (场景分割无法准确预估)
        self.scene_segment_label = QLabel("预估场景个数: 无法预估")
        self.scene_segment_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(self.scene_segment_label, 0, 2)
        
        # 最小场景时长
        min_duration_label = QLabel("最小场景时长 (秒):")
        min_duration_label.setStyleSheet("color: #ABB2BF; font-size: 14px;")
        layout.addWidget(min_duration_label, 1, 0)
        
        # 使用QLineEdit输入框
        self.min_duration_input = QLineEdit()
        self.min_duration_input.setText("3")  # 默认3秒
        self.min_duration_input.setValidator(QIntValidator(1, 60))  # 限制输入1-60的整数
        self.min_duration_input.setStyleSheet(self._get_combo_style())
        layout.addWidget(self.min_duration_input, 1, 1)
        
        # 提示信息 - 修改推荐值
        tip_label = QLabel("提示: 阈值越高，场景变化检测越敏感 (推荐值: 30-50)")
        tip_label.setStyleSheet("color: #888888; font-size: 12px;")
        layout.addWidget(tip_label, 2, 0, 1, 3)
        
        self.tab_widget.addTab(tab, "按场景分割")
    
    def get_title(self):
        return "视频分割"
    
    def get_color(self):
        return "#61AFEF"
    
    def _get_button_style(self):
        return """
            QPushButton {
                background-color: #3A3D44;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #4A4D54;
            }
        """
    
    def _get_combo_style(self):
        return """
            QComboBox {
                background-color: #1E2025;
                color: #D0D0D0;
                border: 1px solid #303238;
                border-radius: 4px;
                padding: 5px;
                min-width: 200px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox QAbstractItemView {
                background-color: #1E2025;
                color: #D0D0D0;
                selection-background-color: #3A3D44;
            }
        """
    
    def select_file(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            get_path('video_split_input'),
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.flv);;所有文件 (*.*)"
        )
        if file_name:
            set_path('video_split_input', file_name)  # 保存路径
            self.path_input.setText(file_name)
            self.log_output.append(f"> 已选择视频文件: {file_name}")
            self.update_video_info(file_name)
            self.update_segment_count()
            self.update_size_segment_count()
            self.update_count_segment_count()
    
    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(
            self,
            "选择输出文件夹",
            get_path('video_split_output')
        )
        if folder:
            set_path('video_split_output', folder)  # 保存路径
            self.output_input.setText(folder)
            self.log_output.append(f"> 输出文件夹: {folder}")
    
    def update_video_info(self, file_path):
        """更新视频信息显示"""
        if not os.path.exists(file_path):
            self.info_label.setText("视频信息: 文件不存在")
            return
        
        try:
            duration = self.get_video_duration(file_path)
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
            
            if duration > 0:
                mins, secs = divmod(duration, 60)
                hours, mins = divmod(mins, 60)
                duration_str = f"{int(hours):02d}:{int(mins):02d}:{int(secs):02d}"
                self.info_label.setText(f"视频信息: {duration_str} | {file_size:.2f} MB")
            else:
                self.info_label.setText(f"视频信息: 无法获取时长 | {file_size:.2f} MB")
        except Exception as e:
            self.logger.error(f"获取视频信息失败: {str(e)}")
            self.info_label.setText("视频信息: 获取失败")
    
    def update_segment_count(self):
        """更新按时间分割预估片段个数"""
        input_file = self.path_input.text()
        if not input_file or not os.path.exists(input_file):
            self.segment_count_label.setText("预估片段个数: 0")
            return
        
        try:
            # 获取时间间隔
            interval = self.time_input.text()
            if not interval:
                interval = 10
            else:
                interval = int(interval)
            
            if interval <= 0:
                self.segment_count_label.setText("预估片段个数: 0")
                return
            
            # 获取视频时长
            duration = self.get_video_duration(input_file)
            if duration <= 0:
                self.segment_count_label.setText("预估片段个数: 未知")
                return
            
            # 计算预估片段个数
            segment_count = int(duration // interval)
            if duration % interval > 0:
                segment_count += 1
                
            self.segment_count_label.setText(f"预估片段个数: {segment_count}")
        except Exception as e:
            self.logger.error(f"更新片段个数失败: {str(e)}")
            self.segment_count_label.setText("预估片段个数: 错误")
    
    def update_count_segment_count(self):
        """更新按个数分割预估每段时间"""
        input_file = self.path_input.text()
        if not input_file or not os.path.exists(input_file):
            self.count_time_label.setText("预估每段时间: 0秒")
            return
        
        try:
            # 获取分割数量
            count = self.count_input.text()
            if not count:
                count = 5
            else:
                count = int(count)
            
            if count <= 0:
                self.count_time_label.setText("预估每段时间: 0秒")
                return
            
            # 获取视频时长
            duration = self.get_video_duration(input_file)
            if duration <= 0:
                self.count_time_label.setText("预估每段时间: 未知")
                return
            
            # 计算每段时间
            segment_time = duration / count
                
            self.count_time_label.setText(f"预估每段时间: {segment_time:.2f}秒")
        except Exception as e:
            self.logger.error(f"更新每段时间失败: {str(e)}")
            self.count_time_label.setText("预估每段时间: 错误")
    
    def update_size_segment_count(self):
        """更新按大小分割预估片段个数"""
        input_file = self.path_input.text()
        if not input_file or not os.path.exists(input_file):
            self.size_segment_label.setText("预估片段个数: 0")
            return
        
        try:
            # 获取分割大小
            size_mb = self.size_input.text()
            if not size_mb:
                size_mb = 5
            else:
                size_mb = int(size_mb)
            
            if size_mb <= 0:
                self.size_segment_label.setText("预估片段个数: 0")
                return
            
            # 获取视频文件大小
            file_size = os.path.getsize(input_file) / (1024 * 1024)  # MB
            if file_size <= 0:
                self.size_segment_label.setText("预估片段个数: 未知")
                return
            
            # 计算预估片段个数
            segment_count = int(file_size // size_mb)
            if file_size % size_mb > 0:
                segment_count += 1
                
            self.size_segment_label.setText(f"预估片段个数: {segment_count}")
        except Exception as e:
            self.logger.error(f"更新大小分割片段个数失败: {str(e)}")
            self.size_segment_label.setText("预估片段个数: 错误")
    
    def start_processing(self):
        if self.is_processing:
            self.log_output.append("> 操作正在进行中，请等待完成或取消")
            return
            
        input_file = self.path_input.text()
        output_folder = self.output_input.text()
        
        if not input_file:
            self.log_output.append("> 错误：请先选择视频文件")
            QMessageBox.warning(self, "输入错误", "请先选择视频文件")
            return
        
        if not output_folder:
            self.log_output.append("> 错误：请先选择输出文件夹")
            QMessageBox.warning(self, "输出错误", "请先选择输出文件夹")
            return
        
        if not os.path.exists(input_file):
            self.log_output.append(f"> 错误：视频文件不存在 - {input_file}")
            QMessageBox.critical(self, "文件错误", f"视频文件不存在:\n{input_file}")
            return
        
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            self.log_output.append(f"> 创建输出文件夹: {output_folder}")
        
        output_format = self.format_combo.currentText().lower()
        codec_option = self.codec_combo.currentText()
        
        self.log_output.clear()
        self.log_output.append("> 开始视频分割处理...")
        self.log_output.append(f"> 输入文件: {input_file}")
        self.log_output.append(f"> 输出文件夹: {output_folder}")
        self.log_output.append(f"> 输出格式: {output_format}")
        self.log_output.append(f"> 编码选项: {codec_option}")
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 根据当前选中的选项卡调用不同的分割函数
        try:
            current_tab = self.tab_widget.currentIndex()
            tab_name = self.tab_widget.tabText(current_tab)
            
            self.log_output.append(f"> 分割方式: {tab_name}")
            
            # 设置处理状态
            self.is_processing = True
            self.start_btn.setEnabled(False)
            self.start_btn.setText("处理中...")  # 修改按钮文字
            self.cancel_btn.setEnabled(True)
            
            if current_tab == 0:  # 按时间分割
                try:
                    interval = int(self.time_input.text())
                except ValueError:
                    interval = 10
                
                self.log_output.append(f"> 时间间隔: {interval}秒")
                
                self.worker = SplitWorker(
                    self,
                    self.split_by_time,
                    (input_file, output_folder, output_format, 
                     interval, codec_option)
                )
            
            elif current_tab == 1:  # 按个数分割
                try:
                    count = int(self.count_input.text())
                except ValueError:
                    count = 5
                
                self.log_output.append(f"> 分割数量: {count}段")
                
                self.worker = SplitWorker(
                    self,
                    self.split_by_count,
                    (input_file, output_folder, output_format, 
                     count, codec_option)
                )
            
            elif current_tab == 2:  # 按大小分割
                try:
                    size_mb = int(self.size_input.text())
                except ValueError:
                    size_mb = 5
                
                self.log_output.append(f"> 分割大小: {size_mb}MB")
                
                self.worker = SplitWorker(
                    self,
                    self.split_by_size,
                    (input_file, output_folder, output_format, 
                     size_mb, codec_option)
                )
            
            elif current_tab == 3:  # 按场景分割
                try:
                    threshold = int(self.threshold_input.text())
                except ValueError:
                    threshold = 30
                
                try:
                    min_duration = int(self.min_duration_input.text())
                except ValueError:
                    min_duration = 3
                
                self.log_output.append(f"> 场景变化阈值: {threshold}")
                self.log_output.append(f"> 最小场景时长: {min_duration}秒")
                
                self.worker = SplitWorker(
                    self,
                    self.split_by_scene,
                    (input_file, output_folder, output_format, 
                     threshold, min_duration, codec_option)
                )
            
            # 连接工作线程信号
            self.worker.progress_updated.connect(self.update_progress)
            self.worker.log_message.connect(self.append_log)
            self.worker.finished.connect(self.on_processing_finished)
            
            # 启动工作线程
            self.worker.start()
            
        except Exception as e:
            self.log_output.append(f"> 启动处理失败: {str(e)}")
            self.is_processing = False
            self.start_btn.setEnabled(True)
            self.start_btn.setText("开始执行")  # 恢复按钮文字
            self.cancel_btn.setEnabled(False)
    
    def cancel_processing(self):
        if self.worker and self.worker.isRunning():
            self.worker.cancel()
            self.log_output.append("> 正在取消操作，请稍候...")
            self.cancel_btn.setEnabled(False)
    
    def on_processing_finished(self, success, message):
        self.is_processing = False
        self.start_btn.setEnabled(True)
        self.start_btn.setText("开始执行")  # 恢复按钮文字
        self.cancel_btn.setEnabled(False)
        
        if success:
            self.log_output.append("> 视频分割完成!")
            self.log_output.append("> 操作已成功完成")
        else:
            self.log_output.append("> 操作失败!")
            if message:
                self.log_output.append(f"> 错误原因: {message}")
    
    def update_progress(self, value):
        self.progress_bar.setValue(value)
    
    def append_log(self, message):
        self.log_output.append(message)
        # 自动滚动到底部
        self.log_output.moveCursor(QTextCursor.MoveOperation.End)
    
    def get_video_duration(self, input_file):
        """获取视频时长（秒）"""
        # 使用ffprobe获取视频时长（更可靠的方法）
        ffprobe_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "tools", "ffprobe")
        
        if os.name == 'nt':  # Windows系统
            ffprobe_path += ".exe"
        
        if not os.path.exists(ffprobe_path):
            self.append_log(f"> 警告：找不到ffprobe，使用备用方法获取时长")
            return self.get_video_duration_fallback(input_file)
        
        try:
            cmd = [
                ffprobe_path,
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                input_file
            ]
            
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                return float(result.stdout.strip())
            
            self.append_log(f"> 错误：获取视频时长失败: {result.stderr.strip()}")
            return 0
        except Exception as e:
            self.append_log(f"> 获取视频时长异常: {str(e)}")
            return self.get_video_duration_fallback(input_file)
    
    def get_video_duration_fallback(self, input_file):
        """备用方法获取视频时长"""
        try:
            # 使用ffmpeg获取时长（原始方法）
            ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "tools", "ffmpeg")
            
            if os.name == 'nt':  # Windows系统
                ffmpeg_path += ".exe"
            
            cmd = [
                ffmpeg_path,
                "-i", input_file,
                "-f", "null",
                "-"
            ]
            
            result = subprocess.run(cmd, stderr=subprocess.PIPE, universal_newlines=False)
            stderr_output = result.stderr.decode('utf-8', errors='ignore')
            
            # 解析视频时长
            duration_line = [line for line in stderr_output.splitlines() if "Duration:" in line]
            if duration_line:
                duration_line = duration_line[0]
                duration_str = duration_line.split("Duration:")[1].split(",")[0].strip()
                
                # 将时长转换为秒
                parts = duration_str.split(":")
                if len(parts) == 3:
                    h, m, s = parts
                    return int(h) * 3600 + int(m) * 60 + float(s)
            
            return 0
        except Exception:
            return 0
    
    def check_disk_space(self, folder, required_bytes):
        """检查磁盘是否有足够空间"""
        try:
            # Windows系统
            if platform.system() == "Windows":
                import ctypes
                free_bytes = ctypes.c_ulonglong(0)
                ctypes.windll.kernel32.GetDiskFreeSpaceExW(
                    ctypes.c_wchar_p(folder), None, None, ctypes.pointer(free_bytes))
                return free_bytes.value >= required_bytes
            # Unix/Linux/Mac系统
            else:
                stat = os.statvfs(folder)
                return stat.f_bavail * stat.f_frsize >= required_bytes
        except Exception as e:
            self.append_log(f"> 警告: 无法检查磁盘空间 - {str(e)}")
            return True  # 如果无法检查，则假定空间足够
    
    def build_ffmpeg_cmd(self, input_file, output_file, start_time=None, duration=None, codec_option="复制 (无重编码)"):
        """构建FFmpeg命令 - 增强版本"""
        cmd = [self.get_ffmpeg_path()]
        
        if start_time is not None:
            cmd.extend(["-ss", str(start_time)])
        
        cmd.extend(["-i", input_file])
        
        if duration is not None:
            cmd.extend(["-t", str(duration)])
        
        # 验证输出格式
        valid_formats = ["mp4", "avi", "mov", "mkv"]
        file_ext = output_file.split('.')[-1].lower()
        if file_ext not in valid_formats:
            # 自动修正为MP4格式
            output_file = output_file.rsplit('.', 1)[0] + ".mp4"
            self.append_log(f"> 警告: 不支持的输出格式, 已自动改为MP4")
        
        # 编码选项
        if codec_option == "复制 (无重编码)":
            cmd.extend(["-c", "copy"])
        elif codec_option == "H.264":
            cmd.extend(["-c:v", "libx264", "-preset", "medium", "-crf", "23"])
        elif codec_option == "H.265":
            cmd.extend(["-c:v", "libx265", "-preset", "medium", "-crf", "28"])
        elif codec_option == "VP9":
            cmd.extend(["-c:v", "libvpx-vp9", "-crf", "30", "-b:v", "0"])
        
        cmd.extend([
            "-avoid_negative_ts", "1",
            "-loglevel", "error",  # 只显示错误信息
            "-y",  # 覆盖输出文件
            output_file
        ])
        
        return cmd
    
    def get_ffmpeg_path(self):
        """获取FFmpeg路径"""
        ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "tools", "ffmpeg")
        
        if os.name == 'nt':  # Windows系统
            ffmpeg_path += ".exe"
        
        return ffmpeg_path
    
    def split_by_time(self, input_file, output_folder, output_format, 
                     interval, codec_option="复制 (无重编码)"):
        """按时间间隔分割视频"""
        # 获取视频总时长
        total_seconds = self.get_video_duration(input_file)
        if total_seconds <= 0:
            self.append_log("> 错误：无法获取视频时长")
            return False
        
        self.append_log(f"> 视频总时长: {total_seconds:.2f}秒")
        
        segment_count = int(total_seconds // interval)
        if segment_count == 0:
            segment_count = 1
        if total_seconds % interval > 0:
            segment_count += 1
        
        self.append_log(f"> 将分割为 {segment_count} 个片段")
        
        # 检查磁盘空间
        file_size = os.path.getsize(input_file)
        if not self.check_disk_space(output_folder, file_size * 2):
            self.append_log("> 错误：磁盘空间不足，无法进行分割")
            return False
        
        # 开始分割
        segment_num = 0
        current_time = 0
        
        while current_time < total_seconds:
            if self.worker and self.worker._is_cancelled:
                self.append_log("> 操作已被用户取消")
                return False
                
            segment_num += 1
            output_file = os.path.join(output_folder, f"time_{segment_num}.{output_format}")
            
            # 计算当前片段的持续时间
            duration = min(interval, total_seconds - current_time)
            
            # 构建命令
            cmd = self.build_ffmpeg_cmd(
                input_file, output_file, 
                start_time=current_time, 
                duration=duration,
                codec_option=codec_option
            )
            
            self.append_log(f"> 生成片段 {segment_num}: {output_file}")
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False)
            
            # 检查是否成功
            if result.returncode != 0:
                error_msg = result.stderr.decode('utf-8', errors='ignore') if result.stderr else "未知错误"
                self.append_log(f"> 错误: {error_msg}")
                return False
            
            current_time += duration
            progress = min(99, int((current_time / total_seconds) * 100))
            self.worker.progress_updated.emit(progress)
        
        self.worker.progress_updated.emit(100)
        return True
    
    def split_by_count(self, input_file, output_folder, output_format, 
                      count, codec_option="复制 (无重编码)"):
        """按个数分割视频"""
        # 获取视频总时长
        total_seconds = self.get_video_duration(input_file)
        if total_seconds <= 0:
            self.append_log("> 错误：无法获取视频时长")
            return False
        
        self.append_log(f"> 视频总时长: {total_seconds:.2f}秒")
        
        segment_duration = total_seconds / count
        self.append_log(f"> 每段时长: {segment_duration:.2f}秒")
        
        # 检查磁盘空间
        file_size = os.path.getsize(input_file)
        if not self.check_disk_space(output_folder, file_size * 2):
            self.append_log("> 错误：磁盘空间不足，无法进行分割")
            return False
        
        # 开始分割
        segment_num = 0
        current_time = 0
        
        for i in range(count):
            if self.worker and self.worker._is_cancelled:
                self.append_log("> 操作已被用户取消")
                return False
                
            segment_num += 1
            output_file = os.path.join(output_folder, f"count_{segment_num}.{output_format}")
            
            # 计算当前片段的持续时间
            duration = min(segment_duration, total_seconds - current_time)
            
            # 构建命令
            cmd = self.build_ffmpeg_cmd(
                input_file, output_file, 
                start_time=current_time, 
                duration=duration,
                codec_option=codec_option
            )
            
            self.append_log(f"> 生成片段 {segment_num}: {output_file}")
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False)
            
            # 检查是否成功
            if result.returncode != 0:
                error_msg = result.stderr.decode('utf-8', errors='ignore') if result.stderr else "未知错误"
                self.append_log(f"> 错误: {error_msg}")
                return False
            
            current_time += duration
            progress = min(99, int((current_time / total_seconds) * 100))
            self.worker.progress_updated.emit(progress)
        
        self.worker.progress_updated.emit(100)
        return True
    
    def split_by_size(self, input_file, output_folder, output_format, 
                     size_mb, codec_option="复制 (无重编码)"):
        """按文件大小分割视频 - 修复版本"""
        # 获取视频总时长
        total_seconds = self.get_video_duration(input_file)
        if total_seconds <= 0:
            self.append_log("> 错误：无法获取视频时长")
            return False
        
        self.append_log(f"> 视频总时长: {total_seconds:.2f}秒")
        
        # 获取视频文件大小
        file_size = os.path.getsize(input_file)
        size_bytes = size_mb * 1024 * 1024
        
        # 计算片段数量
        segment_count = int(file_size // size_bytes)
        if file_size % size_bytes > 0:
            segment_count += 1
            
        self.append_log(f"> 视频总大小: {file_size / (1024*1024):.2f}MB")
        self.append_log(f"> 将分割为 {segment_count} 个片段")
        
        # 检查磁盘空间
        if not self.check_disk_space(output_folder, file_size * 2):
            self.append_log("> 错误：磁盘空间不足，无法进行分割")
            return False
        
        # 开始分割
        current_time = 0.0
        for i in range(segment_count):
            if self.worker and self.worker._is_cancelled:
                self.append_log("> 操作已被用户取消")
                return False
                
            output_file = os.path.join(output_folder, f"size_{i+1}.{output_format}")
            
            # 构建命令 - 从当前时间点开始
            cmd = self.build_ffmpeg_cmd(
                input_file, output_file, 
                start_time=current_time,
                duration=None,  # 不指定时长，用大小限制
                codec_option=codec_option
            )
            
            # 添加文件大小限制
            cmd.insert(-1, "-fs")
            cmd.insert(-1, str(size_bytes))
            
            self.append_log(f"> 生成片段 {i+1}: {output_file}")
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False)
            
            # 检查是否成功
            if result.returncode != 0:
                error_msg = result.stderr.decode('utf-8', errors='ignore') if result.stderr else "未知错误"
                self.append_log(f"> 错误: {error_msg}")
                return False
            
            # 获取新片段的时长
            new_duration = self.get_video_duration(output_file)
            if new_duration > 0:
                current_time += new_duration
                self.append_log(f"> 片段时长: {new_duration:.2f}秒")
            else:
                # 保守估计，使用平均时长
                current_time += total_seconds / segment_count
                self.append_log("> 警告: 无法获取片段时长，使用估算值")
            
            progress = min(99, int(((i+1) / segment_count) * 100))
            self.worker.progress_updated.emit(progress)
        
        self.worker.progress_updated.emit(100)
        return True
    
    def split_by_scene(self, input_file, output_folder, output_format, 
                      threshold, min_duration, codec_option="复制 (无重编码)"):
        """按场景变化分割视频 - 优化版本"""
        # 转换阈值 (1-100 => 0.01-1.0)
        threshold_value = float(threshold) / 100.0
        
        # 获取视频总时长
        total_seconds = self.get_video_duration(input_file)
        if total_seconds <= 0:
            self.append_log("> 错误：无法获取视频时长")
            return False
        
        self.append_log(f"> 视频总时长: {total_seconds:.2f}秒")
        
        # 检查磁盘空间
        file_size = os.path.getsize(input_file)
        if not self.check_disk_space(output_folder, file_size * 2):
            self.append_log("> 错误：磁盘空间不足，无法进行分割")
            return False
        
        # 构建FFmpeg命令 - 优化参数
        cmd = [
            self.get_ffmpeg_path(),
            "-i", input_file,
            "-filter_complex", 
            f"select='gt(scene,{threshold_value})',showinfo",
            "-vsync", "0",  # 更精确的帧处理
            "-f", "null",
            "-"
        ]
        
        self.append_log("> 检测场景变化点...")
        self.append_log("> 此过程可能需要几分钟，请耐心等待...")
        
        # 运行场景检测
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False)
        
        if result.returncode != 0:
            error_msg = result.stderr.decode('utf-8', errors='ignore') if result.stderr else "未知错误"
            self.append_log(f"> 场景检测失败: {error_msg}")
            return False
        
        # 解析场景变化点
        scene_points = []
        output = result.stdout.decode('utf-8', errors='ignore')
        for line in output.splitlines():
            if "pts_time" in line:
                parts = line.split("pts_time:")[1].split(" ")[0]
                try:
                    timestamp = float(parts)
                    scene_points.append(timestamp)
                except ValueError:
                    continue
        
        if not scene_points:
            self.append_log("> 未检测到场景变化点")
            return False
        
        self.append_log(f"> 检测到 {len(scene_points)} 个场景变化点")
        
        # 添加开始和结束时间点
        scene_points.insert(0, 0)
        scene_points.append(total_seconds)
        
        # 过滤掉间隔太短的场景
        filtered_scenes = [scene_points[0]]
        for i in range(1, len(scene_points)):
            if scene_points[i] - filtered_scenes[-1] >= min_duration:
                filtered_scenes.append(scene_points[i])
        
        if len(filtered_scenes) < 2:
            self.append_log("> 没有足够的场景进行分割")
            return False
        
        self.append_log(f"> 将分割为 {len(filtered_scenes)-1} 个场景片段")
        
        # 开始分割场景
        total_scenes = len(filtered_scenes) - 1
        for i in range(total_scenes):
            if self.worker and self.worker._is_cancelled:
                self.append_log("> 操作已被用户取消")
                return False
                
            start_scene = filtered_scenes[i]
            end_scene = filtered_scenes[i+1]
            duration = end_scene - start_scene
            
            output_file = os.path.join(output_folder, f"scene_{i+1}.{output_format}")
            
            # 构建命令
            cmd = self.build_ffmpeg_cmd(
                input_file, output_file, 
                start_time=start_scene, 
                duration=duration,
                codec_option=codec_option
            )
            
            self.append_log(f"> 生成场景片段 {i+1}: {output_file}")
            self.append_log(f"> 时间范围: {start_scene:.2f}秒 - {end_scene:.2f}秒")
            
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False)
            
            # 检查是否成功
            if result.returncode != 0:
                error_msg = result.stderr.decode('utf-8', errors='ignore') if result.stderr else "未知错误"
                self.append_log(f"> 错误: {error_msg}")
                return False
            
            progress = min(99, int(((i+1) / total_scenes) * 100))
            self.worker.progress_updated.emit(progress)
        
        self.worker.progress_updated.emit(100)
        return True
