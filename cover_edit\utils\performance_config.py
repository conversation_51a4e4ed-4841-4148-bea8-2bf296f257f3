#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能配置管理器
统一管理性能相关的配置和优化设置
"""

from dataclasses import dataclass
from typing import Dict, Any
import psutil
import os

@dataclass
class PerformanceConfig:
    """性能配置类"""
    
    # 内存管理
    max_cache_size: int = 50
    memory_warning_threshold_mb: int = 500
    memory_critical_threshold_mb: int = 1000
    
    # 性能监控
    performance_warning_threshold_ms: float = 100.0
    performance_critical_threshold_ms: float = 500.0
    
    # 线程池设置
    max_worker_threads: int = 4
    thread_timeout_seconds: float = 30.0
    
    # 视频处理
    video_cache_frames: int = 10
    max_video_resolution: tuple = (4096, 4096)
    
    # 图像处理
    image_quality_jpeg: int = 95
    image_compression_png: int = 6
    max_image_size_mb: int = 50
    
    # 批量处理
    batch_size_limit: int = 50
    batch_check_interval_ms: int = 2000
    
    # FFmpeg设置
    ffmpeg_timeout_seconds: int = 300
    ffmpeg_buffer_size: str = "32M"

class PerformanceManager:
    """性能管理器"""
    
    def __init__(self):
        self.config = PerformanceConfig()
        self._auto_adjust_config()
    
    def _auto_adjust_config(self):
        """根据系统配置自动调整性能参数"""
        try:
            # 获取系统信息
            memory_gb = psutil.virtual_memory().total / (1024**3)
            cpu_count = os.cpu_count() or 4
            
            # 根据内存调整缓存大小
            if memory_gb >= 16:
                self.config.max_cache_size = 100
                self.config.video_cache_frames = 20
                self.config.memory_warning_threshold_mb = 1000
                self.config.memory_critical_threshold_mb = 2000
            elif memory_gb >= 8:
                self.config.max_cache_size = 75
                self.config.video_cache_frames = 15
                self.config.memory_warning_threshold_mb = 750
                self.config.memory_critical_threshold_mb = 1500
            else:
                self.config.max_cache_size = 25
                self.config.video_cache_frames = 5
                self.config.memory_warning_threshold_mb = 250
                self.config.memory_critical_threshold_mb = 500
            
            # 根据CPU核心数调整线程数
            self.config.max_worker_threads = min(max(2, cpu_count - 1), 8)
            
            # 根据系统性能调整批量处理
            if cpu_count >= 8 and memory_gb >= 16:
                self.config.batch_size_limit = 100
                self.config.batch_check_interval_ms = 1000
            elif cpu_count >= 4 and memory_gb >= 8:
                self.config.batch_size_limit = 75
                self.config.batch_check_interval_ms = 1500
            else:
                self.config.batch_size_limit = 25
                self.config.batch_check_interval_ms = 3000
                
        except Exception:
            # 如果获取系统信息失败，使用默认配置
            pass
    
    def get_config(self) -> PerformanceConfig:
        """获取性能配置"""
        return self.config
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量（MB）"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0
    
    def is_memory_warning(self) -> bool:
        """检查是否达到内存警告阈值"""
        return self.get_memory_usage_mb() > self.config.memory_warning_threshold_mb
    
    def is_memory_critical(self) -> bool:
        """检查是否达到内存严重警告阈值"""
        return self.get_memory_usage_mb() > self.config.memory_critical_threshold_mb
    
    def get_optimal_thread_count(self, task_type: str = "default") -> int:
        """获取最优线程数"""
        base_threads = self.config.max_worker_threads
        
        # 根据任务类型调整
        if task_type == "video_processing":
            # 视频处理通常是CPU密集型，使用较少线程
            return max(1, base_threads // 2)
        elif task_type == "image_processing":
            # 图像处理可以使用更多线程
            return base_threads
        elif task_type == "io_operations":
            # IO操作可以使用更多线程
            return min(base_threads * 2, 16)
        else:
            return base_threads
    
    def should_use_cache(self, cache_size: int) -> bool:
        """判断是否应该使用缓存"""
        return cache_size < self.config.max_cache_size and not self.is_memory_critical()
    
    def get_ffmpeg_args(self) -> Dict[str, Any]:
        """获取FFmpeg优化参数"""
        return {
            'timeout': self.config.ffmpeg_timeout_seconds,
            'buffer_size': self.config.ffmpeg_buffer_size,
            'threads': self.get_optimal_thread_count("video_processing")
        }

# 全局性能管理器实例
_performance_manager = PerformanceManager()

# 便捷函数
def get_performance_config() -> PerformanceConfig:
    """获取性能配置"""
    return _performance_manager.get_config()

def get_memory_usage_mb() -> float:
    """获取内存使用量"""
    return _performance_manager.get_memory_usage_mb()

def is_memory_warning() -> bool:
    """检查内存警告"""
    return _performance_manager.is_memory_warning()

def is_memory_critical() -> bool:
    """检查内存严重警告"""
    return _performance_manager.is_memory_critical()

def get_optimal_thread_count(task_type: str = "default") -> int:
    """获取最优线程数"""
    return _performance_manager.get_optimal_thread_count(task_type)

def should_use_cache(cache_size: int) -> bool:
    """判断是否使用缓存"""
    return _performance_manager.should_use_cache(cache_size)

def get_ffmpeg_args() -> Dict[str, Any]:
    """获取FFmpeg参数"""
    return _performance_manager.get_ffmpeg_args()
