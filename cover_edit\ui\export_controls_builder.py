#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出控制构建器
负责构建导出相关的UI组件，确保与原模块完全一致
"""

from PyQt6.QtWidgets import QPushButton, QProgressBar, QLabel
from PyQt6.QtCore import Qt

# 导入常量
from cover_edit.utils.constants import CoverEditConstants

class ExportControlsBuilder:
    """导出控制构建器 - 确保与原模块布局完全一致"""
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
    
    def build_export_controls(self):
        """
        构建导出控制组件
        🔒 保证：与原模块的导出控制构建产生完全相同的布局
        """
        try:
            self.logger.debug("开始构建导出控制")
            
            # 🔒 导出按钮 - 与原模块完全一致
            self._build_export_button()
            
            # 🔒 进度条 - 与原模块完全一致
            self._build_progress_bar()
            
            self.logger.debug("导出控制构建完成")
            
        except Exception as e:
            self.logger.error(f"构建导出控制失败: {e}")
            raise
    
    def _build_export_button(self):
        """构建导出按钮"""
        
        # 🔒 导出视频按钮 - 与原模块完全一致
        self.parent.export_btn = QPushButton("导出视频")
        self.parent.export_btn.setFixedSize(120, 35)
        self.parent.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #555;
                color: #999;
            }
        """)
        self.parent.export_btn.setEnabled(False)  # 初始状态禁用
    
    def _build_progress_bar(self):
        """构建进度条"""
        
        # 🔒 导出进度条 - 与原模块完全一致
        self.parent.export_progress = QProgressBar()
        self.parent.export_progress.setVisible(False)  # 初始状态隐藏
        self.parent.export_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 5px;
                text-align: center;
                background-color: #2b2b2b;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        
        # 🔒 进度标签 - 与原模块完全一致
        self.parent.export_status_label = QLabel("")
        self.parent.export_status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                padding: 5px;
            }
        """)
        self.parent.export_status_label.setVisible(False)  # 初始状态隐藏
