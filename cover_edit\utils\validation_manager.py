#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证管理器 - 增强数据验证和错误处理
"""

import os
import cv2
from typing import Any, Dict, List, Optional, Tuple, Union
from pathlib import Path
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import QObject, pyqtSignal

from .logger import get_logger
from .decorators import error_handler
from .constants import CoverEditConstants


class ValidationResult:
    """验证结果类"""
    
    def __init__(self, is_valid: bool = True, message: str = "", details: Dict = None):
        self.is_valid = is_valid
        self.message = message
        self.details = details or {}
        self.warnings = []
    
    def add_warning(self, warning: str):
        """添加警告"""
        self.warnings.append(warning)
    
    def __bool__(self):
        return self.is_valid
    
    def __str__(self):
        return f"ValidationResult(valid={self.is_valid}, message='{self.message}')"


class ValidationManager(QObject):
    """验证管理器"""
    
    # 信号定义
    validation_warning = pyqtSignal(str)  # 验证警告
    validation_error = pyqtSignal(str)    # 验证错误
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("ValidationManager")
        
        # 验证配置
        self.validation_config = {
            'max_video_size_mb': 2048,      # 最大视频文件大小
            'max_image_size_mb': 100,       # 最大图像文件大小
            'min_video_duration': 0.1,      # 最小视频时长（秒）
            'max_video_duration': 7200,     # 最大视频时长（秒）
            'min_resolution': (64, 64),     # 最小分辨率
            'max_resolution': (7680, 4320), # 最大分辨率（8K）
            'supported_video_codecs': ['h264', 'h265', 'vp9', 'av1'],
            'supported_audio_codecs': ['aac', 'mp3', 'opus']
        }
    
    @error_handler(show_dialog=False)
    def validate_video_info(self, parent_widget) -> ValidationResult:
        """验证视频信息的完整性和有效性"""
        try:
            result = ValidationResult()
            
            # 检查基本属性存在性
            required_attrs = [
                'video_aspect_ratio', 'video_width', 'video_height',
                'fps', 'total_frames', 'video_duration'
            ]
            
            missing_attrs = []
            for attr in required_attrs:
                if not hasattr(parent_widget, attr):
                    missing_attrs.append(attr)
            
            if missing_attrs:
                result.is_valid = False
                result.message = f"缺少视频属性: {', '.join(missing_attrs)}"
                return result
            
            # 检查属性值的有效性
            width = getattr(parent_widget, 'video_width', 0)
            height = getattr(parent_widget, 'video_height', 0)
            fps = getattr(parent_widget, 'fps', 0)
            duration = getattr(parent_widget, 'video_duration', 0)
            aspect_ratio = getattr(parent_widget, 'video_aspect_ratio', 0)
            
            # 验证分辨率
            if width <= 0 or height <= 0:
                result.is_valid = False
                result.message = f"无效的视频分辨率: {width}x{height}"
                return result
            
            min_w, min_h = self.validation_config['min_resolution']
            max_w, max_h = self.validation_config['max_resolution']
            
            if width < min_w or height < min_h:
                result.add_warning(f"视频分辨率过低: {width}x{height} (最小: {min_w}x{min_h})")
            
            if width > max_w or height > max_h:
                result.add_warning(f"视频分辨率过高: {width}x{height} (最大: {max_w}x{max_h})")
            
            # 验证帧率
            if fps <= 0 or fps > 120:
                result.add_warning(f"异常的帧率: {fps} FPS")
            
            # 验证时长
            min_duration = self.validation_config['min_video_duration']
            max_duration = self.validation_config['max_video_duration']
            
            if duration < min_duration:
                result.is_valid = False
                result.message = f"视频时长过短: {duration:.1f}秒 (最小: {min_duration}秒)"
                return result
            
            if duration > max_duration:
                result.add_warning(f"视频时长过长: {duration:.1f}秒 (最大: {max_duration}秒)")
            
            # 验证宽高比
            calculated_ratio = width / height if height > 0 else 0
            if abs(calculated_ratio - aspect_ratio) > 0.01:
                result.add_warning(f"宽高比不一致: 计算值={calculated_ratio:.3f}, 存储值={aspect_ratio:.3f}")
            
            # 设置详细信息
            result.details = {
                'resolution': f"{width}x{height}",
                'fps': fps,
                'duration': duration,
                'aspect_ratio': aspect_ratio
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证视频信息失败: {e}")
            return ValidationResult(False, f"验证失败: {str(e)}")
    
    @error_handler(show_dialog=False)
    def validate_video_file(self, file_path: str) -> ValidationResult:
        """验证视频文件"""
        try:
            result = ValidationResult()
            
            # 检查文件存在性
            if not os.path.exists(file_path):
                result.is_valid = False
                result.message = f"视频文件不存在: {file_path}"
                return result
            
            # 检查文件大小
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            max_size_mb = self.validation_config['max_video_size_mb']
            
            if file_size_mb > max_size_mb:
                result.add_warning(f"视频文件过大: {file_size_mb:.1f}MB (最大: {max_size_mb}MB)")
            
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in CoverEditConstants.SUPPORTED_VIDEO_FORMATS:
                result.is_valid = False
                result.message = f"不支持的视频格式: {file_ext}"
                return result
            
            # 使用OpenCV验证视频
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                result.is_valid = False
                result.message = "无法打开视频文件"
                return result
            
            try:
                # 获取视频信息
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                # 验证基本信息
                if width <= 0 or height <= 0:
                    result.is_valid = False
                    result.message = f"无效的视频分辨率: {width}x{height}"
                    return result
                
                if fps <= 0:
                    result.add_warning(f"异常的帧率: {fps}")
                
                if frame_count <= 0:
                    result.add_warning(f"异常的帧数: {frame_count}")
                
                # 尝试读取第一帧
                ret, frame = cap.read()
                if not ret:
                    result.add_warning("无法读取视频帧")
                
                # 设置详细信息
                result.details = {
                    'file_size_mb': file_size_mb,
                    'resolution': f"{width}x{height}",
                    'fps': fps,
                    'frame_count': frame_count,
                    'duration': frame_count / fps if fps > 0 else 0
                }
                
            finally:
                cap.release()
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证视频文件失败: {e}")
            return ValidationResult(False, f"验证失败: {str(e)}")
    
    @error_handler(show_dialog=False)
    def validate_image_file(self, file_path: str) -> ValidationResult:
        """验证图像文件"""
        try:
            result = ValidationResult()
            
            # 检查文件存在性
            if not os.path.exists(file_path):
                result.is_valid = False
                result.message = f"图像文件不存在: {file_path}"
                return result
            
            # 检查文件大小
            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            max_size_mb = self.validation_config['max_image_size_mb']
            
            if file_size_mb > max_size_mb:
                result.add_warning(f"图像文件过大: {file_size_mb:.1f}MB (最大: {max_size_mb}MB)")
            
            # 检查文件格式
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in CoverEditConstants.SUPPORTED_IMAGE_FORMATS:
                result.is_valid = False
                result.message = f"不支持的图像格式: {file_ext}"
                return result
            
            # 使用QPixmap验证图像
            pixmap = QPixmap(file_path)
            if pixmap.isNull():
                result.is_valid = False
                result.message = "无法加载图像文件"
                return result
            
            # 验证分辨率
            width = pixmap.width()
            height = pixmap.height()
            
            min_w, min_h = self.validation_config['min_resolution']
            max_w, max_h = self.validation_config['max_resolution']
            
            if width < min_w or height < min_h:
                result.add_warning(f"图像分辨率过低: {width}x{height} (最小: {min_w}x{min_h})")
            
            if width > max_w or height > max_h:
                result.add_warning(f"图像分辨率过高: {width}x{height} (最大: {max_w}x{max_h})")
            
            # 设置详细信息
            result.details = {
                'file_size_mb': file_size_mb,
                'resolution': f"{width}x{height}",
                'aspect_ratio': width / height if height > 0 else 0
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证图像文件失败: {e}")
            return ValidationResult(False, f"验证失败: {str(e)}")
    
    @error_handler(show_dialog=False)
    def validate_export_params(self, params: Dict) -> ValidationResult:
        """验证导出参数"""
        try:
            result = ValidationResult()
            
            # 检查必需参数
            required_params = ['video_path', 'output_path', 'cover_image']
            missing_params = []
            
            for param in required_params:
                if param not in params or params[param] is None:
                    missing_params.append(param)
            
            if missing_params:
                result.is_valid = False
                result.message = f"缺少导出参数: {', '.join(missing_params)}"
                return result
            
            # 验证视频路径
            video_path = params['video_path']
            if not os.path.exists(video_path):
                result.is_valid = False
                result.message = f"视频文件不存在: {video_path}"
                return result
            
            # 验证输出路径
            output_path = params['output_path']
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                result.is_valid = False
                result.message = f"输出目录不存在: {output_dir}"
                return result
            
            # 检查输出目录写权限
            if not os.access(output_dir, os.W_OK):
                result.is_valid = False
                result.message = f"输出目录无写权限: {output_dir}"
                return result
            
            # 验证封面图像
            cover_image = params['cover_image']
            if isinstance(cover_image, QPixmap) and cover_image.isNull():
                result.is_valid = False
                result.message = "封面图像无效"
                return result
            
            # 验证帧范围
            start_frame = params.get('start_frame', 0)
            end_frame = params.get('end_frame', 0)
            
            if start_frame < 0:
                result.add_warning(f"起始帧号无效: {start_frame}")
            
            if end_frame <= start_frame:
                result.add_warning(f"结束帧号无效: {end_frame} (起始: {start_frame})")
            
            # 验证帧率
            fps = params.get('fps', 30.0)
            if fps <= 0 or fps > 120:
                result.add_warning(f"帧率异常: {fps}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证导出参数失败: {e}")
            return ValidationResult(False, f"验证失败: {str(e)}")
    
    def update_validation_config(self, **kwargs):
        """更新验证配置"""
        for key, value in kwargs.items():
            if key in self.validation_config:
                self.validation_config[key] = value
                self.logger.debug(f"更新验证配置: {key} = {value}")


# 全局验证管理器实例
_validation_manager = ValidationManager()

# 便捷函数
def validate_video_info(parent_widget) -> ValidationResult:
    """验证视频信息"""
    return _validation_manager.validate_video_info(parent_widget)

def validate_video_file(file_path: str) -> ValidationResult:
    """验证视频文件"""
    return _validation_manager.validate_video_file(file_path)

def validate_image_file(file_path: str) -> ValidationResult:
    """验证图像文件"""
    return _validation_manager.validate_image_file(file_path)

def validate_export_params(params: Dict) -> ValidationResult:
    """验证导出参数"""
    return _validation_manager.validate_export_params(params)
