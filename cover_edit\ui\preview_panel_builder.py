#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预览面板构建器
负责构建预览相关的UI组件，确保与原模块完全一致
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QSlider, QPushButton
from PyQt6.QtCore import Qt
from PyQt6.QtMultimediaWidgets import QVideoWidget

# 导入常量和原有组件
from cover_edit.utils.constants import CoverEditConstants

class PreviewPanelBuilder:
    """预览面板构建器 - 确保与原模块布局完全一致"""

    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger

    def create_preview_content(self):
        """创建预览内容 - 🚀 新增：为主界面构建器提供预览内容"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout

        # 创建预览内容容器
        preview_content = QWidget()
        layout = QVBoxLayout(preview_content)
        layout.setContentsMargins(0, 0, 0, 0)

        # 构建预览面板内容
        self.parent.left_layout = layout  # 临时设置，供构建方法使用
        self.build_preview_panels()

        return preview_content
    
    def build_preview_panels(self):
        """
        构建预览面板 - 🚀 重构：真正承担UI创建工作
        🔒 保证：与原模块的预览面板构建产生完全相同的布局
        """
        try:
            self.logger.debug("开始构建预览面板")

            # 🚀 重构：获取左侧面板布局
            left_layout = None
            if hasattr(self.parent, 'left_layout') and self.parent.left_layout:
                left_layout = self.parent.left_layout
            elif hasattr(self.parent, 'left_panel') and self.parent.left_panel:
                left_layout = self.parent.left_panel.layout()

            if left_layout:
                # 创建预览区域
                self._create_preview_area(left_layout)
                # 创建控制按钮
                self._create_control_buttons(left_layout)
                # 创建控制面板
                self._create_control_panels(left_layout)
            else:
                self.logger.warning("未找到左侧面板布局，跳过预览面板构建")

            self.logger.debug("预览面板构建完成")

        except Exception as e:
            self.logger.error(f"构建预览面板失败: {e}")
            raise

    def _create_preview_area(self, layout):
        """创建预览区域 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QStackedWidget, QSizePolicy
        from PyQt6.QtCore import Qt

        # 视频预览区域 - 使用堆叠窗口
        self.parent.preview_stack = QStackedWidget()
        self.parent.preview_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 截图模式预览
        from cover_edit.ui.preview_label import PreviewLabel
        self.parent.snapshot_preview = PreviewLabel()
        self.parent.snapshot_preview.setMinimumSize(500, 300)
        self.parent.snapshot_preview.setStyleSheet(self._get_preview_style())
        self.parent.snapshot_preview.setText("请加载视频文件")
        self.parent.snapshot_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 播放模式预览
        self.parent.playback_widget = self._create_playback_widget()

        # 添加到堆叠窗口
        self.parent.preview_stack.addWidget(self.parent.snapshot_preview)
        self.parent.preview_stack.addWidget(self.parent.playback_widget)

        layout.addWidget(self.parent.preview_stack)

    def _create_playback_widget(self):
        """创建播放模式组件 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout
        from PyQt6.QtMultimediaWidgets import QVideoWidget
        from PyQt6.QtCore import Qt

        playback_widget = QWidget()
        playback_layout = QVBoxLayout(playback_widget)
        playback_layout.setContentsMargins(0, 0, 0, 0)
        playback_layout.setSpacing(0)

        # 视频播放器容器
        self.parent.video_container = QWidget()
        video_container_layout = QVBoxLayout(self.parent.video_container)
        video_container_layout.setContentsMargins(0, 0, 0, 0)
        video_container_layout.setSpacing(0)

        # 视频播放器
        self.parent.video_widget = QVideoWidget()
        self.parent.video_widget.setAspectRatioMode(Qt.AspectRatioMode.KeepAspectRatio)
        self.parent.video_widget.setMinimumSize(500, 300)
        self.parent.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: #181A1F;
                border: 2px dashed #5C6370;
                border-radius: 6px;
            }
        """)

        video_container_layout.addWidget(self.parent.video_widget)
        playback_layout.addWidget(self.parent.video_container)

        return playback_widget

    def _create_control_buttons(self, layout):
        """创建控制按钮 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QHBoxLayout, QButtonGroup

        # 模式按钮行
        mode_button_layout = QHBoxLayout()
        mode_button_layout.setContentsMargins(0, 5, 0, 5)
        mode_button_layout.setSpacing(10)

        # 创建按钮组确保单选行为
        self.parent.mode_button_group = QButtonGroup(self.parent)

        # 创建各种按钮
        self._create_mode_buttons(mode_button_layout)
        self._create_action_buttons(mode_button_layout)

        layout.addLayout(mode_button_layout)

    def _create_mode_buttons(self, layout):
        """创建模式切换按钮 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QPushButton

        button_style = self._get_button_style()

        # 播放模式按钮
        self.parent.play_mode_btn = QPushButton("播放模式")
        self.parent.play_mode_btn.setCheckable(True)
        self.parent.play_mode_btn.setFixedWidth(120)
        self.parent.play_mode_btn.setStyleSheet(button_style['mode'])
        # 信号连接将由信号管理器统一处理

        # 截图模式按钮
        self.parent.snapshot_mode_btn = QPushButton("截图模式")
        self.parent.snapshot_mode_btn.setCheckable(True)
        self.parent.snapshot_mode_btn.setChecked(True)  # 默认选中
        self.parent.snapshot_mode_btn.setFixedWidth(120)
        self.parent.snapshot_mode_btn.setStyleSheet(button_style['mode'])
        # 信号连接将由信号管理器统一处理

        # 添加到按钮组
        self.parent.mode_button_group.addButton(self.parent.play_mode_btn, 0)
        self.parent.mode_button_group.addButton(self.parent.snapshot_mode_btn, 1)

        layout.addWidget(self.parent.play_mode_btn)
        layout.addWidget(self.parent.snapshot_mode_btn)

    def _create_action_buttons(self, layout):
        """创建操作按钮 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QPushButton

        button_style = self._get_button_style()

        # 截取帧按钮
        self.parent.capture_frame_btn = QPushButton("截取当前帧")
        self.parent.capture_frame_btn.setFixedWidth(120)
        self.parent.capture_frame_btn.setStyleSheet(button_style['action'])
        self.parent.capture_frame_btn.clicked.connect(self.parent.capture_current_frame)
        self.parent.capture_frame_btn.setEnabled(True)  # 默认启用

        # 导入封面按钮
        self.parent.import_cover_btn = QPushButton("导入封面")
        self.parent.import_cover_btn.setFixedWidth(120)
        self.parent.import_cover_btn.setStyleSheet(button_style['import'])
        self.parent.import_cover_btn.clicked.connect(self.parent.import_cover_image)

        layout.addWidget(self.parent.capture_frame_btn)
        layout.addWidget(self.parent.import_cover_btn)

    def _create_control_panels(self, layout):
        """创建控制面板 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QStackedWidget, QSizePolicy

        # 控制面板堆叠区域
        self.parent.control_stack = QStackedWidget()
        self.parent.control_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.parent.control_stack.setMaximumHeight(60)

        # 创建播放和截图控制面板
        play_control_panel = self._create_play_control_panel()
        snapshot_control_panel = self._create_snapshot_control_panel()

        self.parent.control_stack.addWidget(play_control_panel)
        self.parent.control_stack.addWidget(snapshot_control_panel)
        self.parent.control_stack.setCurrentIndex(1)  # 默认显示截图控制面板

        layout.addWidget(self.parent.control_stack)

        # 添加视频信息组
        info_group = self._create_video_info_group()
        layout.addWidget(info_group)

    def _create_play_control_panel(self):
        """创建播放控制面板 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QSlider, QLabel
        from PyQt6.QtCore import Qt

        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 播放/暂停按钮
        self.parent.play_pause_btn = QPushButton("播放")
        self.parent.play_pause_btn.setFixedSize(80, 40)
        self.parent.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
        """)
        self.parent.play_pause_btn.clicked.connect(self.parent.toggle_playback)

        # 进度条
        self.parent.playback_slider = QSlider(Qt.Orientation.Horizontal)
        self.parent.playback_slider.setMinimum(0)
        self.parent.playback_slider.setMaximum(100)
        self.parent.playback_slider.setValue(0)
        self.parent.playback_slider.setStyleSheet(self._get_slider_style("#4CAF50"))

        # 时间标签
        self.parent.time_label = QLabel("00:00 / 00:00")
        self.parent.time_label.setMinimumWidth(100)
        self.parent.time_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.parent.play_pause_btn)
        layout.addWidget(self.parent.playback_slider, 1)
        layout.addWidget(self.parent.time_label)

        return panel

    def _create_snapshot_control_panel(self):
        """创建截图控制面板 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QWidget, QHBoxLayout, QSlider, QLabel
        from PyQt6.QtCore import Qt

        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 帧滑块
        self.parent.frame_slider = QSlider(Qt.Orientation.Horizontal)
        self.parent.frame_slider.setMinimum(0)
        self.parent.frame_slider.setMaximum(100)
        self.parent.frame_slider.setValue(0)
        self.parent.frame_slider.setStyleSheet(self._get_slider_style("#FF9800"))
        self.parent.frame_slider.valueChanged.connect(self.parent.on_frame_slider_changed)

        # 帧数显示
        self.parent.frame_label = QLabel("帧: 0/0")
        self.parent.frame_label.setMinimumWidth(100)
        self.parent.frame_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.parent.frame_slider, 1)
        layout.addWidget(self.parent.frame_label)

        return panel

    def _get_slider_style(self, color):
        """获取滑块样式 - 🚀 从主模块移动过来"""
        return f"""
            QSlider::groove:horizontal {{
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {color};
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::handle:horizontal:hover {{
                background: {color};
                opacity: 0.8;
            }}
        """

    def _create_video_info_group(self):
        """创建视频信息组 - 🚀 从主模块移动过来"""
        from PyQt6.QtWidgets import QGroupBox, QGridLayout, QLabel, QLineEdit, QPushButton

        info_group = QGroupBox("视频信息与裁剪设置")
        info_layout = QGridLayout(info_group)
        info_layout.setContentsMargins(10, 15, 10, 10)
        info_layout.setSpacing(8)

        # 视频路径
        info_layout.addWidget(QLabel("视频文件:"), 0, 0)
        self.parent.video_path_edit = QLineEdit()
        self.parent.video_path_edit.setReadOnly(True)
        self.parent.video_path_edit.setPlaceholderText("请选择视频文件...")
        info_layout.addWidget(self.parent.video_path_edit, 0, 1, 1, 2)

        self.parent.browse_video_btn = QPushButton("浏览")
        self.parent.browse_video_btn.setFixedWidth(80)
        self.parent.browse_video_btn.clicked.connect(self.parent.load_video)
        info_layout.addWidget(self.parent.browse_video_btn, 0, 3)

        # 视频信息
        info_layout.addWidget(QLabel("分辨率:"), 1, 0)
        self.parent.resolution_label = QLabel("未知")
        info_layout.addWidget(self.parent.resolution_label, 1, 1)

        info_layout.addWidget(QLabel("时长:"), 1, 2)
        self.parent.duration_label = QLabel("未知")
        info_layout.addWidget(self.parent.duration_label, 1, 3)

        # 裁剪设置
        info_layout.addWidget(QLabel("裁剪开头:"), 2, 0)
        self.parent.crop_start_input = QLineEdit("0")
        self.parent.crop_start_input.setFixedWidth(60)
        self.parent.crop_start_input.textChanged.connect(self.parent.on_crop_settings_changed)
        info_layout.addWidget(self.parent.crop_start_input, 2, 1)

        info_layout.addWidget(QLabel("裁剪结尾:"), 2, 2)
        self.parent.crop_end_input = QLineEdit("0")
        self.parent.crop_end_input.setFixedWidth(60)
        self.parent.crop_end_input.textChanged.connect(self.parent.on_crop_settings_changed)
        info_layout.addWidget(self.parent.crop_end_input, 2, 3)

        return info_group

    def _get_button_style(self):
        """获取按钮样式 - 🚀 从主模块移动过来"""
        return {
            'mode': """
                QPushButton {
                    background-color: #3A3F4B;
                    color: #ABB2BF;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:checked {
                    background-color: #61AFEF;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4A4F5B;
                }
            """,
            'action': """
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """,
            'smart': """
                QPushButton {
                    background-color: #9C27B0;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7B1FA2;
                }
            """,
            'import': """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #43A047;
                }
            """
        }

    def _get_preview_style(self) -> str:
        """获取预览样式 - 与原模块完全一致"""
        return """
            QLabel {
                background-color: #181A1F;
                border: 2px dashed #5C6370;
                border-radius: 6px;
                color: #5C6370;
                font-size: 16px;
                font-style: italic;
                padding: 0px;
            }
        """

    def _create_playback_widget(self) -> QWidget:
        """创建播放模式组件 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout
        from PyQt6.QtMultimediaWidgets import QVideoWidget

        playback_widget = QWidget()
        playback_layout = QVBoxLayout(playback_widget)
        playback_layout.setContentsMargins(0, 0, 0, 0)
        playback_layout.setSpacing(0)

        # 🔒 视频播放器容器 - 与原模块完全一致
        self.parent.video_container = QWidget()
        video_container_layout = QVBoxLayout(self.parent.video_container)
        video_container_layout.setContentsMargins(0, 0, 0, 0)
        video_container_layout.setSpacing(0)

        # 🔒 视频播放器组件 - 与原模块完全一致
        self.parent.video_widget = QVideoWidget()
        from PyQt6.QtCore import Qt
        self.parent.video_widget.setAspectRatioMode(Qt.AspectRatioMode.KeepAspectRatio)
        self.parent.video_widget.setMinimumSize(500, 300)
        self.parent.video_widget.setStyleSheet(CoverEditConstants.VIDEO_WIDGET_STYLE)

        video_container_layout.addWidget(self.parent.video_widget)
        playback_layout.addWidget(self.parent.video_container)

        return playback_widget

    def _create_cover_preview(self):
        """创建封面预览 - 与原模块完全一致"""
        # 这个方法将在右侧面板构建器中实现
        # 这里只是占位符，实际的封面预览在右侧面板中创建
        pass

    def _ensure_component_references(self):
        """确保组件引用正确传递"""
        # 这个方法确保所有创建的组件都能被外部访问
        # 组件已经在创建时直接赋值给self.parent，所以这里不需要额外操作
        pass

    def _build_left_preview_panel(self):
        """构建左侧预览面板 - 视频预览"""

        # 🚀 修复：使用已经创建的左侧面板，而不是创建新的
        central_widget = self.parent.centralWidget()
        if not (central_widget and hasattr(central_widget, 'left_panel')):
            self.logger.error("左侧面板不存在，无法构建预览")
            return

        left_panel = central_widget.left_panel
        left_layout = left_panel.layout()

        # 🔒 视频预览区域 - 与原模块完全一致
        video_preview_container = QWidget()
        video_preview_layout = QVBoxLayout(video_preview_container)
        video_preview_layout.setContentsMargins(0, 0, 0, 0)

        # 🔒 创建视频预览标签 - 与原模块完全一致
        # 🚀 修复：使用完整的PreviewLabel
        from .preview_label import PreviewLabel

        self.parent.video_preview = PreviewLabel()
        self.parent.video_preview.setMinimumSize(500, 300)
        self.parent.video_preview.setStyleSheet(self._get_preview_style())

        # 🔒 设置文本 - 兼容QLabel和PreviewLabel
        if hasattr(self.parent.video_preview, 'setText'):
            self.parent.video_preview.setText("请先导入视频\n点击下方按钮选择视频文件")

        video_preview_layout.addWidget(self.parent.video_preview)

        # 🔒 视频控制区域 - 与原模块完全一致
        self._build_video_controls(video_preview_layout)

        # 🚀 修复：只创建预览堆叠窗口，不创建模式按钮（由控制面板创建）
        self._create_preview_stack_only(left_layout, video_preview_container)

        # 🚀 修复：在视频预览区下方添加操作按钮 - 与原模块完全一致
        self._create_action_buttons_row(left_layout)

        # 🚀 第一步：添加视频信息组
        self._create_video_info_group(left_layout)
    
    def _build_right_preview_panel(self):
        """构建右侧预览面板 - 封面预览"""

        # 🚀 修复：使用已经创建的右侧面板，而不是创建新的
        central_widget = self.parent.centralWidget()
        if not (central_widget and hasattr(central_widget, 'right_panel')):
            self.logger.error("右侧面板不存在，无法构建预览")
            return

        right_panel = central_widget.right_panel
        right_layout = right_panel.layout()

        # 🔒 导入PreviewLabel - 确保在方法内可用
        from .preview_label import PreviewLabel

        # 🔒 封面预览区域 - 与原模块完全一致
        self.parent.cover_preview = PreviewLabel()
        self.parent.cover_preview.setStyleSheet(self._get_preview_style())
        self.parent.cover_preview.setMinimumSize(400, 600)  # 9:16比例

        # 🔒 设置文本 - 兼容QLabel和PreviewLabel
        if hasattr(self.parent.cover_preview, 'setText'):
            self.parent.cover_preview.setText("请先导入视频\n系统将自动匹配合适的封面")

        # 🚀 修复：添加到已存在的右侧布局
        right_layout.addWidget(self.parent.cover_preview)

    def _build_video_controls(self, parent_layout):
        """构建视频控制组件"""
        from PyQt6.QtWidgets import QHBoxLayout, QPushButton

        # 🔒 播放控制按钮 - 与原模块完全一致
        controls_layout = QHBoxLayout()

        self.parent.play_pause_btn = QPushButton("播放")
        self.parent.play_pause_btn.setFixedSize(80, 30)
        self.parent.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        controls_layout.addWidget(self.parent.play_pause_btn)
        controls_layout.addStretch()

        parent_layout.addLayout(controls_layout)

    def _create_preview_stack_only(self, left_layout, video_preview_container):
        """只创建预览堆叠窗口 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QStackedWidget, QSizePolicy

        # 🔒 创建预览堆叠窗口 - 与原模块完全一致
        self.parent.preview_stack = QStackedWidget()
        self.parent.preview_stack.setSizePolicy(
            QSizePolicy.Policy.Expanding,
            QSizePolicy.Policy.Expanding
        )

        # 🚀 修复：使用传入的video_preview_container作为截图模式预览
        self.parent.preview_stack.addWidget(video_preview_container)

        # 创建播放模式预览
        playback_widget = self._create_playback_widget()
        self.parent.preview_stack.addWidget(playback_widget)

        # 默认显示截图模式
        self.parent.preview_stack.setCurrentIndex(0)

        # 添加到左侧布局
        left_layout.addWidget(self.parent.preview_stack)

    def _create_playback_widget(self):
        """创建播放模式组件 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout
        from PyQt6.QtMultimediaWidgets import QVideoWidget
        from PyQt6.QtCore import Qt

        playback_widget = QWidget()
        playback_layout = QVBoxLayout(playback_widget)
        playback_layout.setContentsMargins(0, 0, 0, 0)
        playback_layout.setSpacing(0)

        # 视频播放器容器
        video_container = QWidget()
        video_container_layout = QVBoxLayout(video_container)
        video_container_layout.setContentsMargins(0, 0, 0, 0)
        video_container_layout.setSpacing(0)

        # 视频播放器
        self.parent.video_widget = QVideoWidget()
        self.parent.video_widget.setAspectRatioMode(Qt.AspectRatioMode.KeepAspectRatio)
        self.parent.video_widget.setMinimumSize(500, 300)
        self.parent.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: #2B2B2B;
                border: 1px solid #3C3C3C;
            }
        """)

        video_container_layout.addWidget(self.parent.video_widget)
        playback_layout.addWidget(video_container)

        return playback_widget

    def _create_video_info_group(self, left_layout):
        """创建视频信息组 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QGroupBox, QGridLayout, QLabel, QLineEdit, QPushButton

        # 🔒 视频信息组框 - 与原模块完全一致
        info_group = QGroupBox("视频信息")
        info_layout = QGridLayout(info_group)
        info_layout.setContentsMargins(10, 15, 10, 10)
        info_layout.setSpacing(8)

        # 🔒 视频路径行 - 与原模块完全一致
        info_layout.addWidget(QLabel("视频文件:"), 0, 0)

        self.parent.video_path_edit = QLineEdit()
        self.parent.video_path_edit.setReadOnly(True)
        self.parent.video_path_edit.setPlaceholderText("请选择视频文件...")
        info_layout.addWidget(self.parent.video_path_edit, 0, 1, 1, 2)

        # 🔒 浏览按钮 - 与原模块完全一致
        self.parent.browse_video_btn = QPushButton("浏览")
        self.parent.browse_video_btn.setFixedWidth(80)
        # 信号连接将在主模块中统一处理
        info_layout.addWidget(self.parent.browse_video_btn, 0, 3)

        # 🔒 视频信息显示 - 与原模块完全一致
        info_layout.addWidget(QLabel("分辨率:"), 1, 0)
        self.parent.resolution_label = QLabel("未知")
        self.parent.resolution_label.setStyleSheet("color: #98C379;")
        info_layout.addWidget(self.parent.resolution_label, 1, 1)

        info_layout.addWidget(QLabel("时长:"), 1, 2)
        self.parent.duration_label = QLabel("未知")
        self.parent.duration_label.setStyleSheet("color: #98C379;")
        info_layout.addWidget(self.parent.duration_label, 1, 3)

        info_layout.addWidget(QLabel("帧率:"), 2, 0)
        self.parent.fps_label = QLabel("未知")
        self.parent.fps_label.setStyleSheet("color: #98C379;")
        info_layout.addWidget(self.parent.fps_label, 2, 1)

        # 🔒 裁剪设置 - 与原模块完全一致
        info_layout.addWidget(QLabel("开头裁剪(帧):"), 3, 0)
        self.parent.crop_start_input = QLineEdit("0")
        self.parent.crop_start_input.setFixedWidth(80)
        info_layout.addWidget(self.parent.crop_start_input, 3, 1)

        info_layout.addWidget(QLabel("结尾裁剪(帧):"), 3, 2)
        self.parent.crop_end_input = QLineEdit("0")
        self.parent.crop_end_input.setFixedWidth(80)
        info_layout.addWidget(self.parent.crop_end_input, 3, 3)

        left_layout.addWidget(info_group)

    def _create_action_buttons_row(self, left_layout):
        """创建操作按钮行 - 与原模块完全一致的位置和布局"""
        from PyQt6.QtWidgets import QHBoxLayout, QPushButton, QButtonGroup

        # 🔒 操作按钮水平布局 - 与原模块完全一致
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setContentsMargins(0, 5, 0, 5)
        action_buttons_layout.setSpacing(10)

        # 🔒 创建按钮组确保模式按钮互斥 - 与原模块完全一致
        self.parent.mode_button_group = QButtonGroup(self.parent)

        # 🔒 播放模式按钮 - 与原模块完全一致
        self.parent.play_mode_btn = QPushButton("播放模式")
        self.parent.play_mode_btn.setCheckable(True)
        self.parent.play_mode_btn.setFixedWidth(80)
        self.parent.play_mode_btn.setStyleSheet(self._get_mode_button_style())

        # 🔒 截图模式按钮 - 与原模块完全一致
        self.parent.snapshot_mode_btn = QPushButton("截图模式")
        self.parent.snapshot_mode_btn.setCheckable(True)
        self.parent.snapshot_mode_btn.setChecked(True)  # 默认选中
        self.parent.snapshot_mode_btn.setFixedWidth(80)
        self.parent.snapshot_mode_btn.setStyleSheet(self._get_mode_button_style())

        # 🔒 添加到按钮组
        self.parent.mode_button_group.addButton(self.parent.play_mode_btn, 0)
        self.parent.mode_button_group.addButton(self.parent.snapshot_mode_btn, 1)

        # 🔒 截取当前帧按钮 - 与原模块完全一致
        self.parent.capture_frame_btn = QPushButton("截取当前帧")
        self.parent.capture_frame_btn.setFixedWidth(90)
        self.parent.capture_frame_btn.setStyleSheet(self._get_action_button_style())
        self.parent.capture_frame_btn.setEnabled(False)  # 初始禁用

        # 🔒 智能截取按钮 - 与原模块完全一致
        self.parent.smart_capture_btn = QPushButton("智能截取")
        self.parent.smart_capture_btn.setFixedWidth(80)
        self.parent.smart_capture_btn.setStyleSheet(self._get_smart_button_style())
        self.parent.smart_capture_btn.setEnabled(False)  # 初始禁用

        # 🔒 导入封面按钮 - 与原模块完全一致
        self.parent.import_cover_btn = QPushButton("导入封面")
        self.parent.import_cover_btn.setFixedWidth(80)
        self.parent.import_cover_btn.setStyleSheet(self._get_import_button_style())
        self.parent.import_cover_btn.setEnabled(True)  # 始终可用

        # 🔒 按顺序添加到水平布局 - 与原模块完全一致
        action_buttons_layout.addWidget(self.parent.play_mode_btn)
        action_buttons_layout.addWidget(self.parent.snapshot_mode_btn)
        action_buttons_layout.addWidget(self.parent.capture_frame_btn)
        action_buttons_layout.addWidget(self.parent.smart_capture_btn)
        action_buttons_layout.addWidget(self.parent.import_cover_btn)
        action_buttons_layout.addStretch()  # 右侧留空

        # 🔒 添加到左侧布局
        left_layout.addLayout(action_buttons_layout)

    def _get_mode_button_style(self) -> str:
        """获取模式按钮样式 - 与原模块完全一致"""
        return """
            QPushButton {
                background-color: #3A3F4B;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                padding: 8px 0;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #61AFEF;
                color: white;
            }
            QPushButton:hover {
                background-color: #4A4F5B;
            }
        """

    def _get_action_button_style(self) -> str:
        """获取截取当前帧按钮样式 - 与原模块完全一致"""
        return """
            QPushButton {
                background-color: #E06C75;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 0;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D85C6B;
            }
            QPushButton:disabled {
                background-color: #5C6370;
                color: #ABB2BF;
            }
        """

    def _get_smart_button_style(self) -> str:
        """获取智能截取按钮样式 - 与原模块完全一致"""
        return """
            QPushButton {
                background-color: #D19A66;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 0;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C8905C;
            }
            QPushButton:disabled {
                background-color: #5C6370;
                color: #ABB2BF;
            }
        """

    def _get_import_button_style(self) -> str:
        """获取导入封面按钮样式 - 与原模块完全一致"""
        return """
            QPushButton {
                background-color: #98C379;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 0;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8BB369;
            }
        """

    def _get_mode_button_style(self) -> str:
        """获取模式按钮样式 - 与原模块完全一致"""
        return """
            QPushButton {
                background-color: #3A3F4B;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                padding: 8px 0;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #61AFEF;
                color: white;
            }
            QPushButton:hover {
                background-color: #4A4F5B;
            }
        """
    
    def _build_video_controls(self, parent_layout):
        """构建视频控制组件"""
        
        # 🔒 播放控制按钮 - 与原模块完全一致
        controls_layout = QHBoxLayout()
        
        self.parent.play_pause_btn = QPushButton("播放")
        self.parent.play_pause_btn.setFixedSize(80, 30)
        self.parent.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        controls_layout.addWidget(self.parent.play_pause_btn)
        controls_layout.addStretch()
        
        parent_layout.addLayout(controls_layout)
        
        # 🔒 进度滑块 - 与原模块完全一致
        self.parent.position_slider = QSlider(Qt.Orientation.Horizontal)
        self.parent.position_slider.setMinimum(0)
        self.parent.position_slider.setMaximum(100)
        self.parent.position_slider.setValue(0)
        
        parent_layout.addWidget(self.parent.position_slider)
