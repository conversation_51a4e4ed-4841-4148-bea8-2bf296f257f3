#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径管理器 - 统一管理项目路径
避免重复的路径计算，提供缓存机制
"""

import os
from pathlib import Path
from typing import Optional
from functools import lru_cache

class PathManager:
    """路径管理器 - 单例模式"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._init_paths()
            PathManager._initialized = True
    
    def _init_paths(self):
        """初始化路径"""
        # 获取模块根目录 (modules/cover_edit/)
        self.module_dir = Path(__file__).parent.parent
        
        # 获取项目根目录
        self.project_root = self.module_dir.parent.parent
        
        # 常用路径
        self._paths = {
            'module_dir': self.module_dir,
            'project_root': self.project_root,
            'bin_dir': self.project_root / 'bin',
            'cover_dir': self.project_root / 'Cover',
            'config_dir': self.project_root / 'config',
            'logs_dir': self.project_root / 'logs',
            'azt_dir': self.project_root / 'AZT',
        }
    
    @lru_cache(maxsize=32)
    def get_path(self, path_key: str) -> Path:
        """获取路径（带缓存）"""
        return self._paths.get(path_key, self.project_root)
    
    @lru_cache(maxsize=32)
    def get_ffmpeg_path(self) -> Optional[str]:
        """获取FFmpeg路径（带缓存）"""
        # 检查bin目录
        bin_ffmpeg = self.get_path('bin_dir') / 'ffmpeg.exe'
        if bin_ffmpeg.exists():
            return str(bin_ffmpeg)
        
        # 检查系统PATH
        import shutil
        system_ffmpeg = shutil.which('ffmpeg')
        if system_ffmpeg:
            return system_ffmpeg
        
        return None
    
    @lru_cache(maxsize=32)
    def get_ffprobe_path(self) -> Optional[str]:
        """获取FFprobe路径（带缓存）"""
        # 检查bin目录
        bin_ffprobe = self.get_path('bin_dir') / 'ffprobe.exe'
        if bin_ffprobe.exists():
            return str(bin_ffprobe)
        
        # 检查系统PATH
        import shutil
        system_ffprobe = shutil.which('ffprobe')
        if system_ffprobe:
            return system_ffprobe
        
        return None
    
    def find_font_file(self, font_name: str) -> Optional[str]:
        """查找字体文件"""
        azt_dir = self.get_path('azt_dir')
        if not azt_dir.exists():
            return None
        
        for font_file in azt_dir.glob('*'):
            if (font_name in font_file.name and 
                font_file.suffix.lower() in ['.ttf', '.ttc', '.otf']):
                return str(font_file)
        
        return None
    
    def ensure_dir_exists(self, path_key: str) -> Path:
        """确保目录存在"""
        path = self.get_path(path_key)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    def clear_cache(self):
        """清除路径缓存"""
        self.get_path.cache_clear()
        self.get_ffmpeg_path.cache_clear()
        self.get_ffprobe_path.cache_clear()

# 全局路径管理器实例
_path_manager = PathManager()

# 便捷函数
def get_project_root() -> Path:
    """获取项目根目录"""
    return _path_manager.get_path('project_root')

def get_cover_dir() -> Path:
    """获取Cover目录"""
    return _path_manager.get_path('cover_dir')

def get_bin_dir() -> Path:
    """获取bin目录"""
    return _path_manager.get_path('bin_dir')

def get_ffmpeg_path() -> Optional[str]:
    """获取FFmpeg路径"""
    return _path_manager.get_ffmpeg_path()

def get_ffprobe_path() -> Optional[str]:
    """获取FFprobe路径"""
    return _path_manager.get_ffprobe_path()

def find_font_file(font_name: str) -> Optional[str]:
    """查找字体文件"""
    return _path_manager.find_font_file(font_name)

def ensure_logs_dir() -> Path:
    """确保日志目录存在"""
    return _path_manager.ensure_dir_exists('logs_dir')
