#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 模块资源管理器
统一管理QPixmap、视频捕获、文件句柄等资源，防止内存泄漏
"""

import weakref
import gc
from typing import Any, Dict, List, Optional, Set
from contextlib import contextmanager
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import QObject, pyqtSignal

from .logger import get_logger
from .constants import CoverEditConstants

class ResourceManager(QObject):
    """资源管理器 - 统一管理各种资源的生命周期"""
    
    # 信号
    resource_registered = pyqtSignal(str, str)  # 资源类型, 资源ID
    resource_released = pyqtSignal(str, str)    # 资源类型, 资源ID
    memory_warning = pyqtSignal(float)          # 内存使用量(MB)
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("ResourceManager")
        
        # 🚀 修复：混合存储策略，处理不支持弱引用的对象
        self._resources: Dict[str, Any] = {
            'pixmaps': weakref.WeakSet(),  # QPixmap支持弱引用
            'video_captures': set(),       # 🚀 修复：VideoCapture不支持弱引用，使用普通set
            'file_handles': weakref.WeakSet(),
            'threads': weakref.WeakSet(),
            'temp_files': set(),  # 临时文件路径，使用普通set
            'custom': weakref.WeakSet()
        }
        
        # 资源计数器
        self._resource_counters: Dict[str, int] = {
            'pixmaps': 0,
            'video_captures': 0,
            'file_handles': 0,
            'threads': 0,
            'temp_files': 0,
            'custom': 0
        }
        
        # 资源ID映射
        self._resource_ids: Dict[Any, str] = {}
        self._id_counter = 0
    
    def _generate_resource_id(self) -> str:
        """生成唯一的资源ID"""
        self._id_counter += 1
        return f"res_{self._id_counter:06d}"
    
    def register_pixmap(self, pixmap: QPixmap, description: str = "") -> str:
        """
        注册QPixmap资源
        
        Args:
            pixmap: QPixmap对象
            description: 资源描述
            
        Returns:
            资源ID
        """
        if pixmap is None or pixmap.isNull():
            return ""
        
        resource_id = self._generate_resource_id()
        self._resources['pixmaps'].add(pixmap)
        self._resource_ids[pixmap] = resource_id
        self._resource_counters['pixmaps'] += 1
        
        size_mb = (pixmap.width() * pixmap.height() * 4) / (1024 * 1024)  # 估算内存使用
        self.logger.debug(f"注册Pixmap资源: {resource_id} ({pixmap.width()}x{pixmap.height()}, "
                         f"~{size_mb:.1f}MB) - {description}")
        
        self.resource_registered.emit('pixmap', resource_id)
        self._check_memory_usage()
        
        return resource_id
    
    def register_video_capture(self, capture, description: str = "") -> str:
        """
        注册视频捕获资源

        Args:
            capture: cv2.VideoCapture对象
            description: 资源描述

        Returns:
            资源ID
        """
        if capture is None:
            return ""

        try:
            resource_id = self._generate_resource_id()

            # 🚀 修复：安全地添加VideoCapture到普通set
            self._resources['video_captures'].add(capture)

            # 🚀 修复：使用capture的内存地址作为键，避免弱引用问题
            capture_key = id(capture)  # 使用内存地址作为键
            self._resource_ids[capture_key] = resource_id
            self._resource_counters['video_captures'] += 1

            self.logger.debug(f"注册VideoCapture资源: {resource_id} - {description}")
            self.resource_registered.emit('video_capture', resource_id)

            return resource_id

        except Exception as e:
            self.logger.error(f"注册VideoCapture失败: {e}")
            return ""
    
    def register_file_handle(self, file_handle, description: str = "") -> str:
        """
        注册文件句柄资源
        
        Args:
            file_handle: 文件句柄对象
            description: 资源描述
            
        Returns:
            资源ID
        """
        if file_handle is None:
            return ""
        
        resource_id = self._generate_resource_id()
        self._resources['file_handles'].add(file_handle)
        self._resource_ids[file_handle] = resource_id
        self._resource_counters['file_handles'] += 1
        
        self.logger.debug(f"注册文件句柄资源: {resource_id} - {description}")
        self.resource_registered.emit('file_handle', resource_id)
        
        return resource_id
    
    def register_thread(self, thread, description: str = "") -> str:
        """
        注册线程资源
        
        Args:
            thread: QThread对象
            description: 资源描述
            
        Returns:
            资源ID
        """
        if thread is None:
            return ""
        
        resource_id = self._generate_resource_id()
        self._resources['threads'].add(thread)
        self._resource_ids[thread] = resource_id
        self._resource_counters['threads'] += 1
        
        self.logger.debug(f"注册线程资源: {resource_id} - {description}")
        self.resource_registered.emit('thread', resource_id)
        
        return resource_id
    
    def register_temp_file(self, file_path: str, description: str = "") -> str:
        """
        注册临时文件
        
        Args:
            file_path: 临时文件路径
            description: 资源描述
            
        Returns:
            资源ID
        """
        if not file_path:
            return ""
        
        resource_id = self._generate_resource_id()
        self._resources['temp_files'].add(file_path)
        self._resource_counters['temp_files'] += 1
        
        self.logger.debug(f"注册临时文件: {resource_id} - {file_path} - {description}")
        self.resource_registered.emit('temp_file', resource_id)
        
        return resource_id
    
    def register_custom(self, resource: Any, description: str = "") -> str:
        """
        注册自定义资源
        
        Args:
            resource: 自定义资源对象
            description: 资源描述
            
        Returns:
            资源ID
        """
        if resource is None:
            return ""
        
        resource_id = self._generate_resource_id()
        self._resources['custom'].add(resource)
        self._resource_ids[resource] = resource_id
        self._resource_counters['custom'] += 1
        
        self.logger.debug(f"注册自定义资源: {resource_id} - {description}")
        self.resource_registered.emit('custom', resource_id)
        
        return resource_id
    
    def release_resource(self, resource: Any) -> bool:
        """
        释放指定资源

        Args:
            resource: 要释放的资源对象

        Returns:
            是否成功释放
        """
        if resource is None:
            return False

        # 🚀 修复：对于VideoCapture，使用内存地址作为键
        resource_key = resource
        if hasattr(resource, 'release') and hasattr(resource, 'read'):  # 可能是VideoCapture
            resource_key = id(resource)

        resource_id = self._resource_ids.get(resource_key, "unknown")

        try:
            # 尝试各种释放方法
            if hasattr(resource, 'release'):
                resource.release()
                self.logger.debug(f"调用release()释放资源: {resource_id}")
                # 从video_captures集合中移除
                self._resources['video_captures'].discard(resource)
            elif hasattr(resource, 'close'):
                resource.close()
                self.logger.debug(f"调用close()释放资源: {resource_id}")
            elif hasattr(resource, 'clear'):
                resource.clear()
                self.logger.debug(f"调用clear()释放资源: {resource_id}")
            elif isinstance(resource, str) and resource in self._resources['temp_files']:
                # 删除临时文件
                import os
                if os.path.exists(resource):
                    os.remove(resource)
                    self.logger.debug(f"删除临时文件: {resource}")
                self._resources['temp_files'].discard(resource)

            # 从资源映射中移除
            if resource_key in self._resource_ids:
                del self._resource_ids[resource_key]

            self.resource_released.emit('unknown', resource_id)
            return True

        except Exception as e:
            self.logger.warning(f"释放资源失败: {resource_id} - {e}")
            return False
    
    def cleanup_all(self) -> Dict[str, int]:
        """
        清理所有资源
        
        Returns:
            清理统计信息
        """
        cleanup_stats = {}
        
        for resource_type, resource_set in self._resources.items():
            count = 0
            
            if resource_type == 'temp_files':
                # 特殊处理临时文件
                temp_files = list(resource_set)
                for file_path in temp_files:
                    try:
                        import os
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            count += 1
                    except Exception as e:
                        self.logger.warning(f"删除临时文件失败: {file_path} - {e}")
                resource_set.clear()
            else:
                # 处理其他资源类型
                resources = list(resource_set)
                for resource in resources:
                    if self.release_resource(resource):
                        count += 1
            
            cleanup_stats[resource_type] = count
            self._resource_counters[resource_type] = 0
        
        # 强制垃圾回收
        gc.collect()
        
        total_cleaned = sum(cleanup_stats.values())
        self.logger.info(f"资源清理完成，共清理 {total_cleaned} 个资源: {cleanup_stats}")
        
        return cleanup_stats
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """
        获取资源统计信息
        
        Returns:
            资源统计信息
        """
        stats = {
            'counters': self._resource_counters.copy(),
            'active_resources': {},
            'total_active': 0
        }
        
        # 统计当前活跃资源
        for resource_type, resource_set in self._resources.items():
            if resource_type == 'temp_files':
                active_count = len(resource_set)
            else:
                active_count = len(resource_set)
            
            stats['active_resources'][resource_type] = active_count
            stats['total_active'] += active_count
        
        return stats
    
    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > CoverEditConstants.MEMORY_CRITICAL_THRESHOLD:
                self.logger.critical(f"内存使用严重过高: {memory_mb:.1f}MB")
                self.memory_warning.emit(memory_mb)
            elif memory_mb > CoverEditConstants.MEMORY_WARNING_THRESHOLD:
                self.logger.warning(f"内存使用过高: {memory_mb:.1f}MB")
                self.memory_warning.emit(memory_mb)
                
        except ImportError:
            pass  # psutil未安装
        except Exception as e:
            self.logger.debug(f"内存检查失败: {e}")

# ============================================================================
# 上下文管理器
# ============================================================================

@contextmanager
def managed_resource(resource_manager: ResourceManager, 
                    resource: Any, 
                    resource_type: str = 'custom',
                    description: str = ""):
    """
    资源管理上下文管理器
    
    Args:
        resource_manager: 资源管理器实例
        resource: 要管理的资源
        resource_type: 资源类型
        description: 资源描述
    """
    resource_id = ""
    
    try:
        # 注册资源
        if resource_type == 'pixmap':
            resource_id = resource_manager.register_pixmap(resource, description)
        elif resource_type == 'video_capture':
            resource_id = resource_manager.register_video_capture(resource, description)
        elif resource_type == 'file_handle':
            resource_id = resource_manager.register_file_handle(resource, description)
        elif resource_type == 'thread':
            resource_id = resource_manager.register_thread(resource, description)
        elif resource_type == 'temp_file':
            resource_id = resource_manager.register_temp_file(resource, description)
        else:
            resource_id = resource_manager.register_custom(resource, description)
        
        yield resource
        
    finally:
        # 自动释放资源
        if resource:
            resource_manager.release_resource(resource)

# ============================================================================
# 全局资源管理器实例
# ============================================================================

# 创建全局资源管理器实例
_global_resource_manager = None

def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器实例"""
    global _global_resource_manager
    if _global_resource_manager is None:
        _global_resource_manager = ResourceManager()
    return _global_resource_manager

def cleanup_global_resources():
    """清理全局资源"""
    global _global_resource_manager
    if _global_resource_manager is not None:
        return _global_resource_manager.cleanup_all()
    return {}

# ============================================================================
# 便捷函数
# ============================================================================

def register_pixmap(pixmap: QPixmap, description: str = "") -> str:
    """注册QPixmap资源的便捷函数"""
    return get_resource_manager().register_pixmap(pixmap, description)

def register_video_capture(capture, description: str = "") -> str:
    """注册视频捕获资源的便捷函数"""
    return get_resource_manager().register_video_capture(capture, description)

def register_temp_file(file_path: str, description: str = "") -> str:
    """注册临时文件的便捷函数"""
    return get_resource_manager().register_temp_file(file_path, description)

def release_resource(resource: Any) -> bool:
    """释放资源的便捷函数"""
    return get_resource_manager().release_resource(resource)

# ============================================================================
# 使用示例
# ============================================================================

if __name__ == "__main__":
    # 创建资源管理器
    rm = ResourceManager()
    
    # 模拟注册各种资源
    from PyQt6.QtGui import QPixmap
    
    # 注册Pixmap
    pixmap = QPixmap(100, 100)
    pixmap_id = rm.register_pixmap(pixmap, "测试图像")
    
    # 注册临时文件
    temp_file_id = rm.register_temp_file("/tmp/test.txt", "测试临时文件")
    
    # 获取统计信息
    stats = rm.get_resource_stats()
    print(f"资源统计: {stats}")
    
    # 清理所有资源
    cleanup_stats = rm.cleanup_all()
    print(f"清理统计: {cleanup_stats}")
    
    # 使用上下文管理器
    with managed_resource(rm, QPixmap(200, 200), 'pixmap', '上下文管理的图像') as managed_pixmap:
        print(f"使用资源: {managed_pixmap.size()}")
    # 资源会自动释放
