#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制面板构建器
负责构建控制相关的UI组件，确保与原模块完全一致
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QPushButton, QLabel, QLineEdit, QCheckBox)
from PyQt6.QtCore import Qt

# 导入常量
from cover_edit.utils.constants import CoverEditConstants

class ControlPanelBuilder:
    """控制面板构建器 - 确保与原模块布局完全一致"""
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
    
    def build_control_panels(self):
        """
        构建控制面板 - 与原模块完全一致
        🔒 保证：与原模块的控制面板构建产生完全相同的布局
        """
        try:
            self.logger.debug("开始构建控制面板")

            # 🚀 修复：操作按钮已在预览面板构建器中创建，这里只创建控制面板
            self.logger.debug("创建控制面板...")
            self._create_control_panels()
            self.logger.debug("控制面板创建完成")

            # 🚀 暂时跳过导出进度条
            # self._create_export_progress_left()

            self.logger.debug("控制面板构建完成")

        except Exception as e:
            self.logger.error(f"构建控制面板失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            raise

    def _create_control_buttons(self):
        """创建控制按钮 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QHBoxLayout, QButtonGroup

        # 🔒 模式按钮行 - 与原模块完全一致
        mode_button_layout = QHBoxLayout()
        mode_button_layout.setContentsMargins(0, 5, 0, 5)
        mode_button_layout.setSpacing(10)

        # 🔒 创建按钮组确保单选行为 - 与原模块完全一致
        self.parent.mode_button_group = QButtonGroup(self.parent)

        # 🚀 修复：只创建操作按钮，模式按钮在_create_control_panels中创建
        self._create_action_buttons(mode_button_layout)

        # 🔒 添加到左侧布局 - 与原模块完全一致
        central_widget = self.parent.centralWidget()
        if central_widget and hasattr(central_widget, 'left_layout'):
            central_widget.left_layout.addLayout(mode_button_layout)
        elif hasattr(central_widget, 'left_panel') and central_widget.left_panel:
            layout = central_widget.left_panel.layout()
            if layout:
                layout.addLayout(mode_button_layout)

    def _create_mode_buttons(self, layout):
        """创建模式切换按钮 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QPushButton

        button_style = self._get_button_style()

        # 🔒 播放模式按钮 - 与原模块完全一致
        self.parent.play_mode_btn = QPushButton("播放模式")
        self.parent.play_mode_btn.setCheckable(True)
        self.parent.play_mode_btn.setFixedWidth(120)
        self.parent.play_mode_btn.setStyleSheet(button_style['mode'])

        # 🔒 截图模式按钮 - 与原模块完全一致
        self.parent.snapshot_mode_btn = QPushButton("截图模式")
        self.parent.snapshot_mode_btn.setCheckable(True)
        self.parent.snapshot_mode_btn.setChecked(True)  # 默认选中
        self.parent.snapshot_mode_btn.setFixedWidth(120)
        self.parent.snapshot_mode_btn.setStyleSheet(button_style['mode'])

        # 🔒 添加到按钮组 - 与原模块完全一致
        self.parent.mode_button_group.addButton(self.parent.play_mode_btn, 0)
        self.parent.mode_button_group.addButton(self.parent.snapshot_mode_btn, 1)

        # 🔒 添加到水平布局 - 与原模块完全一致
        layout.addWidget(self.parent.play_mode_btn)
        layout.addWidget(self.parent.snapshot_mode_btn)

    def _create_action_buttons(self, layout):
        """创建操作按钮 - 第二步：先只添加截取当前帧按钮"""
        from PyQt6.QtWidgets import QPushButton

        button_style = self._get_button_style()

        # 🚀 第二步：截取当前帧按钮
        self.parent.capture_frame_btn = QPushButton("截取当前帧")
        self.parent.capture_frame_btn.setFixedWidth(120)
        self.parent.capture_frame_btn.setStyleSheet(button_style['action'])
        self.parent.capture_frame_btn.setEnabled(False)  # 初始禁用，等视频加载后启用

        # 🚀 第三步：添加智能截取按钮
        self.parent.smart_capture_btn = QPushButton("智能截取")
        self.parent.smart_capture_btn.setFixedWidth(120)
        self.parent.smart_capture_btn.setStyleSheet(button_style['smart'])
        self.parent.smart_capture_btn.setEnabled(False)  # 初始禁用，等视频加载后启用

        # 🚀 第四步：添加导入封面按钮
        self.parent.import_cover_btn = QPushButton("导入封面")
        self.parent.import_cover_btn.setFixedWidth(120)
        self.parent.import_cover_btn.setStyleSheet(button_style['import'])
        self.parent.import_cover_btn.setEnabled(True)  # 导入封面按钮始终可用

        # 🔒 添加到水平布局
        layout.addWidget(self.parent.capture_frame_btn)
        layout.addWidget(self.parent.smart_capture_btn)
        layout.addWidget(self.parent.import_cover_btn)

    def _create_control_panels(self):
        """创建控制面板 - 与原模块完全一致"""
        try:
            from PyQt6.QtWidgets import QStackedWidget, QSizePolicy

            # 🔒 控制面板堆叠区域 - 与原模块完全一致
            self.parent.control_stack = QStackedWidget()
            self.parent.control_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
            self.parent.control_stack.setMaximumHeight(60)

            # 🔒 创建播放和截图控制面板 - 与原模块完全一致
            play_control_panel = self._create_play_control_panel()
            snapshot_control_panel = self._create_snapshot_control_panel()

            self.parent.control_stack.addWidget(play_control_panel)
            self.parent.control_stack.addWidget(snapshot_control_panel)
            self.parent.control_stack.setCurrentIndex(1)  # 默认显示截图控制面板

            # 🚀 修复：模式按钮已在预览面板构建器中创建，这里只添加控制面板
            central_widget = self.parent.centralWidget()
            if central_widget and hasattr(central_widget, 'left_panel'):
                left_layout = central_widget.left_panel.layout()
                if left_layout:
                    # 添加控制面板
                    left_layout.addWidget(self.parent.control_stack)

            # 🔒 修复：添加到左侧布局 - 确保正确访问left_layout
            # 优先从主模块获取left_layout
            if hasattr(self.parent, 'left_layout') and self.parent.left_layout:
                self.parent.left_layout.addWidget(self.parent.control_stack)
                self.logger.debug("control_stack已添加到主模块的left_layout")
            else:
                # 回退方案：从central_widget获取
                central_widget = self.parent.centralWidget()
                if central_widget and hasattr(central_widget, 'left_layout'):
                    central_widget.left_layout.addWidget(self.parent.control_stack)
                    self.logger.debug("control_stack已添加到central_widget的left_layout")
                elif hasattr(central_widget, 'left_panel') and central_widget.left_panel:
                    layout = central_widget.left_panel.layout()
                    if layout:
                        layout.addWidget(self.parent.control_stack)
                        self.logger.debug("control_stack已添加到left_panel的layout")
                else:
                    self.logger.error("无法找到left_layout，control_stack未添加")

            # 🚀 修复：移除重复的视频信息组创建
            # 视频信息组已在预览面板构建器中创建，这里不需要重复创建
            # info_group = self._create_video_info_group()  # 已移除重复创建

        except Exception as e:
            self.logger.error(f"创建控制面板时出错: {e}")
            raise

    def _get_button_style(self) -> dict:
        """获取按钮样式 - 与原模块完全一致"""
        return {
            'mode': """
                QPushButton {
                    background-color: #3A3F4B;
                    color: #ABB2BF;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:checked {
                    background-color: #61AFEF;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4A4F5B;
                }
            """,
            'action': """
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """,
            'smart': """
                QPushButton {
                    background-color: #9C27B0;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7B1FA2;
                }
            """,
            'import': """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #43A047;
                }
            """
        }

    def _build_video_controls(self):
        """构建视频控制组件"""
        
        # 🔒 视频加载按钮 - 与原模块完全一致
        self.parent.load_video_btn = QPushButton("选择视频文件")
        self.parent.load_video_btn.setFixedSize(120, 35)
        self.parent.load_video_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        
        # 🔒 截图按钮 - 与原模块完全一致
        self.parent.snapshot_btn = QPushButton("截取当前帧")
        self.parent.snapshot_btn.setFixedSize(120, 35)
        self.parent.snapshot_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.parent.snapshot_btn.setEnabled(False)
        
        # 🔒 智能截图按钮 - 与原模块完全一致
        self.parent.smart_snapshot_btn = QPushButton("智能截取")
        self.parent.smart_snapshot_btn.setFixedSize(120, 35)
        self.parent.smart_snapshot_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)
        self.parent.smart_snapshot_btn.setEnabled(False)
        
        # 🔒 裁剪控制 - 与原模块完全一致
        self._build_crop_controls()
    
    def _build_cover_controls(self):
        """构建封面控制组件"""
        
        # 🔒 导入封面按钮 - 与原模块完全一致
        self.parent.import_cover_btn = QPushButton("导入封面图片")
        self.parent.import_cover_btn.setFixedSize(120, 35)
        self.parent.import_cover_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
    
    def _build_batch_controls(self):
        """构建批量处理控制组件"""
        
        # 🔒 批量模式复选框 - 与原模块完全一致
        self.parent.batch_mode_checkbox = QCheckBox("批量处理模式")
        self.parent.batch_mode_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #555;
                background-color: #2b2b2b;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
            }
        """)
        
        # 🔒 监控文件夹按钮 - 与原模块完全一致
        self.parent.watch_folder_btn = QPushButton("选择监控文件夹")
        self.parent.watch_folder_btn.setFixedSize(140, 35)
        self.parent.watch_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
        """)
        self.parent.watch_folder_btn.setEnabled(False)
        
        # 🔒 输出文件夹按钮 - 与原模块完全一致
        self.parent.output_folder_btn = QPushButton("选择输出文件夹")
        self.parent.output_folder_btn.setFixedSize(140, 35)
        self.parent.output_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #795548;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6D4C41;
            }
        """)
        self.parent.output_folder_btn.setEnabled(False)
        
        # 🔒 开始批量处理按钮 - 与原模块完全一致
        self.parent.start_batch_btn = QPushButton("开始批量处理")
        self.parent.start_batch_btn.setFixedSize(140, 35)
        self.parent.start_batch_btn.setStyleSheet("""
            QPushButton {
                background-color: #E91E63;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C2185B;
            }
        """)
        self.parent.start_batch_btn.setEnabled(False)
    
    def _build_crop_controls(self):
        """构建裁剪控制组件"""
        
        # 🔒 裁剪输入框 - 与原模块完全一致
        self.parent.crop_start_input = QLineEdit(str(CoverEditConstants.DEFAULT_CROP_START))
        self.parent.crop_start_input.setFixedWidth(80)
        self.parent.crop_start_input.setStyleSheet("""
            QLineEdit {
                background-color: #3C4043;
                border: 1px solid #5F6368;
                border-radius: 4px;
                padding: 5px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1976D2;
            }
        """)
        
        self.parent.crop_end_input = QLineEdit(str(CoverEditConstants.DEFAULT_CROP_END))
        self.parent.crop_end_input.setFixedWidth(80)
        self.parent.crop_end_input.setStyleSheet("""
            QLineEdit {
                background-color: #3C4043;
                border: 1px solid #5F6368;
                border-radius: 4px;
                padding: 5px;
                color: white;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #1976D2;
            }
        """)



    def _create_export_progress_left(self):
        """创建导出进度条（左侧面板）- 与原模块完全一致"""
        from PyQt6.QtWidgets import QProgressBar

        # 🔒 导出进度条 - 与原模块完全一致
        self.parent.export_progress = QProgressBar()
        self.parent.export_progress.setVisible(False)  # 默认隐藏
        self.parent.export_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #2b2b2b;
                color: white;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)

        # 🔒 修复：添加到左侧布局 - 确保正确访问left_layout
        if hasattr(self.parent, 'left_layout') and self.parent.left_layout:
            self.parent.left_layout.addWidget(self.parent.export_progress)
            self.logger.debug(f"导出进度条已添加到左侧布局，布局项目数: {self.parent.left_layout.count()}")
        else:
            # 回退方案：从central_widget获取
            central_widget = self.parent.centralWidget()
            if central_widget and hasattr(central_widget, 'left_layout'):
                central_widget.left_layout.addWidget(self.parent.export_progress)
                self.logger.debug("导出进度条已添加到central_widget的left_layout")
            elif hasattr(central_widget, 'left_panel') and central_widget.left_panel:
                layout = central_widget.left_panel.layout()
                if layout:
                    layout.addWidget(self.parent.export_progress)
                    self.logger.debug("导出进度条已添加到left_panel的layout")
            else:
                self.logger.error("无法找到left_layout，导出进度条未添加")

    def _create_play_control_panel(self):
        """创建播放控制面板 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QSlider, QLabel
        from PyQt6.QtCore import Qt

        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 🔒 播放/暂停按钮 - 与原模块完全一致
        self.parent.play_pause_btn = QPushButton("播放")
        self.parent.play_pause_btn.setFixedSize(80, 40)
        self.parent.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        # 🚀 修复：延迟信号连接，避免在UI构建阶段连接
        # self.parent.play_pause_btn.clicked.connect(self.parent.toggle_playback)

        layout.addWidget(self.parent.play_pause_btn)
        layout.addStretch()

        return panel

    def _create_mode_buttons(self, left_layout):
        """创建模式切换按钮 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QHBoxLayout, QPushButton, QButtonGroup

        # 模式按钮行
        mode_button_layout = QHBoxLayout()
        mode_button_layout.setContentsMargins(0, 5, 0, 5)
        mode_button_layout.setSpacing(10)

        # 创建按钮组确保单选行为
        self.parent.mode_button_group = QButtonGroup(self.parent)

        # 播放模式按钮
        self.parent.play_mode_btn = QPushButton("播放模式")
        self.parent.play_mode_btn.setCheckable(True)
        self.parent.play_mode_btn.setFixedWidth(120)
        self.parent.play_mode_btn.setStyleSheet(self._get_mode_button_style())
        # 🚀 修复：延迟信号连接，避免在UI构建阶段连接
        # self.parent.play_mode_btn.clicked.connect(self.parent.switch_to_play_mode)

        # 截图模式按钮
        self.parent.snapshot_mode_btn = QPushButton("截图模式")
        self.parent.snapshot_mode_btn.setCheckable(True)
        self.parent.snapshot_mode_btn.setChecked(True)  # 默认选中
        self.parent.snapshot_mode_btn.setFixedWidth(120)
        self.parent.snapshot_mode_btn.setStyleSheet(self._get_mode_button_style())
        # 🚀 修复：延迟信号连接，避免在UI构建阶段连接
        # self.parent.snapshot_mode_btn.clicked.connect(self.parent.switch_to_snapshot_mode)

        # 添加到按钮组
        self.parent.mode_button_group.addButton(self.parent.play_mode_btn, 0)
        self.parent.mode_button_group.addButton(self.parent.snapshot_mode_btn, 1)

        mode_button_layout.addWidget(self.parent.play_mode_btn)
        mode_button_layout.addWidget(self.parent.snapshot_mode_btn)
        mode_button_layout.addStretch()

        left_layout.addLayout(mode_button_layout)

    def _get_mode_button_style(self) -> str:
        """获取模式按钮样式 - 与原模块完全一致"""
        return """
            QPushButton {
                background-color: #3A3F4B;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                padding: 8px 0;
                font-weight: bold;
            }
            QPushButton:checked {
                background-color: #61AFEF;
                color: white;
            }
            QPushButton:hover {
                background-color: #4A4F5B;
            }
        """

        # 🔒 进度条 - 与原模块完全一致
        self.parent.playback_slider = QSlider(Qt.Orientation.Horizontal)
        self.parent.playback_slider.setMinimum(0)
        self.parent.playback_slider.setMaximum(100)
        self.parent.playback_slider.setValue(0)
        self.parent.playback_slider.setStyleSheet(self._get_slider_style("#4CAF50"))

        # 🔒 时间标签 - 与原模块完全一致
        self.parent.time_label = QLabel("00:00 / 00:00")
        self.parent.time_label.setMinimumWidth(100)
        self.parent.time_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.parent.play_pause_btn)
        layout.addWidget(self.parent.playback_slider, 1)
        layout.addWidget(self.parent.time_label)

        return panel

    def _create_snapshot_control_panel(self):
        """创建截图控制面板 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QWidget, QHBoxLayout, QSlider, QLabel
        from PyQt6.QtCore import Qt

        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 🔒 帧滑块 - 与原模块完全一致
        self.parent.frame_slider = QSlider(Qt.Orientation.Horizontal)
        self.parent.frame_slider.setMinimum(0)
        self.parent.frame_slider.setMaximum(100)
        self.parent.frame_slider.setValue(0)
        self.parent.frame_slider.setStyleSheet(self._get_slider_style("#FF9800"))

        # 🔒 帧数显示 - 与原模块完全一致
        self.parent.frame_label = QLabel("帧: 0/0")
        self.parent.frame_label.setMinimumWidth(100)
        self.parent.frame_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.parent.frame_slider, 1)
        layout.addWidget(self.parent.frame_label)

        return panel

    def _get_slider_style(self, color: str) -> str:
        """获取滑块样式 - 与原模块完全一致"""
        return f"""
            QSlider::groove:horizontal {{
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {color};
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::handle:horizontal:hover {{
                background: {color};
                opacity: 0.8;
            }}
        """

    # 🚀 修复：移除重复的视频信息组方法
    # _create_video_info_group 方法已在预览面板构建器中定义，这里不需要重复

    def _ensure_button_references(self):
        """确保按钮引用正确传递"""
        # 这个方法确保所有创建的按钮都能被外部访问
        # 按钮已经在创建时直接赋值给self.parent，所以这里不需要额外操作
        pass
