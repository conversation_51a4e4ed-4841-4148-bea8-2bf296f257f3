import logging
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel  # 添加 QVBoxLayout 和 QLabel
from PyQt6.QtGui import QFont  # 添加 QFont

class BaseModule(QWidget):
    """所有功能模块的基类"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.parent_window = parent
        try:
            self.logger.info("初始化模块")
            self.init_ui()
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}", exc_info=True)
            # 显示错误UI
            layout = QVBoxLayout(self)  # 确保布局已创建
            error_label = QLabel(f"模块初始化错误: {str(e)}")
            error_label.setStyleSheet("color: red; font-size: 16px;")
            layout.addWidget(error_label)
    
    def init_ui(self):
        """初始化UI（必须由子类实现）"""
        raise NotImplementedError("子类必须实现 init_ui 方法")
    
    def get_title(self):
        """获取模块标题（可被子类重写）"""
        return "未命名模块"
    
    def get_color(self):
        """获取模块主题色（可被子类重写）"""
        return "#61AFEF"  # 默认蓝色
    
    def get_style(self):
        """返回基本样式"""
        return """
            QGroupBox {
                color: #ABB2BF;
                font-size: 14px;
                border: 1px solid #303238;
                border-radius: 8px;
                margin-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox {
                background-color: #1E2025;
                border: 1px solid #303238;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                color: #D0D0D0;
            }
            QPushButton {
                background-color: #3A3D44;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #4A4D54;
            }
            QTextEdit {
                background-color: #1E2025;
                border: 1px solid #303238;
                border-radius: 4px;
                color: #ABB2BF;
                font-family: 'Consolas', monospace;
                font-size: 12px;
                padding: 8px;
            }
        """
