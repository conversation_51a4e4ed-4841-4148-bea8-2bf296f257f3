#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 图像处理器
专门处理图像相关的操作，包括缩放、合成、格式转换等
"""

import numpy as np
from typing import Optional, Tuple, List
from PIL import Image, ImageDraw, ImageFont
from PyQt6.QtGui import QPixmap, QImage, QPainter, QColor
from PyQt6.QtCore import QObject, pyqtSignal, Qt, QSize

from .logger import get_logger
from .constants import CoverEditConstants
from .decorators import error_handler, performance_monitor
from .resource_manager import register_pixmap

class ImageProcessor(QObject):
    """图像处理器 - 专门处理图像相关操作"""
    
    # 信号定义
    image_processed = pyqtSignal(str, QPixmap)  # 操作类型, 处理结果
    processing_progress = pyqtSignal(int)  # 处理进度
    error_occurred = pyqtSignal(str)  # 错误信息
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("ImageProcessor")
        
        # 图像缓存
        self.image_cache = {}
        self.max_cache_size = CoverEditConstants.MAX_CACHE_SIZE
        
    @performance_monitor(threshold_ms=100.0)
    def scale_image(self, image: QPixmap, target_size: Tuple[int, int], 
                   keep_aspect_ratio: bool = True, 
                   smooth: bool = True) -> Optional[QPixmap]:
        """
        缩放图像
        
        Args:
            image: 原始图像
            target_size: 目标尺寸 (width, height)
            keep_aspect_ratio: 是否保持宽高比
            smooth: 是否使用平滑缩放
            
        Returns:
            缩放后的图像
        """
        try:
            if image.isNull():
                self.logger.warning("输入图像为空")
                return None
            
            target_width, target_height = target_size
            
            # 验证目标尺寸
            if target_width <= 0 or target_height <= 0:
                self.logger.warning(f"无效的目标尺寸: {target_size}")
                return None
            
            # 检查缓存
            cache_key = f"scale_{image.cacheKey()}_{target_width}x{target_height}_{keep_aspect_ratio}_{smooth}"
            if cache_key in self.image_cache:
                self.logger.debug("使用缓存的缩放图像")
                return self.image_cache[cache_key]
            
            # 执行缩放
            aspect_mode = Qt.AspectRatioMode.KeepAspectRatio if keep_aspect_ratio else Qt.AspectRatioMode.IgnoreAspectRatio
            transform_mode = Qt.TransformationMode.SmoothTransformation if smooth else Qt.TransformationMode.FastTransformation
            
            scaled_image = image.scaled(target_width, target_height, aspect_mode, transform_mode)
            
            # 注册资源
            register_pixmap(scaled_image, f"缩放图像 {target_width}x{target_height}")
            
            # 添加到缓存
            self._add_to_cache(cache_key, scaled_image)
            
            self.logger.debug(f"图像缩放完成: {image.width()}x{image.height()} -> {scaled_image.width()}x{scaled_image.height()}")
            
            return scaled_image
            
        except Exception as e:
            self.logger.error(f"图像缩放失败: {e}")
            self.error_occurred.emit(str(e))
            return None
    
    @performance_monitor(threshold_ms=200.0)
    def scale_to_video_resolution(self, image: QPixmap, video_width: int, video_height: int) -> Optional[QPixmap]:
        """
        将图像缩放到视频分辨率
        
        Args:
            image: 原始图像
            video_width: 视频宽度
            video_height: 视频高度
            
        Returns:
            缩放后的图像
        """
        try:
            if image.isNull():
                return None
            
            # 如果图像已经是目标尺寸，直接返回
            if image.width() == video_width and image.height() == video_height:
                return image
            
            # 缩放到视频分辨率
            scaled_image = self.scale_image(image, (video_width, video_height), keep_aspect_ratio=False)
            
            if scaled_image:
                self.logger.info(f"图像已缩放到视频分辨率: {image.width()}x{image.height()} -> {video_width}x{video_height}")
            
            return scaled_image
            
        except Exception as e:
            self.logger.error(f"缩放到视频分辨率失败: {e}")
            return None
    
    @performance_monitor(threshold_ms=300.0)
    def composite_images(self, base_image: QPixmap, overlay_images: List[Tuple[QPixmap, int, int, float]]) -> Optional[QPixmap]:
        """
        合成多个图像
        
        Args:
            base_image: 基础图像
            overlay_images: 叠加图像列表 [(图像, x, y, 透明度), ...]
            
        Returns:
            合成后的图像
        """
        try:
            if base_image.isNull():
                self.logger.warning("基础图像为空")
                return None
            
            # 创建结果图像
            result = QPixmap(base_image.size())
            result.fill(Qt.GlobalColor.transparent)
            
            # 创建画笔
            painter = QPainter(result)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
            
            # 绘制基础图像
            painter.drawPixmap(0, 0, base_image)
            
            # 绘制叠加图像
            for i, (overlay, x, y, opacity) in enumerate(overlay_images):
                if overlay.isNull():
                    continue
                
                painter.setOpacity(opacity)
                painter.drawPixmap(x, y, overlay)
                
                # 更新进度
                progress = int((i + 1) / len(overlay_images) * 100)
                self.processing_progress.emit(progress)
            
            painter.end()
            
            # 注册资源
            register_pixmap(result, f"合成图像 {result.width()}x{result.height()}")
            
            self.logger.info(f"图像合成完成: 基础图像 + {len(overlay_images)} 个叠加图像")
            
            return result
            
        except Exception as e:
            self.logger.error(f"图像合成失败: {e}")
            self.error_occurred.emit(str(e))
            return None
    
    @performance_monitor(threshold_ms=150.0)
    def qpixmap_to_pil(self, pixmap: QPixmap) -> Optional[Image.Image]:
        """
        将QPixmap转换为PIL Image
        
        Args:
            pixmap: QPixmap对象
            
        Returns:
            PIL Image对象
        """
        try:
            if pixmap.isNull():
                return None
            
            # 转换为QImage
            qimage = pixmap.toImage()
            
            # 确保格式为RGBA
            if qimage.format() != QImage.Format.Format_RGBA8888:
                qimage = qimage.convertToFormat(QImage.Format.Format_RGBA8888)
            
            # 获取图像数据
            width = qimage.width()
            height = qimage.height()
            ptr = qimage.constBits()
            
            # 转换为numpy数组
            arr = np.array(ptr).reshape(height, width, 4)
            
            # 创建PIL Image
            pil_image = Image.fromarray(arr, 'RGBA')
            
            return pil_image
            
        except Exception as e:
            self.logger.error(f"QPixmap转PIL失败: {e}")
            return None
    
    @performance_monitor(threshold_ms=150.0)
    def pil_to_qpixmap(self, pil_image: Image.Image) -> Optional[QPixmap]:
        """
        将PIL Image转换为QPixmap
        
        Args:
            pil_image: PIL Image对象
            
        Returns:
            QPixmap对象
        """
        try:
            if pil_image is None:
                return None
            
            # 确保是RGBA格式
            if pil_image.mode != 'RGBA':
                pil_image = pil_image.convert('RGBA')
            
            # 转换为numpy数组
            arr = np.array(pil_image)
            height, width, _ = arr.shape
            bytes_per_line = 4 * width
            
            # 创建QImage
            qimage = QImage(arr.data, width, height, bytes_per_line, QImage.Format.Format_RGBA8888)
            
            # 转换为QPixmap
            pixmap = QPixmap.fromImage(qimage)
            
            # 注册资源
            register_pixmap(pixmap, f"PIL转换图像 {width}x{height}")
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"PIL转QPixmap失败: {e}")
            return None
    
    def create_text_image(self, text: str, font_name: str, font_size: int,
                         fill_color: Tuple[int, int, int], 
                         stroke_color: Tuple[int, int, int] = None,
                         stroke_width: int = 0,
                         canvas_size: Tuple[int, int] = None) -> Optional[QPixmap]:
        """
        创建文字图像
        
        Args:
            text: 文字内容
            font_name: 字体名称
            font_size: 字体大小
            fill_color: 填充颜色
            stroke_color: 描边颜色
            stroke_width: 描边宽度
            canvas_size: 画布大小
            
        Returns:
            文字图像
        """
        try:
            if not text:
                return None
            
            # 确定画布大小
            if canvas_size is None:
                # 估算文字大小
                estimated_width = len(text) * font_size
                estimated_height = font_size * 2
                canvas_size = (estimated_width, estimated_height)
            
            canvas_width, canvas_height = canvas_size
            
            # 创建PIL图像
            pil_image = Image.new('RGBA', (canvas_width, canvas_height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(pil_image)
            
            # 加载字体
            font = self._get_font(font_name, font_size)
            
            # 计算文字位置（居中）
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (canvas_width - text_width) // 2
            y = (canvas_height - text_height) // 2
            
            # 绘制描边
            if stroke_color and stroke_width > 0:
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            draw.text((x + dx, y + dy), text, font=font, fill=stroke_color)
            
            # 绘制主文字
            draw.text((x, y), text, font=font, fill=fill_color)
            
            # 转换为QPixmap
            pixmap = self.pil_to_qpixmap(pil_image)
            
            if pixmap:
                self.logger.debug(f"文字图像创建成功: '{text}' ({canvas_width}x{canvas_height})")
            
            return pixmap
            
        except Exception as e:
            self.logger.error(f"创建文字图像失败: {e}")
            return None
    
    def _get_font(self, font_name: str, font_size: int) -> ImageFont.ImageFont:
        """获取字体对象"""
        try:
            # 尝试加载指定字体
            font_path = self._find_font_path(font_name)
            if font_path:
                return ImageFont.truetype(font_path, font_size)
            else:
                # 使用默认字体
                return ImageFont.load_default()
        except Exception as e:
            self.logger.warning(f"字体加载失败，使用默认字体: {e}")
            return ImageFont.load_default()
    
    def _find_font_path(self, font_name: str) -> Optional[str]:
        """查找字体文件路径"""
        from .path_manager import find_font_file
        return find_font_file(font_name)
    
    def _add_to_cache(self, key: str, image: QPixmap):
        """添加图像到缓存"""
        try:
            # 如果缓存已满，删除最旧的项目
            if len(self.image_cache) >= self.max_cache_size:
                # 删除第一个项目（最旧的）
                oldest_key = next(iter(self.image_cache))
                del self.image_cache[oldest_key]
                self.logger.debug("清理图像缓存")
            
            self.image_cache[key] = image
            
        except Exception as e:
            self.logger.warning(f"添加缓存失败: {e}")
    
    def clear_cache(self):
        """清理图像缓存"""
        try:
            cache_size = len(self.image_cache)
            self.image_cache.clear()
            self.logger.info(f"已清理图像缓存，释放了 {cache_size} 个缓存项")
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
    
    def get_cache_info(self) -> dict:
        """获取缓存信息"""
        return {
            'size': len(self.image_cache),
            'max_size': self.max_cache_size,
            'usage_percent': len(self.image_cache) / self.max_cache_size * 100
        }

# ============================================================================
# 便捷函数
# ============================================================================

def create_image_processor() -> ImageProcessor:
    """创建图像处理器实例"""
    return ImageProcessor()

# 全局图像处理器实例（可选）
_global_image_processor = None

def get_global_image_processor() -> ImageProcessor:
    """获取全局图像处理器实例"""
    global _global_image_processor
    if _global_image_processor is None:
        _global_image_processor = ImageProcessor()
    return _global_image_processor
