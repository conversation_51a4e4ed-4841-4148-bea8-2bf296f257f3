#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 模块 - 重构后的主入口
完全独立，不依赖原模块
"""

# 🚀 优化：导入精简版主模块
from .main_module import CoverEditOptimizedModule
from .workers.export_worker import VideoExportWorker

# 为了保持兼容性，也导出原来的名称
CoverEditModule = CoverEditOptimizedModule

# 导出类，保持完全兼容
__all__ = ['CoverEditModule', 'CoverEditOptimizedModule', 'VideoExportWorker']

# 版本信息
__version__ = '2.0.0-independent'
__author__ = 'Cover Edit Team'
__description__ = 'Cover Edit Module - Independent Refactored Version'

# 🔒 完全独立保证：
# 1. 不导入原模块的任何部分
# 2. 使用自己的VideoExportWorker
# 3. 所有功能完全独立实现
# 4. 保持100%接口兼容性
