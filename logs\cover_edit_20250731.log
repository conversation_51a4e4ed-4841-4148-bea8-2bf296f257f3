2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:05:23 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始构建主布局...
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 主布局构建完成
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始构建预览面板...
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 预览面板构建完成
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始构建控制面板...
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 控制面板构建完成
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始构建右侧面板...
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 右侧面板构建完成
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 已创建组件 (5): ['import_cover_btn', 'export_btn', 'frame_slider', 'video_preview', 'cover_preview']
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - WARNING - warning:117 - ❌ 缺失组件 (3): ['load_video_btn', 'snapshot_btn', 'smart_snapshot_btn']
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - WARNING - warning:117 - 预览组件 video_label 缺失
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - WARNING - warning:117 - 预览组件 cover_label 缺失
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - WARNING - warning:117 - 缺失的控制组件: ['load_video_btn', 'snapshot_btn', 'smart_snapshot_btn', 'play_btn', 'start_batch_btn', 'watch_folder_btn', 'output_folder_btn', 'progress_bar']
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 修复组件名称映射...
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 组件名称映射完成: 0 个映射
2025-07-31 02:05:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 强制显示所有面板...
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 面板显示完成
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 强制显示批量处理组件...
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 批量处理组件显示完成: 7 个组件
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 🔗 开始立即连接UI信号...
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - WARNING - warning:117 - ⚠️ 按钮 load_video_btn 不存在
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 browse_video_btn -> load_video
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 import_cover_btn -> import_cover_image
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 export_btn -> export_video
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 text_edit_btn_export -> toggle_text_editing
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 export_settings_btn_export -> show_export_settings
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 capture_frame_btn -> capture_current_frame
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 smart_capture_btn -> smart_capture_frame
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - WARNING - warning:117 - ⚠️ 按钮 add_text_btn 不存在
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 browse_watch_folder_btn -> browse_watch_folder
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 browse_output_folder_btn -> browse_output_folder
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 reset_batch_btn -> reset_batch_processing
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 play_mode_btn -> switch_to_play_mode
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 snapshot_mode_btn -> switch_to_snapshot_mode
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ 连接 play_pause_btn -> toggle_playback
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 🔗 UI信号连接完成: 16 个连接
2025-07-31 02:05:24 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:34:33 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:34:34 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:34:34 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:34:34 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:34:34 - CoverEdit.CoverEditModule - ERROR - error:121 - 初始化UI失败: name 'QHBoxLayout' is not defined
2025-07-31 02:34:34 - CoverEdit.CoverEditModule - ERROR - error:121 - UI初始化详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 199, in _init_ui
    main_layout = QHBoxLayout(self)
                  ^^^^^^^^^^^
NameError: name 'QHBoxLayout' is not defined

2025-07-31 02:34:34 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:34:34 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:36:12 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - ERROR - error:121 - 初始化UI失败: 'CoverEditOptimizedModule' object has no attribute 'start_batch_processing'
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - ERROR - error:121 - UI初始化详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 226, in _init_ui
    right_panel = self._create_right_panel()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 331, in _create_right_panel
    batch_group = self._create_batch_group()
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 990, in _create_batch_group
    self.batch_settings_widget = self._create_batch_settings()
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 1052, in _create_batch_settings
    self.start_batch_btn.clicked.connect(self.start_batch_processing)
                                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'CoverEditOptimizedModule' object has no attribute 'start_batch_processing'. Did you mean: 'reset_batch_processing'?

2025-07-31 02:36:12 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - ERROR - error:121 - 连接UI信号失败: wrapped C/C++ object of type QSlider has been deleted
2025-07-31 02:36:12 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:39:03 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:39:11 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - ERROR - error:121 - 连接UI信号失败: 'CoverEditOptimizedModule' object has no attribute 'on_playback_slider_pressed'
2025-07-31 02:39:11 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:39:50 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - ERROR - error:121 - 连接UI信号失败: 'CoverEditOptimizedModule' object has no attribute 'on_playback_slider_pressed'
2025-07-31 02:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:41:15 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:41:15 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 02:41:15 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:42:05 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:42:05 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:43:18 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:43:18 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:45:29 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:45:29 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:45:48 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:45:48 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 02:45:48 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 02:46:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:00 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 02:46:00 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:16 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 02:46:21 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:46:22 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 02:46:56 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 02:47:23 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:47:23 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:47:23 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:47:23 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 02:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 02:47:34 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 02:47:38 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 02:47:40 - CoverEdit.CoverEditModule - ERROR - error:121 - 显示帧失败: name 'Qt' is not defined
2025-07-31 02:47:40 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 02:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 导入封面: C:/Users/<USER>/Desktop/序列.00_00_00_00.Still007.jpg
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:51:49 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:51:49 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 02:52:30 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 02:52:30 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 02:52:42 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 02:52:42 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 02:52:45 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 02:52:57 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 02:53:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 02:53:27 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 02:54:00 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 02:54:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置变化
2025-07-31 02:54:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置变化
2025-07-31 02:55:27 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置
2025-07-31 02:55:28 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置
2025-07-31 02:57:26 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:00:26 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:00:26 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:00:42 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#户外折叠椅-快手.mp4
2025-07-31 03:00:42 - CoverEdit.CoverEditModule - INFO - info:113 - 封面文件夹不存在，跳过封面匹配
2025-07-31 03:00:42 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#户外折叠椅-快手.mp4
2025-07-31 03:00:53 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/5g监控摄像头2025新款家用无线连手机远程360度云台旋转双向语音 #一起来省钱 #大家都在买的商品 #快手省钱技巧 #好物分享清单-快手.mp4
2025-07-31 03:00:53 - CoverEdit.CoverEditModule - INFO - info:113 - 封面文件夹不存在，跳过封面匹配
2025-07-31 03:00:53 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/5g监控摄像头2025新款家用无线连手机远程360度云台旋转双向语音 #一起来省钱 #大家都在买的商品 #快手省钱技巧 #好物分享清单-快手.mp4
2025-07-31 03:04:42 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头1帧, 结尾0帧
2025-07-31 03:04:42 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头15帧, 结尾0帧
2025-07-31 03:04:48 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头2帧, 结尾0帧
2025-07-31 03:04:48 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头25帧, 结尾0帧
2025-07-31 03:04:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头256帧, 结尾0帧
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:05:57 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:05:57 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:06:05 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/万利达恒温烧水壶 #万利达恒温电热水壶 #万利达恒温烧水壶 #一壶两用恒温烧水壶 #家用烧水壶 @快手卖货助手(O3x5yurai4cphmcw) @我要上热门(O3x8er38dpbhvbaa) @快手粉条(O3xhcy6vhfzcu3qe)-快手.mp4
2025-07-31 03:06:05 - CoverEdit.CoverEditModule - INFO - info:113 - 封面文件夹不存在，跳过封面匹配
2025-07-31 03:06:05 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/万利达恒温烧水壶 #万利达恒温电热水壶 #万利达恒温烧水壶 #一壶两用恒温烧水壶 #家用烧水壶 @快手卖货助手(O3x5yurai4cphmcw) @我要上热门(O3x8er38dpbhvbaa) @快手粉条(O3xhcy6vhfzcu3qe)-快手.mp4
2025-07-31 03:06:11 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/万利达恒温烧水壶 #万利达恒温电热水壶 #万利达恒温烧水壶 #一壶两用恒温烧水壶 #家用烧水壶 @快手卖货助手(O3x5yurai4cphmcw) @我要上热门(O3x8er38dpbhvbaa) @快手粉条(O3xhcy6vhfzcu3qe)-快手.mp4
2025-07-31 03:06:37 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 03:07:04 - CoverEdit.CoverEditModule - INFO - info:113 - 打开视频转码设置对话框
2025-07-31 03:09:29 - CoverEdit.CoverEditModule - INFO - info:113 - 打开视频转码设置对话框
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:10:04 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:10:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:10:05 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:14:56 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:14:56 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:15:06 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 03:15:06 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:15:06 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 03:15:11 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 03:24:53 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:24:53 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:24:53 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:24:53 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:24:54 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:24:54 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:24:54 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:24:54 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#空气炸锅烧烤架 #好物分享 #快来跟我一起薅羊毛啦 #上热门感谢官方大大-快手.mp4
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2467帧，当前第15帧
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:25:06 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#空气炸锅烧烤架 #好物分享 #快来跟我一起薅羊毛啦 #上热门感谢官方大大-快手.mp4
2025-07-31 03:25:08 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#空气炸锅烧烤架 #好物分享 #快来跟我一起薅羊毛啦 #上热门感谢官方大大-快手.mp4
2025-07-31 03:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->1, 结尾 10->10
2025-07-31 03:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 跳转到新的开始位置: 33ms (第1帧)
2025-07-31 03:25:31 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 1->10, 结尾 10->10
2025-07-31 03:25:31 - CoverEdit.CoverEditModule - INFO - info:113 - 跳转到新的开始位置: 333ms (第10帧)
2025-07-31 03:26:17 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 03:28:35 - CoverEdit.CoverEditModule - INFO - info:113 - 文字编辑模式: 开启
2025-07-31 03:28:56 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:34:30 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:34:30 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 测试日志消息
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-770帧，当前第15帧
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:35:03 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 03:35:32 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 03:35:42 - CoverEdit.CoverEditModule - INFO - info:113 - 导出设置已更新: {'width': 1080, 'height': 1920, 'fps': 30.0, 'start_time': 0.0, 'end_time': 60.0, 'output_format': 'MP4', 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (15Mbps) 推荐', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:36:35 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:36:35 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:36:48 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 03:36:50 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 03:36:57 - CoverEdit.CoverEditModule - INFO - info:113 - 导出设置已更新: {'width': 1080, 'height': 1920, 'fps': 30.0, 'start_time': 0.0, 'end_time': 60.0, 'output_format': 'MP4', 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (15Mbps) 推荐', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:38:41 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:38:41 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 🧪 测试日志功能
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:42:35 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 开始搜索FFmpeg，检查 16 个可能的路径...
2025-07-31 03:42:35 - CoverEdit.CoverEditModule - INFO - info:113 - 找到FFmpeg: C:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe
2025-07-31 03:43:02 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 03:43:39 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:43:39 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:43:39 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:43:39 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:43:40 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:43:40 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:43:40 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:43:40 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 📋 批量处理面板已显示
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1524帧，当前第15帧
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1288)
2025-07-31 03:44:36 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 03:44:39 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 📋 批量处理面板已隐藏
2025-07-31 03:44:41 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 📋 批量处理面板已显示
2025-07-31 03:44:44 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 📋 批量处理面板已隐藏
2025-07-31 03:44:53 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->2, 结尾 10->10
2025-07-31 03:44:53 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第2-1524帧，当前第15帧
2025-07-31 03:44:53 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 2->25, 结尾 10->10
2025-07-31 03:44:54 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(15) -> 开始帧(25)
2025-07-31 03:44:54 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 25->255, 结尾 10->10
2025-07-31 03:44:54 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(25) -> 开始帧(255)
2025-07-31 03:45:07 - CoverEdit.CoverEditModule - INFO - info:113 - 导入封面: C:/Users/<USER>/Desktop/兰姐.jpg
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:45:19 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 开始搜索FFmpeg，检查 16 个可能的路径...
2025-07-31 03:45:19 - CoverEdit.CoverEditModule - INFO - info:113 - 找到FFmpeg: C:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-4110帧，当前第15帧
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:45:41 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4
2025-07-31 03:45:54 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 03:46:01 - CoverEdit.CoverEditModule - INFO - info:113 - 导出设置已更新: {'width': 1080, 'height': 1920, 'fps': 30.0, 'start_time': 0.0, 'end_time': 60.0, 'output_format': 'MP4', 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (15Mbps) 推荐', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 03:46:10 - CoverEdit.CoverEditModule - INFO - info:113 - 开始搜索FFmpeg，检查 16 个可能的路径...
2025-07-31 03:46:10 - CoverEdit.CoverEditModule - INFO - info:113 - 找到FFmpeg: C:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - VideoExportWorker接收到的编码器: libx264
2025-07-31 03:46:10 - CoverEdit.CoverEditModule - INFO - info:113 - 开始导出视频: C:/Users/<USER>/Desktop/123.mp4
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 开始视频导出线程
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 使用字典形式的导出设置: {'output_format': 'MP4', 'encoder': 'libx264', 'width': 1080, 'height': 1920, 'fps': 30.0, 'quality': '高质量', 'bitrate': 8000, 'start_time': 0.0, 'end_time': 60.0, 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (15Mbps) 推荐', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 视频比例分析: 原始=1920x1080 (比例:1.778), 目标=1080x1920 (比例:0.562)
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 应用智能比例处理
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 宽视频转窄比例：裁剪左右部分 (原始比例:1.778 → 目标比例:0.562)
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - ✅ GPU编码器可用: h264_nvenc
2025-07-31 03:46:10 - CoverEdit.VideoExportWorker - INFO - info:113 - FFmpeg命令: C:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe -y -ss 0.4999999997573405 -t 136.5333332670711 -i C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4 -vf crop=607:1080:656:0,scale=1080:1920 -r 30.0 -c:v h264_nvenc -c:a aac -b:a 128k -ar 44100 -b:v 15000k -maxrate 18000k -bufsize 30000k -preset medium -pix_fmt yuv420p C:/Users/<USER>/Desktop/123.mp4
2025-07-31 03:46:11 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 1%
2025-07-31 03:46:12 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 5%
2025-07-31 03:46:12 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 10%
2025-07-31 03:46:13 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 14%
2025-07-31 03:46:13 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 19%
2025-07-31 03:46:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 24%
2025-07-31 03:46:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 28%
2025-07-31 03:46:15 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 33%
2025-07-31 03:46:15 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 37%
2025-07-31 03:46:16 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 42%
2025-07-31 03:46:16 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 46%
2025-07-31 03:46:17 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 51%
2025-07-31 03:46:17 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 55%
2025-07-31 03:46:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 60%
2025-07-31 03:46:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 64%
2025-07-31 03:46:19 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 69%
2025-07-31 03:46:19 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 73%
2025-07-31 03:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 78%
2025-07-31 03:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 82%
2025-07-31 03:46:21 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 87%
2025-07-31 03:46:22 - CoverEdit.VideoExportWorker - INFO - info:113 - FFmpeg执行成功
2025-07-31 03:46:28 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 91%
2025-07-31 03:46:28 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 100%
2025-07-31 03:46:28 - CoverEdit.CoverEditModule - INFO - info:113 - 导出成功: C:/Users/<USER>/Desktop/123.mp4
2025-07-31 03:47:17 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:47:17 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:47:17 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:47:17 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:47:17 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:47:18 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:47:18 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:47:18 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#钓鱼伞  #渔具用品  #好物分享 -快手.mp4
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-6727帧，当前第15帧
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:47:26 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#钓鱼伞  #渔具用品  #好物分享 -快手.mp4
2025-07-31 03:47:39 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#户外折叠椅-快手.mp4
2025-07-31 03:47:39 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:47:39 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:47:39 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#户外折叠椅-快手.mp4
2025-07-31 03:47:44 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#空气炸锅烧烤架 #好物分享 #快来跟我一起薅羊毛啦 #上热门感谢官方大大-快手.mp4
2025-07-31 03:47:44 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 03:47:44 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 03:47:44 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#空气炸锅烧烤架 #好物分享 #快来跟我一起薅羊毛啦 #上热门感谢官方大大-快手.mp4
2025-07-31 03:47:48 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:47:49 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 开始搜索FFmpeg，检查 16 个可能的路径...
2025-07-31 03:47:49 - CoverEdit.CoverEditModule - INFO - info:113 - 找到FFmpeg: C:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe
2025-07-31 03:48:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出设置已更新: {'width': 1080, 'height': 1920, 'fps': 30.0, 'start_time': 0.0, 'end_time': 60.0, 'output_format': 'MP4', 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (15Mbps) 推荐', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 03:48:15 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 03:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 开始搜索FFmpeg，检查 16 个可能的路径...
2025-07-31 03:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 找到FFmpeg: c:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe
2025-07-31 03:49:10 - CoverEdit.VideoExportWorker - INFO - info:113 - VideoExportWorker接收到的编码器: libx264
2025-07-31 03:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 开始导出视频: C:/Users/<USER>/Desktop/234.mp4
2025-07-31 03:49:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 开始视频导出线程
2025-07-31 03:49:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 使用字典形式的导出设置: {'output_format': 'MP4', 'encoder': 'libx264', 'width': 1080, 'height': 1920, 'fps': 30.0, 'quality': '高质量', 'bitrate': 8000, 'start_time': 0.0, 'end_time': 60.0, 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '高质量 (15Mbps) 推荐', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 03:49:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 视频比例分析: 原始=1920x1080 (比例:1.778), 目标=1080x1920 (比例:0.562)
2025-07-31 03:49:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 应用智能比例处理
2025-07-31 03:49:10 - CoverEdit.VideoExportWorker - INFO - info:113 - 宽视频转窄比例：裁剪左右部分 (原始比例:1.778 → 目标比例:0.562)
2025-07-31 03:49:11 - CoverEdit.VideoExportWorker - INFO - info:113 - ✅ GPU编码器可用: h264_nvenc
2025-07-31 03:49:11 - CoverEdit.VideoExportWorker - INFO - info:113 - FFmpeg命令: c:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe -y -ss 0.49999999939467316 -t 81.76666656767554 -i C:/Users/<USER>/Desktop/Video/#空气炸锅烧烤架 #好物分享 #快来跟我一起薅羊毛啦 #上热门感谢官方大大-快手.mp4 -vf crop=607:1080:656:0,scale=1080:1920 -r 30.0 -c:v h264_nvenc -c:a aac -b:a 128k -ar 44100 -b:v 15000k -maxrate 18000k -bufsize 30000k -preset medium -pix_fmt yuv420p C:/Users/<USER>/Desktop/234.mp4
2025-07-31 03:49:12 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 2%
2025-07-31 03:49:12 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 9%
2025-07-31 03:49:13 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 16%
2025-07-31 03:49:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 24%
2025-07-31 03:49:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 31%
2025-07-31 03:49:15 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 39%
2025-07-31 03:49:15 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 46%
2025-07-31 03:49:16 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 54%
2025-07-31 03:49:16 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 61%
2025-07-31 03:49:17 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 69%
2025-07-31 03:49:17 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 76%
2025-07-31 03:49:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 84%
2025-07-31 03:49:18 - CoverEdit.VideoExportWorker - INFO - info:113 - FFmpeg执行成功
2025-07-31 03:49:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 100%
2025-07-31 03:49:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出成功: C:/Users/<USER>/Desktop/234.mp4
2025-07-31 03:49:32 - CoverEdit.CoverEditModule - ERROR - error:121 - 截取当前帧失败: name 'Qt' is not defined
2025-07-31 03:50:38 - CoverEdit.CoverEditModule - INFO - info:113 - [info] 📋 批量处理面板已显示
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:57:07 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:57:07 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:57:07 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:57:08 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:57:08 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:57:08 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:57:08 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 03:58:49 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 03:58:49 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:07:38 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:07:38 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/5g监控摄像头2025新款家用无线连手机远程360度云台旋转双向语音 #一起来省钱 #大家都在买的商品 #快手省钱技巧 #好物分享清单-快手.mp4
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2033帧，当前第15帧
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1408)
2025-07-31 04:07:49 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/5g监控摄像头2025新款家用无线连手机远程360度云台旋转双向语音 #一起来省钱 #大家都在买的商品 #快手省钱技巧 #好物分享清单-快手.mp4
2025-07-31 04:08:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 重置批量处理失败: 'SimpleBatchProcessor' object has no attribute 'reset_processing'
2025-07-31 04:08:03 - CoverEdit.CoverEditModule - ERROR - error:121 - 重置批量处理失败: 'SimpleBatchProcessor' object has no attribute 'reset_processing'
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:10:46 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:10:46 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:11:38 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:11:38 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:11:38 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:11:39 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:11:39 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:11:39 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:11:39 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:11:39 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:11:39 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:25:49 - CoverEdit.CoverEditModule - INFO - info:113 - 打开导出设置窗口
2025-07-31 04:26:38 - CoverEdit.CoverEditModule - INFO - info:113 - 导出设置已更新: {'width': 1080, 'height': 1920, 'fps': 30.0, 'start_time': 0.0, 'end_time': 60.0, 'output_format': 'MP4', 'resolution_preset': '1080×1920 (9:16 竖屏)', 'video_codec': 'H.264 (兼容性好)', 'quality_preset': '中等质量 (15Mbps@1440p/10Mbps@1080p)', 'use_gpu': True, 'bitrate_mode': 'VBR', 'target_bitrate': 8000, 'max_bitrate': 12000, 'crf_value': 23, 'preset': 'medium', 'audio_codec': 'AAC', 'audio_bitrate': 128, 'audio_sample_rate': 44100}
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:26:55 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:26:55 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:27:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4
2025-07-31 04:27:04 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 04:27:05 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 04:27:05 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 04:27:05 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-4110帧，当前第15帧
2025-07-31 04:27:05 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 04:27:05 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 04:27:05 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4
2025-07-31 04:30:06 - CoverEdit.CoverEditModule - INFO - info:113 - 开始搜索FFmpeg，检查 16 个可能的路径...
2025-07-31 04:30:06 - CoverEdit.CoverEditModule - INFO - info:113 - 找到FFmpeg: c:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - VideoExportWorker接收到的编码器: libx264
2025-07-31 04:30:06 - CoverEdit.CoverEditModule - INFO - info:113 - 开始导出视频: C:/Users/<USER>/Desktop/369.mp4
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - 开始视频导出线程
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - 使用字典形式的导出设置: {'output_format': 'MP4', 'encoder': 'libx264', 'width': 720, 'height': 1280, 'fps': 30.00000001455957, 'quality': '高质量', 'bitrate': 8000}
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - 视频比例分析: 原始=1920x1080 (比例:1.778), 目标=720x1280 (比例:0.562)
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - 应用智能比例处理
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - 宽视频转窄比例：裁剪左右部分 (原始比例:1.778 → 目标比例:0.562)
2025-07-31 04:30:06 - CoverEdit.VideoExportWorker - INFO - info:113 - FFmpeg命令: c:\Users\<USER>\Desktop\优化版\bin\ffmpeg.exe -y -ss 0.4999999997573405 -t 136.5333332670711 -i C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4 -vf crop=607:1080:656:0,scale=720:1280 -r 30.00000001455957 -c:v libx264 -c:a aac -b:a 128k -ar 44100 -b:v 8000k -maxrate 9600k -bufsize 16000k -preset medium -pix_fmt yuv420p C:/Users/<USER>/Desktop/369.mp4
2025-07-31 04:30:07 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 0%
2025-07-31 04:30:08 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 1%
2025-07-31 04:30:08 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 2%
2025-07-31 04:30:09 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 4%
2025-07-31 04:30:09 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 5%
2025-07-31 04:30:10 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 6%
2025-07-31 04:30:10 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 8%
2025-07-31 04:30:11 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 9%
2025-07-31 04:30:11 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 11%
2025-07-31 04:30:12 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 12%
2025-07-31 04:30:12 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 14%
2025-07-31 04:30:13 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 15%
2025-07-31 04:30:13 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 16%
2025-07-31 04:30:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 18%
2025-07-31 04:30:14 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 19%
2025-07-31 04:30:15 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 20%
2025-07-31 04:30:15 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 21%
2025-07-31 04:30:16 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 23%
2025-07-31 04:30:16 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 24%
2025-07-31 04:30:17 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 26%
2025-07-31 04:30:17 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 27%
2025-07-31 04:30:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 28%
2025-07-31 04:30:18 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 30%
2025-07-31 04:30:19 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 32%
2025-07-31 04:30:19 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 33%
2025-07-31 04:30:20 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 34%
2025-07-31 04:30:21 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 36%
2025-07-31 04:30:21 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 36%
2025-07-31 04:30:22 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 37%
2025-07-31 04:30:22 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 38%
2025-07-31 04:30:23 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 38%
2025-07-31 04:30:23 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 39%
2025-07-31 04:30:24 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 39%
2025-07-31 04:30:24 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 40%
2025-07-31 04:30:25 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 41%
2025-07-31 04:30:26 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 42%
2025-07-31 04:30:26 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 43%
2025-07-31 04:30:27 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 43%
2025-07-31 04:30:27 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 45%
2025-07-31 04:30:28 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 46%
2025-07-31 04:30:28 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 47%
2025-07-31 04:30:29 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 48%
2025-07-31 04:30:29 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 49%
2025-07-31 04:30:30 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 50%
2025-07-31 04:30:30 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 51%
2025-07-31 04:30:31 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 52%
2025-07-31 04:30:31 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 53%
2025-07-31 04:30:32 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 54%
2025-07-31 04:30:32 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 55%
2025-07-31 04:30:33 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 56%
2025-07-31 04:30:33 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 57%
2025-07-31 04:30:34 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 58%
2025-07-31 04:30:35 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 59%
2025-07-31 04:30:35 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 60%
2025-07-31 04:30:36 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 61%
2025-07-31 04:30:36 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 62%
2025-07-31 04:30:37 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 63%
2025-07-31 04:30:37 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 64%
2025-07-31 04:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 65%
2025-07-31 04:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 67%
2025-07-31 04:30:39 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 68%
2025-07-31 04:30:39 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 69%
2025-07-31 04:30:40 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 70%
2025-07-31 04:30:40 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 71%
2025-07-31 04:30:41 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 72%
2025-07-31 04:30:41 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 73%
2025-07-31 04:30:42 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 74%
2025-07-31 04:30:42 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 75%
2025-07-31 04:30:43 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 76%
2025-07-31 04:30:43 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 77%
2025-07-31 04:30:44 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 78%
2025-07-31 04:30:44 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 79%
2025-07-31 04:30:45 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 80%
2025-07-31 04:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 82%
2025-07-31 04:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 83%
2025-07-31 04:30:47 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 84%
2025-07-31 04:30:47 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 85%
2025-07-31 04:30:48 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 87%
2025-07-31 04:30:48 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 88%
2025-07-31 04:30:49 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 89%
2025-07-31 04:30:49 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 91%
2025-07-31 04:30:50 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 92%
2025-07-31 04:30:50 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 93%
2025-07-31 04:30:51 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 95%
2025-07-31 04:30:51 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 96%
2025-07-31 04:30:51 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 99%
2025-07-31 04:30:52 - CoverEdit.VideoExportWorker - INFO - info:113 - FFmpeg执行成功
2025-07-31 04:30:52 - CoverEdit.CoverEditModule - INFO - info:113 - 导出进度: 100%
2025-07-31 04:30:52 - CoverEdit.CoverEditModule - INFO - info:113 - 导出成功: C:/Users/<USER>/Desktop/369.mp4
2025-07-31 04:32:08 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:32:08 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:32:08 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:32:08 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:32:09 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:32:09 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:32:09 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:32:09 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:32:09 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:33:03 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:33:03 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:33:03 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:33:03 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:33:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:33:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:33:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:33:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:33:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:34:37 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:34:37 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2720帧，当前第15帧
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 04:34:49 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 04:40:23 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:40:23 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:40:23 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:40:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:40:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:40:24 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:40:24 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:40:24 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:40:24 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:41:10 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:41:10 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:41:10 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:41:10 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 🔄 toggle_batch_mode called with checked=True
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 batch_settings_widget exists, current visible: False
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 面板已显示，visible: False
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 🔄 toggle_batch_mode called with checked=False
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 batch_settings_widget exists, current visible: False
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 面板已隐藏，visible: False
2025-07-31 04:41:11 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:43:03 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:43:03 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:43:03 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:43:03 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:43:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:43:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:43:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:43:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:43:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:44:44 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:44:44 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:44:44 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:44:44 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:44:45 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:44:45 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:44:45 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:44:45 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:44:45 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:45:49 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:45:49 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:45:49 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:45:49 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:45:50 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:45:50 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:45:50 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:45:50 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:45:50 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:46:19 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:46:19 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:46:19 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:46:19 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:46:20 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:46:44 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:46:44 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:46:45 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:47:10 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:47:10 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:47:11 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 04:53:21 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 04:53:21 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 04:53:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 Ctrl+1 快捷键被触发
2025-07-31 04:53:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 批量处理日志已显示
2025-07-31 04:53:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 Ctrl+1 快捷键被触发
2025-07-31 04:53:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 批量处理日志已隐藏
2025-07-31 04:53:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 Ctrl+1 快捷键被触发
2025-07-31 04:53:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 批量处理日志已显示
2025-07-31 04:53:27 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 05:15:46 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 05:15:47 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 05:15:47 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-981帧，当前第15帧
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: square, 原始: 1440x2560, 缩放至: 720x960)
2025-07-31 05:15:55 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 05:18:31 - CoverEdit.CoverEditModule - INFO - info:113 - 文字编辑模式: 开启
2025-07-31 05:18:33 - CoverEdit.CoverEditModule - INFO - info:113 - 文字编辑模式: 关闭
2025-07-31 05:18:37 - CoverEdit.CoverEditModule - INFO - info:113 - 文字编辑模式: 开启
2025-07-31 05:18:38 - CoverEdit.CoverEditModule - INFO - info:113 - 文字编辑模式: 关闭
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 05:37:28 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 05:37:28 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 05:37:53 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 05:37:55 - CoverEdit.CoverEditModule - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 05:46:32 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 05:46:32 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 05:46:32 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 05:46:32 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 05:46:33 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 05:46:33 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 05:46:33 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 05:46:33 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 05:46:33 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 05:46:35 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 05:49:10 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 05:49:10 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#户外折叠椅-快手.mp4
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1494帧，当前第15帧
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 05:49:21 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#户外折叠椅-快手.mp4
2025-07-31 05:49:29 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 05:55:02 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 05:55:02 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 05:55:02 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 05:55:02 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 05:55:03 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 05:55:03 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 05:55:03 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 05:55:03 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 05:55:03 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 05:55:06 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 05:56:03 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 05:56:03 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 05:56:03 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 05:56:03 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:\Users\<USER>\Desktop\Video\保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 05:56:04 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:\Users\<USER>\Desktop\Video\保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 05:56:06 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 05:56:06 - CoverEdit.TextHandler - INFO - info:113 - 保存文字到封面
2025-07-31 05:56:06 - CoverEdit.TextHandler - INFO - info:113 - 💾 退出文字编辑模式并保存
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:06:39 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:06:39 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:06:47 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 12:06:47 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:06:47 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:06:47 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:06:47 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-981帧，当前第15帧
2025-07-31 12:06:47 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:06:48 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: square, 原始: 1440x2560, 缩放至: 720x960)
2025-07-31 12:06:48 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 12:06:52 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:07:26 - CoverEdit.TextHandler - INFO - info:113 - 保存文字到封面
2025-07-31 12:07:26 - CoverEdit.TextHandler - INFO - info:113 - 💾 退出文字编辑模式并保存
2025-07-31 12:09:41 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:09:43 - CoverEdit.TextHandler - INFO - info:113 - 保存文字到封面
2025-07-31 12:09:43 - CoverEdit.TextHandler - INFO - info:113 - 💾 退出文字编辑模式并保存
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:15:04 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:15:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:\Users\<USER>\Desktop\Video\保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:15:05 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:\Users\<USER>\Desktop\Video\保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 12:15:07 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:16:19 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:16:19 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:25:19 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:25:19 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#项链 #项链吊坠 #合金项链 #首饰控-快手.mp4
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1634帧，当前第15帧
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:25:30 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#项链 #项链吊坠 #合金项链 #首饰控-快手.mp4
2025-07-31 12:25:35 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:26:09 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:26:09 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:26:18 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/辅助视频/time_1.mp4
2025-07-31 12:26:18 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:26:19 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:26:19 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:26:19 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-139帧，当前第15帧
2025-07-31 12:26:19 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:26:19 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 12:26:19 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/辅助视频/time_1.mp4
2025-07-31 12:26:21 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:27:44 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:27:44 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/封面合成/5g监控摄像头2025新款家用无线连手机远程360度云台旋转双向语音 #一起来省钱 #大家都在买的商品 #快手省钱技巧 #好物分享清单-快手_transcoded.mov
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2033帧，当前第15帧
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1408)
2025-07-31 12:27:52 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/封面合成/5g监控摄像头2025新款家用无线连手机远程360度云台旋转双向语音 #一起来省钱 #大家都在买的商品 #快手省钱技巧 #好物分享清单-快手_transcoded.mov
2025-07-31 12:27:55 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:32:59 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:32:59 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2326帧，当前第15帧
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 12:33:13 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 12:33:18 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:36:30 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:36:30 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:36:39 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 12:36:39 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:36:39 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:36:39 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:36:39 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2720帧，当前第15帧
2025-07-31 12:36:39 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:36:40 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:36:40 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 12:36:42 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:39:41 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:39:41 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2720帧，当前第15帧
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:39:50 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 12:39:52 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:51:18 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:51:18 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:51:32 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 12:51:33 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:53:39 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:53:39 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-770帧，当前第15帧
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:53:46 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 12:53:52 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:54:22 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:54:22 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-770帧，当前第15帧
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:54:41 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 12:54:43 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:55:08 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:55:08 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:55:08 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:55:08 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:55:09 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:55:09 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:55:09 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:55:09 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:55:09 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:55:09 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 12:55:09 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块析构
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 12:58:25 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 12:58:25 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 12:58:41 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 12:58:42 - CoverEdit.TextHandler - INFO - info:113 - 📝 进入文字编辑模式
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:10:33 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:10:33 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-4110帧，当前第15帧
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:10:40 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#测评 它这个烟嘴过滤器真有那么好用吗？ #焦油过滤器 #烟具-快手.mp4
2025-07-31 13:14:32 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:14:32 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:14:32 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:14:32 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:14:33 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:14:33 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:14:33 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:14:33 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:14:33 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:14:58 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:14:58 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1524帧，当前第15帧
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1288)
2025-07-31 13:15:10 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:21:26 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - ERROR - error:121 - 初始化UI失败: 'PreviewLabel' object has no attribute 'add_text'
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - ERROR - error:121 - UI初始化详细错误: Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 272, in _init_ui
    left_panel = self._create_left_panel()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 332, in _create_left_panel
    self._create_preview_area(left_layout)
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 442, in _create_preview_area
    self.snapshot_preview = PreviewLabel()
                            ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\ui\preview_label.py", line 30, in __init__
    self._setup_text_input()
  File "C:\Users\<USER>\Desktop\优化版\modules\cover_edit\ui\preview_label.py", line 67, in _setup_text_input
    self.text_input.returnPressed.connect(self.add_text)
                                          ^^^^^^^^^^^^^
AttributeError: 'PreviewLabel' object has no attribute 'add_text'

2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:21:26 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:22:00 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 初始化UI失败: 'PreviewLabel' object has no attribute 'add_text'
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - ERROR - error:121 - UI初始化详细错误: Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 272, in _init_ui
    left_panel = self._create_left_panel()
                 ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 332, in _create_left_panel
    self._create_preview_area(left_layout)
  File "c:\Users\<USER>\Desktop\优化版\modules\cover_edit\main_module.py", line 442, in _create_preview_area
    self.snapshot_preview = PreviewLabel()
                            ^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\优化版\modules\cover_edit\ui\preview_label.py", line 30, in __init__
    self._setup_text_input()
  File "c:\Users\<USER>\Desktop\优化版\modules\cover_edit\ui\preview_label.py", line 67, in _setup_text_input
    self.text_input.returnPressed.connect(self.add_text)
                                          ^^^^^^^^^^^^^
AttributeError: 'PreviewLabel' object has no attribute 'add_text'

2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:22:00 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:30:38 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:30:38 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2326帧，当前第15帧
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 13:30:46 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:32:18 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:32:18 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#项链 #项链吊坠 #合金项链 #首饰控-快手.mp4
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1634帧，当前第15帧
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:32:35 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#项链 #项链吊坠 #合金项链 #首饰控-快手.mp4
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:44:56 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:44:56 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#前置过滤器 #过滤器 #好物分享 #好物推荐 @我要上热门(O3x8er38dpbhvbaa) @快手粉条(O3xhcy6vhfzcu3qe) @快手平台帐号(O3xa3cpv8sghbu8m)-快手.mp4
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3036帧，当前第15帧
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:45:09 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#前置过滤器 #过滤器 #好物分享 #好物推荐 @我要上热门(O3x8er38dpbhvbaa) @快手粉条(O3xhcy6vhfzcu3qe) @快手平台帐号(O3xa3cpv8sghbu8m)-快手.mp4
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:47:23 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:47:23 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:47:30 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:48:04 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:48:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:48:11 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:48:35 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:48:35 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:48:43 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 13:48:43 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:48:43 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:48:43 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:48:43 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 13:48:43 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:48:44 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:48:44 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:52:55 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:52:55 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1524帧，当前第15帧
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1288)
2025-07-31 13:53:10 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手.mp4
2025-07-31 13:54:17 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 Ctrl+1 快捷键被触发
2025-07-31 13:54:17 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 批量处理日志已显示
2025-07-31 13:54:18 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 Ctrl+1 快捷键被触发
2025-07-31 13:54:18 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 批量处理日志已隐藏
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 13:58:07 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 13:58:07 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-770帧，当前第15帧
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 13:58:19 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:03:38 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:03:38 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:03:49 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:06:27 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:06:27 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:06:36 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:10:24 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:10:24 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:10:32 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:12:01 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:12:01 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-981帧，当前第15帧
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: square, 原始: 1440x2560, 缩放至: 720x960)
2025-07-31 14:12:07 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:12:36 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:12:36 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-981帧，当前第15帧
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: square, 原始: 1440x2560, 缩放至: 720x960)
2025-07-31 14:12:45 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:19:03 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:19:03 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2326帧，当前第15帧
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 14:19:13 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:23:04 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:23:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:23:13 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 14:23:13 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:23:14 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:23:14 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:23:14 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3034帧，当前第15帧
2025-07-31 14:23:14 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:23:14 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 14:23:14 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 14:37:06 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:37:06 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:37:06 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:37:06 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:37:06 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:37:07 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:37:07 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:37:07 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:37:07 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@极明家居灯饰(O3xq7ub6k2gjx5s4) 的精彩视频-快手.mp4
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2431帧，当前第15帧
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:37:15 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/@极明家居灯饰(O3xq7ub6k2gjx5s4) 的精彩视频-快手.mp4
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:39:26 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:39:26 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:39:36 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:39:36 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:39:42 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 14:39:42 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:39:43 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:39:43 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:39:43 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3034帧，当前第15帧
2025-07-31 14:39:43 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:39:43 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 14:39:43 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:41:41 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:41:41 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:43:41 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:43:41 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2720帧，当前第15帧
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:43:48 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/@桃桃吃图(O3xvdr7k5a48z86a) 的精彩视频-快手.mp4
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:44:50 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:44:50 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:44:57 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 14:44:57 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:44:57 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:44:57 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:44:57 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-2326帧，当前第15帧
2025-07-31 14:44:57 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:44:58 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 14:44:58 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/封面合成/这就是能把左右打闭口的磷虾巧饵，出丝棉密，雾化持久，味型浓郁，穿透力强！_爆护饵料-快手_cover_20250731_001657_b7302b9f.mp4
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:52:39 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:52:39 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:52:49 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 14:59:03 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 14:59:03 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 14:59:09 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:03:00 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:03:00 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:03:00 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:03:00 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:03:01 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:03:01 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:03:01 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:03:01 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:03:01 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:05:32 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:05:32 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:05:33 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:05:33 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:05:33 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3034帧，当前第15帧
2025-07-31 15:05:33 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:05:33 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 15:05:33 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:11:27 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:11:27 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 15:11:35 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:17:04 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:17:04 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3034帧，当前第15帧
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 15:17:12 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:18:53 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:18:53 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3034帧，当前第15帧
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 15:19:01 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:28:34 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:28:34 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 15:28:42 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:34:32 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:34:32 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:34:47 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:34:47 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:35:02 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:35:02 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:35:10 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:35:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:35:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:35:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:35:11 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 15:35:11 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:35:11 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 15:35:11 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:35:13 - CoverEdit.CoverEditModule - ERROR - error:121 - 打开文字属性窗口失败: QFontDatabase(a0: QFontDatabase): not enough arguments
2025-07-31 15:35:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 打开文字属性窗口失败: QFontDatabase(a0: QFontDatabase): not enough arguments
2025-07-31 15:35:16 - CoverEdit.CoverEditModule - ERROR - error:121 - 打开文字属性窗口失败: QFontDatabase(a0: QFontDatabase): not enough arguments
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:37:29 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:37:29 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/封面合成/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手_transcoded.mov
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1524帧，当前第15帧
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1288)
2025-07-31 15:37:36 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/封面合成/V8双响小钢炮也太酷了！续航高 音质好 大音量 颜值也很高，关键价格还特别香#机甲小钢炮 #低音炮 #炫酷音箱  #蓝牙音响-快手_transcoded.mov
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:53:33 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:53:33 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-1500帧，当前第15帧
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 15:53:40 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/保温壶就要现在反季去买，你们看看现在才啥价#保温壶 #好物推荐🔥 #多喝热水 @强哥🌈静姐五口之家(O3x3kir7wefxeceu) @静姐的日常(O3xv66yed8rbbw4e)-快手.mp4
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 15:54:46 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 15:54:46 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-3034帧，当前第15帧
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 1080x1920)
2025-07-31 15:54:54 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/遥远/20250731043600-好_t.mp4
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 16:22:48 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 16:22:48 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-981帧，当前第15帧
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: square, 原始: 1440x2560, 缩放至: 720x960)
2025-07-31 16:22:55 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #5月20日 #玫瑰花小夜灯-快手.mp4
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - 初始化封面编辑模块
2025-07-31 16:26:00 - CoverEdit.SimpleBatchProcessor - INFO - info:113 - 简洁批量处理器初始化完成
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - 所有管理器初始化完成
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - 🔧 开始初始化UI...
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - ✅ UI初始化完成
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器设置完成
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - UI信号连接完成
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - 📋 快捷键设置完成: Ctrl+1 切换批量处理日志
2025-07-31 16:26:00 - CoverEdit.CoverEditModule - INFO - info:113 - 封面编辑模块初始化完成
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 媒体播放器加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 10->0
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式跳转: 当前帧(0) -> 开始帧(15)
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 裁剪设置更新: 开头 15->15, 结尾 0->10
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 截图模式范围更新: 第15-770帧，当前第15帧
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 设置默认裁剪值: 开头15帧, 结尾10帧
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 自动加载匹配封面: 序列.00_00_00_00.Still007.jpg (类型: portrait, 原始: 1440x2560, 缩放至: 720x1280)
2025-07-31 16:26:10 - CoverEdit.CoverEditModule - INFO - info:113 - 成功加载视频: C:/Users/<USER>/Desktop/Video/#好物推荐 #蓝牙耳机 #耳机 #好物分享-快手.mp4
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:00 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标按下事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:01 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标释放事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:02 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:04 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:05 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
2025-07-31 16:27:15 - CoverEdit.CoverEditModule - ERROR - error:121 - 文本输入框鼠标移动事件处理失败: wrapped C/C++ object of type QLineEdit has been deleted
