#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单构建器
负责构建菜单栏和工具栏，从原模块迁移而来
"""

import os
from PyQt6.QtWidgets import (QMenuBar, QMenu, QToolBar, QStatusBar, 
                            QLabel, QProgressBar)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction, QKeySequence

# 导入常量
from cover_edit.utils.constants import CoverEditConstants

class MenuBuilder:
    """菜单构建器 - 从原模块迁移"""
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
    
    def build_menu_bar(self):
        """构建菜单栏"""
        try:
            self.logger.debug("开始构建菜单栏")

            # 检查是否有menuBar方法（QMainWindow才有）
            if not hasattr(self.parent, 'menuBar'):
                self.logger.warning("父组件不支持菜单栏，跳过菜单栏构建")
                return

            # 创建菜单栏
            menubar = self.parent.menuBar()
            
            # 文件菜单
            self._create_file_menu(menubar)
            
            # 编辑菜单
            self._create_edit_menu(menubar)
            
            # 视图菜单
            self._create_view_menu(menubar)
            
            # 工具菜单
            self._create_tools_menu(menubar)
            
            # 帮助菜单
            self._create_help_menu(menubar)
            
            self.logger.debug("菜单栏构建完成")
            
        except Exception as e:
            self.logger.error(f"构建菜单栏失败: {e}")
            raise
    
    def _create_file_menu(self, menubar):
        """创建文件菜单"""
        file_menu = menubar.addMenu('文件(&F)')
        
        # 打开视频
        open_video_action = QAction('打开视频(&O)', self.parent)
        open_video_action.setShortcut(QKeySequence.StandardKey.Open)
        open_video_action.setStatusTip('打开视频文件')
        open_video_action.triggered.connect(self.parent.load_video)
        file_menu.addAction(open_video_action)
        
        # 导入封面
        import_cover_action = QAction('导入封面(&I)', self.parent)
        import_cover_action.setShortcut('Ctrl+I')
        import_cover_action.setStatusTip('导入封面图片')
        if hasattr(self.parent, 'import_cover_image'):
            import_cover_action.triggered.connect(self.parent.import_cover_image)
        file_menu.addAction(import_cover_action)
        
        file_menu.addSeparator()
        
        # 导出视频
        export_action = QAction('导出视频(&E)', self.parent)
        export_action.setShortcut('Ctrl+E')
        export_action.setStatusTip('导出带封面的视频')
        if hasattr(self.parent, 'export_video'):
            export_action.triggered.connect(self.parent.export_video)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&X)', self.parent)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip('退出程序')
        exit_action.triggered.connect(self.parent.close)
        file_menu.addAction(exit_action)
        
        # 保存引用
        self.parent.file_menu = file_menu
        self.parent.open_video_action = open_video_action
        self.parent.import_cover_action = import_cover_action
        self.parent.export_action = export_action
    
    def _create_edit_menu(self, menubar):
        """创建编辑菜单"""
        edit_menu = menubar.addMenu('编辑(&E)')
        
        # 撤销
        undo_action = QAction('撤销(&U)', self.parent)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.setStatusTip('撤销上一步操作')
        undo_action.setEnabled(False)  # 暂时禁用
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction('重做(&R)', self.parent)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.setStatusTip('重做上一步操作')
        redo_action.setEnabled(False)  # 暂时禁用
        edit_menu.addAction(redo_action)
        
        edit_menu.addSeparator()
        
        # 清除所有图层
        clear_layers_action = QAction('清除所有图层(&C)', self.parent)
        clear_layers_action.setShortcut('Ctrl+Shift+C')
        clear_layers_action.setStatusTip('清除所有封面图层')
        if hasattr(self.parent, 'clear_all_layers'):
            clear_layers_action.triggered.connect(self.parent.clear_all_layers)
        edit_menu.addAction(clear_layers_action)
        
        # 保存引用
        self.parent.edit_menu = edit_menu
        self.parent.undo_action = undo_action
        self.parent.redo_action = redo_action
        self.parent.clear_layers_action = clear_layers_action
    
    def _create_view_menu(self, menubar):
        """创建视图菜单"""
        view_menu = menubar.addMenu('视图(&V)')
        
        # 全屏
        fullscreen_action = QAction('全屏(&F)', self.parent)
        fullscreen_action.setShortcut('F11')
        fullscreen_action.setStatusTip('切换全屏模式')
        fullscreen_action.setCheckable(True)
        fullscreen_action.triggered.connect(self._toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        view_menu.addSeparator()
        
        # 显示/隐藏工具栏
        toolbar_action = QAction('工具栏(&T)', self.parent)
        toolbar_action.setStatusTip('显示/隐藏工具栏')
        toolbar_action.setCheckable(True)
        toolbar_action.setChecked(True)
        toolbar_action.triggered.connect(self._toggle_toolbar)
        view_menu.addAction(toolbar_action)
        
        # 显示/隐藏状态栏
        statusbar_action = QAction('状态栏(&S)', self.parent)
        statusbar_action.setStatusTip('显示/隐藏状态栏')
        statusbar_action.setCheckable(True)
        statusbar_action.setChecked(True)
        statusbar_action.triggered.connect(self._toggle_statusbar)
        view_menu.addAction(statusbar_action)
        
        # 保存引用
        self.parent.view_menu = view_menu
        self.parent.fullscreen_action = fullscreen_action
        self.parent.toolbar_action = toolbar_action
        self.parent.statusbar_action = statusbar_action
    
    def _create_tools_menu(self, menubar):
        """创建工具菜单"""
        tools_menu = menubar.addMenu('工具(&T)')
        
        # 批量处理
        batch_action = QAction('批量处理(&B)', self.parent)
        batch_action.setShortcut('Ctrl+B')
        batch_action.setStatusTip('批量处理视频文件')
        if hasattr(self.parent, 'show_batch_dialog'):
            batch_action.triggered.connect(self.parent.show_batch_dialog)
        tools_menu.addAction(batch_action)
        
        # 设置
        settings_action = QAction('设置(&S)', self.parent)
        settings_action.setShortcut('Ctrl+,')
        settings_action.setStatusTip('打开设置对话框')
        if hasattr(self.parent, 'show_settings_dialog'):
            settings_action.triggered.connect(self.parent.show_settings_dialog)
        tools_menu.addAction(settings_action)
        
        # 保存引用
        self.parent.tools_menu = tools_menu
        self.parent.batch_action = batch_action
        self.parent.settings_action = settings_action
    
    def _create_help_menu(self, menubar):
        """创建帮助菜单"""
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 使用说明
        help_action = QAction('使用说明(&H)', self.parent)
        help_action.setShortcut('F1')
        help_action.setStatusTip('查看使用说明')
        if hasattr(self.parent, 'show_help'):
            help_action.triggered.connect(self.parent.show_help)
        help_menu.addAction(help_action)
        
        # 关于
        about_action = QAction('关于(&A)', self.parent)
        about_action.setStatusTip('关于本程序')
        if hasattr(self.parent, 'show_about'):
            about_action.triggered.connect(self.parent.show_about)
        help_menu.addAction(about_action)
        
        # 保存引用
        self.parent.help_menu = help_menu
        self.parent.help_action = help_action
        self.parent.about_action = about_action
    
    def build_toolbar(self):
        """构建工具栏"""
        try:
            self.logger.debug("开始构建工具栏")

            # 检查是否有addToolBar方法（QMainWindow才有）
            if not hasattr(self.parent, 'addToolBar'):
                self.logger.warning("父组件不支持工具栏，跳过工具栏构建")
                return

            # 创建工具栏
            toolbar = self.parent.addToolBar('主工具栏')
            toolbar.setMovable(False)
            
            # 添加常用操作
            if hasattr(self.parent, 'open_video_action'):
                toolbar.addAction(self.parent.open_video_action)
            
            if hasattr(self.parent, 'import_cover_action'):
                toolbar.addAction(self.parent.import_cover_action)
            
            toolbar.addSeparator()
            
            if hasattr(self.parent, 'export_action'):
                toolbar.addAction(self.parent.export_action)
            
            # 保存引用
            self.parent.toolbar = toolbar
            
            self.logger.debug("工具栏构建完成")
            
        except Exception as e:
            self.logger.error(f"构建工具栏失败: {e}")
            raise
    
    def build_status_bar(self):
        """构建状态栏"""
        try:
            self.logger.debug("开始构建状态栏")

            # 检查是否有statusBar方法（QMainWindow才有）
            if not hasattr(self.parent, 'statusBar'):
                self.logger.warning("父组件不支持状态栏，跳过状态栏构建")
                return

            # 创建状态栏
            statusbar = self.parent.statusBar()
            
            # 状态标签
            self.parent.status_label = QLabel("就绪")
            statusbar.addWidget(self.parent.status_label)
            
            # 进度条
            self.parent.progress_bar = QProgressBar()
            self.parent.progress_bar.setVisible(False)
            self.parent.progress_bar.setMaximumWidth(200)
            statusbar.addPermanentWidget(self.parent.progress_bar)
            
            # 视频信息标签
            self.parent.video_info_label = QLabel("")
            statusbar.addPermanentWidget(self.parent.video_info_label)
            
            # 保存引用
            self.parent.statusbar = statusbar
            
            self.logger.debug("状态栏构建完成")
            
        except Exception as e:
            self.logger.error(f"构建状态栏失败: {e}")
            raise
    
    def _toggle_fullscreen(self, checked):
        """切换全屏模式"""
        if checked:
            self.parent.showFullScreen()
        else:
            self.parent.showNormal()
    
    def _toggle_toolbar(self, checked):
        """切换工具栏显示"""
        if hasattr(self.parent, 'toolbar'):
            self.parent.toolbar.setVisible(checked)
    
    def _toggle_statusbar(self, checked):
        """切换状态栏显示"""
        if hasattr(self.parent, 'statusbar'):
            self.parent.statusbar.setVisible(checked)
