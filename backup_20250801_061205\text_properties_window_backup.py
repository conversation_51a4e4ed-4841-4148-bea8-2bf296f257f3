#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字属性窗口模块
🏗️ 模块化设计 - 独立的文字属性设置窗口
"""

import json
import os
from pathlib import Path
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QSlider, QComboBox, QSpinBox,
                            QColorDialog, QApplication, QWidget)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QColor
from ..utils.logger import get_logger


class TextPropertiesWindow(QDialog):
    """
    文字属性设置窗口
    🏗️ 模块化架构 - 独立窗口设计
    """
    
    # 信号定义
    properties_changed = pyqtSignal()  # 属性变化信号
    window_closed = pyqtSignal()       # 窗口关闭信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger("TextPropertiesWindow")
        self.parent_module = parent
        
        # 窗口设置
        self.setWindowTitle("文字属性设置")
        self.setFixedSize(350, 300)  # 缩小窗口尺寸
        self.setModal(False)  # 非模态窗口，可以同时操作主窗口

        # 初始化字体数据库
        self.loaded_fonts = {}  # 存储已加载的字体ID

        # 初始化UI
        self.init_ui()
        
        # 加载保存的字体设置
        self.load_font_settings()

        # 连接信号
        self.setup_connections()

        print("✅ 文字属性窗口创建完成")

    def get_config_file_path(self) -> str:
        """获取配置文件路径"""
        try:
            # 获取项目根目录下的config文件夹
            current_dir = Path(__file__).parent.parent.parent.parent
            config_dir = current_dir / 'config'
            config_dir.mkdir(exist_ok=True)
            return str(config_dir / 'text_properties.json')
        except Exception as e:
            print(f"⚠️ 获取配置文件路径失败: {e}")
            return 'text_properties.json'

    def load_font_settings(self):
        """加载保存的字体设置"""
        try:
            config_file = self.get_config_file_path()
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 恢复字体选择
                last_font = config.get('last_font', '微软雅黑')
                font_index = self.font_combo.findText(last_font)
                if font_index >= 0:
                    self.font_combo.setCurrentIndex(font_index)
                    print(f"📝 恢复字体选择: {last_font}")

                # 恢复字体样式
                last_style = config.get('last_style', '正常')
                style_index = self.font_style_combo.findText(last_style)
                if style_index >= 0:
                    self.font_style_combo.setCurrentIndex(style_index)
                    print(f"📝 恢复字体样式: {last_style}")

        except Exception as e:
            print(f"⚠️ 加载字体设置失败: {e}")

    def save_font_settings(self):
        """保存字体设置"""
        try:
            config_file = self.get_config_file_path()

            # 读取现有配置
            config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 更新字体设置
            config['last_font'] = self.font_combo.currentText()
            config['last_style'] = self.font_style_combo.currentText()

            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            print(f"💾 保存字体设置: {config['last_font']} - {config['last_style']}")

        except Exception as e:
            print(f"⚠️ 保存字体设置失败: {e}")
    
    def init_ui(self):
        """初始化UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)  # 减少边距
        layout.setSpacing(6)  # 减少间距

        # 字体设置组
        font_group = self.create_font_group()
        layout.addWidget(font_group)

        # 颜色设置组（包含描边宽度和背景透明度）
        color_group = self.create_color_group()
        layout.addWidget(color_group)

        # 按钮组
        button_group = self.create_button_group()
        layout.addWidget(button_group)
    
    def create_font_group(self):
        """创建字体设置组"""
        group = QGroupBox("字体设置")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # 字体和样式在一行
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体:"))
        self.font_combo = QComboBox()

        # 暂时使用系统字体，先解决字体应用问题
        font_list = self._get_system_fonts()
        print(f"📝 使用系统字体，总数: {len(font_list)}")
        print(f"📝 前10个字体: {font_list[:10]}")
        self.font_combo.addItems(font_list)
        self.font_combo.setFixedWidth(170)  # 增加宽度以显示长字体名
        font_layout.addWidget(self.font_combo)

        font_layout.addWidget(QLabel("样式:"))
        self.font_style_combo = QComboBox()
        self.font_style_combo.addItems(["正常", "粗体", "斜体"])
        self.font_style_combo.setFixedWidth(80)
        font_layout.addWidget(self.font_style_combo)
        font_layout.addStretch()
        layout.addLayout(font_layout)
        
        # 字体大小
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("大小:"))
        self.font_size_slider = QSlider(Qt.Orientation.Horizontal)
        self.font_size_slider.setRange(12, 120)
        self.font_size_slider.setValue(24)
        self.font_size_label = QLabel("24px")
        size_layout.addWidget(self.font_size_slider)
        size_layout.addWidget(self.font_size_label)
        layout.addLayout(size_layout)

        # 🚀 新增：字体间距和行距设置
        spacing_layout = QHBoxLayout()

        # 字体间距
        spacing_layout.addWidget(QLabel("间距:"))
        self.letter_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.letter_spacing_slider.setRange(-50, 50)  # -5.0px 到 5.0px
        self.letter_spacing_slider.setValue(0)  # 默认0px
        self.letter_spacing_slider.setFixedWidth(100)
        spacing_layout.addWidget(self.letter_spacing_slider)

        self.letter_spacing_label = QLabel("0px")
        self.letter_spacing_label.setFixedWidth(35)
        spacing_layout.addWidget(self.letter_spacing_label)

        # 行距
        spacing_layout.addWidget(QLabel("行距:"))
        self.line_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.line_spacing_slider.setRange(80, 300)  # 0.8 到 3.0 倍行距
        self.line_spacing_slider.setValue(120)  # 默认1.2倍行距
        self.line_spacing_slider.setFixedWidth(100)
        spacing_layout.addWidget(self.line_spacing_slider)

        self.line_spacing_label = QLabel("1.2")
        self.line_spacing_label.setFixedWidth(35)
        spacing_layout.addWidget(self.line_spacing_label)

        spacing_layout.addStretch()
        layout.addLayout(spacing_layout)

        return group
    
    def create_color_group(self):
        """创建颜色设置组"""
        group = QGroupBox("颜色设置")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # 填充颜色
        fill_layout = QHBoxLayout()
        fill_layout.addWidget(QLabel("填充颜色:"))
        self.fill_color_btn = QPushButton()
        self.fill_color_btn.setFixedSize(60, 25)
        self.fill_color_btn.setStyleSheet("background-color: white; border: 1px solid #ccc;")
        fill_layout.addWidget(self.fill_color_btn)
        fill_layout.addStretch()
        layout.addLayout(fill_layout)

        # 描边颜色和宽度
        stroke_layout = QHBoxLayout()
        stroke_layout.addWidget(QLabel("描边颜色:"))
        self.stroke_color_btn = QPushButton()
        self.stroke_color_btn.setFixedSize(60, 25)
        self.stroke_color_btn.setStyleSheet("background-color: black; border: 1px solid #ccc;")
        stroke_layout.addWidget(self.stroke_color_btn)

        stroke_layout.addWidget(QLabel("宽度:"))
        self.stroke_width_spin = QSpinBox()
        self.stroke_width_spin.setRange(0, 10)
        self.stroke_width_spin.setValue(2)
        self.stroke_width_spin.setFixedWidth(50)
        stroke_layout.addWidget(self.stroke_width_spin)
        stroke_layout.addStretch()
        layout.addLayout(stroke_layout)

        # 背景颜色和透明度
        bg_layout = QHBoxLayout()
        bg_layout.addWidget(QLabel("背景颜色:"))
        self.bg_color_btn = QPushButton()
        self.bg_color_btn.setFixedSize(60, 25)
        self.bg_color_btn.setStyleSheet("background-color: blue; border: 1px solid #ccc;")
        bg_layout.addWidget(self.bg_color_btn)

        # 透明度标签和滑块
        bg_layout.addWidget(QLabel("透明度:"))
        self.bg_opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.bg_opacity_slider.setRange(0, 100)
        self.bg_opacity_slider.setValue(0)  # 🚀 修复：默认透明度为0
        self.bg_opacity_slider.setFixedWidth(120)
        bg_layout.addWidget(self.bg_opacity_slider)

        self.bg_opacity_label = QLabel("0%")  # 🚀 修复：默认显示0%
        self.bg_opacity_label.setFixedWidth(35)
        bg_layout.addWidget(self.bg_opacity_label)
        bg_layout.addStretch()
        layout.addLayout(bg_layout)

        return group
    
    # 🏗️ 效果设置已合并到颜色设置组中
    
    def create_button_group(self):
        """创建按钮组"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 0)
        layout.setSpacing(8)

        self.apply_btn = QPushButton("应用")
        self.apply_btn.setFixedHeight(30)
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setFixedHeight(30)
        self.close_btn = QPushButton("关闭")
        self.close_btn.setFixedHeight(30)

        layout.addWidget(self.apply_btn)
        layout.addWidget(self.reset_btn)
        layout.addWidget(self.close_btn)

        return widget
    
    def setup_connections(self):
        """设置信号连接"""
        # 字体属性变化
        self.font_combo.currentTextChanged.connect(self.on_font_changed)
        self.font_style_combo.currentTextChanged.connect(self.on_font_style_changed)
        self.font_size_slider.valueChanged.connect(self.on_font_size_changed)

        # 🚀 新增：字体间距和行距变化
        self.letter_spacing_slider.valueChanged.connect(self.on_letter_spacing_changed)
        self.line_spacing_slider.valueChanged.connect(self.on_line_spacing_changed)
        
        # 颜色按钮
        self.fill_color_btn.clicked.connect(self.choose_fill_color)
        self.stroke_color_btn.clicked.connect(self.choose_stroke_color)
        self.bg_color_btn.clicked.connect(self.choose_bg_color)
        
        # 效果属性变化
        self.stroke_width_spin.valueChanged.connect(self.on_property_changed)
        self.bg_opacity_slider.valueChanged.connect(self.on_bg_opacity_changed)
        
        # 按钮
        self.apply_btn.clicked.connect(self.apply_properties)
        self.reset_btn.clicked.connect(self.reset_properties)
        self.close_btn.clicked.connect(self.close_window)
    
    def on_font_changed(self, font_name):
        """字体选择变化"""
        print(f"📝 字体选择变化: {font_name}")
        self.save_font_settings()  # 保存字体设置
        self.on_property_changed()
        print(f"📝 字体已变更: {font_name}")

    def on_font_style_changed(self, style_name):
        """字体样式变化"""
        print(f"📝 字体样式选择变化: {style_name}")
        self.save_font_settings()  # 保存字体设置
        self.on_property_changed()
        print(f"📝 字体样式已变更: {style_name}")

    def on_font_size_changed(self, value):
        """字体大小变化"""
        print(f"📝 字体大小变化: {value}px")
        self.font_size_label.setText(f"{value}px")
        self.on_property_changed()

    def on_letter_spacing_changed(self, value):
        """字体间距变化"""
        spacing_px = value / 10.0  # 转换为像素值，范围 -5.0px 到 5.0px
        print(f"📝 字体间距变化: {spacing_px}px")
        self.letter_spacing_label.setText(f"{spacing_px:.1f}px")
        self.on_property_changed()

    def on_line_spacing_changed(self, value):
        """行距变化"""
        line_height = value / 100.0  # 转换为倍数，范围 0.8 到 3.0
        print(f"📝 行距变化: {line_height:.1f}")
        self.line_spacing_label.setText(f"{line_height:.1f}")
        self.on_property_changed()

    def on_bg_opacity_changed(self, value):
        """背景透明度变化"""
        self.bg_opacity_label.setText(f"{value}%")
        self.on_property_changed()

    def on_property_changed(self):
        """属性变化时发出信号"""
        print("🎨 发出文字属性变化信号...")
        self.properties_changed.emit()
        print("🎨 文字属性已变化，信号已发出")
    
    def choose_fill_color(self):
        """选择填充颜色"""
        color = QColorDialog.getColor(QColor(255, 255, 255), self, "选择填充颜色")
        if color.isValid():
            self.fill_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            self.on_property_changed()
    
    def choose_stroke_color(self):
        """选择描边颜色"""
        color = QColorDialog.getColor(QColor(0, 0, 0), self, "选择描边颜色")
        if color.isValid():
            self.stroke_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            self.on_property_changed()
    
    def choose_bg_color(self):
        """选择背景颜色"""
        color = QColorDialog.getColor(QColor(0, 0, 255), self, "选择背景颜色")
        if color.isValid():
            self.bg_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            self.on_property_changed()
    
    def apply_properties(self):
        """应用属性"""
        print("✅ 应用文字属性")
        self.properties_changed.emit()
    
    def reset_properties(self):
        """重置属性"""
        self.font_combo.setCurrentText("微软雅黑")
        self.font_style_combo.setCurrentText("正常")
        self.font_size_slider.setValue(24)
        self.stroke_width_spin.setValue(2)
        self.bg_opacity_slider.setValue(0)  # 🚀 修复：重置时透明度为0

        # 🚀 新增：重置字体间距和行距
        self.letter_spacing_slider.setValue(0)  # 默认0px间距
        self.line_spacing_slider.setValue(120)  # 默认1.2倍行距
        
        self.fill_color_btn.setStyleSheet("background-color: white; border: 1px solid #ccc;")
        self.stroke_color_btn.setStyleSheet("background-color: black; border: 1px solid #ccc;")
        self.bg_color_btn.setStyleSheet("background-color: blue; border: 1px solid #ccc;")
        
        print("🔄 文字属性已重置")
        self.on_property_changed()
    
    def close_window(self):
        """关闭窗口"""
        self.window_closed.emit()
        self.close()
        print("❌ 文字属性窗口已关闭")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.window_closed.emit()
        super().closeEvent(event)
    
    def get_current_properties(self):
        """获取当前属性设置"""
        return {
            'font_family': self.font_combo.currentText(),
            'font_style': self.font_style_combo.currentText(),
            'font_size': self.font_size_slider.value(),
            'stroke_width': self.stroke_width_spin.value(),
            'bg_opacity': self.bg_opacity_slider.value(),
            # 🚀 新增：字体间距和行距属性
            'letter_spacing': self.letter_spacing_slider.value() / 10.0,  # 转换为像素值
            'line_spacing': self.line_spacing_slider.value() / 100.0  # 转换为倍数
        }

    def _load_azt_fonts(self):
        """加载AZT文件夹中的字体文件"""
        try:
            from pathlib import Path
            from PyQt6.QtGui import QFontDatabase

            print("🔤 开始加载字体...")

            # 获取系统字体作为基础
            font_list = self._get_system_fonts()
            print(f"📝 系统字体数量: {len(font_list)}")

            # 获取AZT文件夹路径
            current_dir = Path(__file__).parent.parent.parent.parent
            azt_dir = current_dir / "AZT"

            print(f"📁 AZT字体文件夹路径: {azt_dir}")

            if not azt_dir.exists():
                print("❌ AZT文件夹不存在，使用系统字体")
                return font_list

            # 只加载前5个字体文件进行测试
            font_extensions = ['.ttf', '.ttc', '.otf', '.TTF', '.TTC', '.OTF']
            loaded_count = 0
            max_fonts = 5  # 限制加载数量

            for font_file in azt_dir.iterdir():
                if loaded_count >= max_fonts:
                    break

                if font_file.suffix in font_extensions:
                    try:
                        print(f"🔤 正在加载字体: {font_file.name}")

                        # 使用QFontDatabase加载字体文件
                        font_id = QFontDatabase.addApplicationFont(str(font_file))

                        if font_id != -1:
                            # 获取字体族名称
                            font_families = QFontDatabase.applicationFontFamilies(font_id)
                            print(f"   - 字体族: {font_families}")

                            for family in font_families:
                                if family not in font_list:
                                    font_list.append(family)
                                    loaded_count += 1
                                    print(f"   ✅ 添加字体: {family}")

                            # 保存字体ID以便后续管理
                            self.loaded_fonts[font_file.name] = font_id

                        else:
                            print(f"   ⚠️ 无法加载字体文件: {font_file.name}")

                    except Exception as e:
                        print(f"   ❌ 加载字体文件失败 {font_file.name}: {e}")

            print(f"✅ 成功加载 {loaded_count} 个AZT字体")
            print(f"📝 总字体数量: {len(font_list)}")

            return font_list

        except Exception as e:
            print(f"❌ 加载AZT字体失败: {e}")
            return self._get_system_fonts()

    def _get_system_fonts(self):
        """获取系统字体列表"""
        try:
            from PyQt6.QtGui import QFontDatabase
            system_fonts = QFontDatabase.families()

            # 优先使用的字体列表
            preferred_fonts = [
                "微软雅黑", "Microsoft YaHei", "宋体", "SimSun", "黑体", "SimHei",
                "楷体", "KaiTi", "Arial", "Times New Roman", "Calibri", "Consolas"
            ]

            # 构建字体列表：优先字体 + 系统字体
            font_list = []

            # 添加优先字体（如果存在）
            for font in preferred_fonts:
                if font in system_fonts:
                    font_list.append(font)

            # 添加其他系统字体（排除已添加的和垂直字体）
            for font in sorted(system_fonts):
                if font not in font_list and not font.startswith('@'):
                    font_list.append(font)

            return font_list

        except Exception as e:
            print(f"❌ 获取系统字体失败: {e}")
            return ["Arial", "微软雅黑"]
