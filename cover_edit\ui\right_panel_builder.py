#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
右侧面板构建器
负责构建右侧面板的所有组件，从原模块完整迁移
"""

import os
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QComboBox, QSlider, QPushButton, QSizePolicy,
                            QLineEdit, QCheckBox, QTextEdit, QSpinBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor

# 导入常量
from cover_edit.utils.constants import CoverEditConstants

class RightPanelBuilder:
    """右侧面板构建器 - 从原模块完整迁移"""
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
    
    def build_right_panel(self):
        """构建右侧面板 - 与原模块完全一致"""
        try:
            self.logger.debug("开始构建右侧面板")
            
            # 🔒 创建封面预览 - 与原模块完全一致
            self._create_cover_preview()
            
            # 🏗️ 新设计：文字属性组已改为独立窗口，不再在主界面创建
            
            # 🔒 创建导出按钮 - 与原模块完全一致
            self._create_export_button()
            
            # 🔒 创建批量处理组 - 与原模块完全一致
            self._create_batch_group()
            
            self.logger.debug("右侧面板构建完成")
            
        except Exception as e:
            self.logger.error(f"构建右侧面板失败: {e}")
            raise

    def _get_preview_style(self) -> str:
        """获取预览样式 - 与原模块完全一致"""
        return """
            QLabel {
                background-color: #181A1F;
                border: 2px dashed #5C6370;
                border-radius: 6px;
                color: #5C6370;
                font-size: 16px;
                font-style: italic;
                padding: 0px;
            }
        """
    
    def _create_cover_preview(self):
        """创建封面预览 - 与原模块完全一致"""
        # 🔒 创建封面预览容器 - 与原模块完全一致
        cover_container = QWidget()
        cover_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        cover_layout = QVBoxLayout(cover_container)
        cover_layout.setContentsMargins(0, 0, 0, 0)
        cover_layout.setSpacing(0)
        
        # 🔒 创建封面预览标签 - 与原模块完全一致
        from .preview_label import PreviewLabel
        self.parent.cover_preview = PreviewLabel()
        # 🔧 修复：设置合适的最小尺寸以保证预览质量
        self.parent.cover_preview.setMinimumSize(400, 600)  # 9:16比例的合适尺寸
        self.parent.cover_preview.setStyleSheet(self._get_preview_style())
        self.parent.cover_preview.setText("请先导入视频\n系统将自动匹配合适的封面")
        
        # 🏗️ 新设计：不再创建文字图层，改为独立窗口模式
        
        # 初始化尺寸记录
        self.parent._last_preview_size = None
        
        # 🔒 设置预览缩放事件处理 - 与原模块完全一致
        self._setup_preview_resize_handler()
        
        # 添加预览到容器
        cover_layout.addWidget(self.parent.cover_preview)
        
        # 🔒 添加到右侧布局 - 与原模块完全一致
        central_widget = self.parent.centralWidget()
        if central_widget and hasattr(central_widget, 'right_layout'):
            central_widget.right_layout.addWidget(cover_container, 1)  # 拉伸因子为1
        elif hasattr(central_widget, 'right_panel') and central_widget.right_panel:
            layout = central_widget.right_panel.layout()
            if layout:
                layout.addWidget(cover_container, 1)
    
    def _setup_preview_resize_handler(self):
        """设置预览缩放事件处理 - 与原模块完全一致"""
        # 保存原始的 resizeEvent 方法
        self.parent.cover_preview._original_resizeEvent = self.parent.cover_preview.resizeEvent
        
        # 创建新的 resizeEvent 方法，同时调用原始方法和我们的方法
        def combined_resizeEvent(event):
            self.parent.cover_preview._original_resizeEvent(event)  # 调用原始缩放逻辑
            self._on_preview_resize(event)  # 调用我们的逻辑
            # 延迟同步，确保尺寸变化完成后再同步
            if not hasattr(self.parent, '_sync_timer'):
                from PyQt6.QtCore import QTimer
                self.parent._sync_timer = QTimer()
                self.parent._sync_timer.setSingleShot(True)
                # 🏗️ 新设计：不再需要文字图层同步
            self.parent._sync_timer.start(100)  # 100ms后同步
        
        self.parent.cover_preview.resizeEvent = combined_resizeEvent
    
    def _on_preview_resize(self, event):
        """预览缩放事件处理 - 与原模块完全一致"""
        try:
            # 记录新的尺寸
            new_size = event.size()
            self.parent._last_preview_size = (new_size.width(), new_size.height())
        except Exception as e:
            self.logger.error(f"预览缩放事件处理失败: {e}")
    
    # 🏗️ 新设计：不再需要文字图层同步方法
    
    # 🏗️ 新设计：文字属性组已改为独立窗口，移除所有相关方法


    def _create_export_button(self):
        """创建导出按钮区域 - 与原模块完全一致"""
        from PyQt6.QtWidgets import QHBoxLayout

        # 🔒 创建导出区域的水平布局 - 与原模块完全一致
        export_layout = QHBoxLayout()
        export_layout.setSpacing(10)

        # 🔒 统一的按钮样式 - 与原模块完全一致
        button_style = """
            QPushButton {
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:disabled {
                background-color: #5C6370;
                color: #ABB2BF;
            }
        """

        # 🔒 文字编辑按钮 - 与原模块完全一致
        self.parent.text_edit_btn_export = QPushButton("文字编辑")
        self.parent.text_edit_btn_export.setFixedHeight(50)
        self.parent.text_edit_btn_export.setStyleSheet(button_style + """
            QPushButton {
                background-color: #61AFEF;
            }
            QPushButton:hover {
                background-color: #528BFF;
            }
        """)
        self.parent.text_edit_btn_export.setEnabled(False)  # 初始状态禁用，直到有封面图片

        # 🔒 导出设置按钮 - 与原模块完全一致
        self.parent.export_settings_btn_export = QPushButton("导出设置")
        self.parent.export_settings_btn_export.setFixedHeight(50)
        self.parent.export_settings_btn_export.setStyleSheet(button_style + """
            QPushButton {
                background-color: #98C379;
            }
            QPushButton:hover {
                background-color: #7FB069;
            }
        """)

        # 🔒 导出视频按钮 - 与原模块完全一致
        self.parent.export_btn = QPushButton("导出视频")
        self.parent.export_btn.setFixedHeight(50)
        self.parent.export_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #E06C75;
            }
            QPushButton:hover {
                background-color: #D55A65;
            }
        """)
        self.parent.export_btn.setEnabled(False)  # 初始禁用

        # 🔒 添加按钮到布局（等宽分布） - 与原模块完全一致
        export_layout.addWidget(self.parent.text_edit_btn_export, 1)
        export_layout.addWidget(self.parent.export_settings_btn_export, 1)
        export_layout.addWidget(self.parent.export_btn, 1)

        # 🔒 添加到右侧布局 - 与原模块完全一致
        central_widget = self.parent.centralWidget()
        if central_widget and hasattr(central_widget, 'right_layout'):
            central_widget.right_layout.addLayout(export_layout)
        elif hasattr(central_widget, 'right_panel') and central_widget.right_panel:
            layout = central_widget.right_panel.layout()
            if layout:
                layout.addLayout(export_layout)
    
    def _create_batch_group(self):
        """创建批量处理组 - 与原模块完全一致"""
        # 🔒 批量处理组框 - 与原模块完全一致
        batch_group = QGroupBox("批量处理")
        batch_layout = QVBoxLayout(batch_group)
        batch_layout.setContentsMargins(10, 15, 10, 10)
        batch_layout.setSpacing(8)
        
        # 🔒 批量处理开关行（水平布局） - 与原模块完全一致

        batch_header_layout = QHBoxLayout()

        self.parent.batch_mode_checkbox = QCheckBox("▲ 启用批量处理模式")
        self.parent.batch_mode_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ABB2BF;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 0px;
                height: 0px;
            }
        """)
        batch_header_layout.addWidget(self.parent.batch_mode_checkbox)

        # 🔒 添加弹性空间 - 与原模块完全一致
        batch_header_layout.addStretch()

        # 🔒 重置处理记录按钮（最简单样式） - 与原模块完全一致
        self.parent.reset_batch_btn = QPushButton("重置记录")
        self.parent.reset_batch_btn.setFixedHeight(24)
        self.parent.reset_batch_btn.setFixedWidth(80)
        self.parent.reset_batch_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                font-size: 12px;
                padding: 2px 6px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #999999;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        batch_header_layout.addWidget(self.parent.reset_batch_btn)

        batch_layout.addLayout(batch_header_layout)

        # 🔒 批量处理设置（初始隐藏） - 与原模块完全一致
        self.parent.batch_settings_widget = self._create_batch_settings()
        self.parent.batch_settings_widget.hide()  # 初始隐藏
        batch_layout.addWidget(self.parent.batch_settings_widget)
        
        # 🔒 添加到右侧布局 - 与原模块完全一致
        central_widget = self.parent.centralWidget()
        if central_widget and hasattr(central_widget, 'right_layout'):
            central_widget.right_layout.addWidget(batch_group)
        elif hasattr(central_widget, 'right_panel') and central_widget.right_panel:
            layout = central_widget.right_panel.layout()
            if layout:
                layout.addWidget(batch_group)

    def _create_batch_settings(self):
        """创建批量处理设置 - 与原模块完全一致"""

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 🔒 监控文件夹 - 与原模块完全一致
        folder_layout = QHBoxLayout()
        folder_layout.addWidget(QLabel("监控文件夹:"))
        self.parent.watch_folder_edit = QLineEdit()
        self.parent.watch_folder_edit.setPlaceholderText("选择要监控的文件夹...")
        self.parent.watch_folder_edit.setReadOnly(True)
        folder_layout.addWidget(self.parent.watch_folder_edit)

        self.parent.browse_watch_folder_btn = QPushButton("浏览")
        self.parent.browse_watch_folder_btn.setFixedWidth(60)
        folder_layout.addWidget(self.parent.browse_watch_folder_btn)

        layout.addLayout(folder_layout)

        # 🔒 输出文件夹 - 与原模块完全一致
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出文件夹:"))
        self.parent.output_folder_edit = QLineEdit()
        self.parent.output_folder_edit.setPlaceholderText("选择输出文件夹...")
        self.parent.output_folder_edit.setReadOnly(True)
        output_layout.addWidget(self.parent.output_folder_edit)

        self.parent.browse_output_folder_btn = QPushButton("浏览")
        self.parent.browse_output_folder_btn.setFixedWidth(60)
        # 🚀 修复：移除重复的信号连接，在主模块中统一连接
        # self.parent.browse_output_folder_btn.clicked.connect(self.parent.browse_output_folder)
        output_layout.addWidget(self.parent.browse_output_folder_btn)

        layout.addLayout(output_layout)

        # 🔒 批量处理状态和控制 - 与原模块完全一致
        status_layout = QHBoxLayout()

        self.parent.batch_status_label = QLabel("状态: 未启动")
        self.parent.batch_status_label.setStyleSheet("color: #98C379; font-size: 12px;")
        status_layout.addWidget(self.parent.batch_status_label)

        # 🔒 批量处理启动勾选框 - 与原模块完全一致
        self.parent.batch_enable_checkbox = QCheckBox("启动批量处理")
        self.parent.batch_enable_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ABB2BF;
                font-size: 12px;
                margin-left: 20px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #5C6370;
                background-color: transparent;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #98C379;
                background-color: #98C379;
                border-radius: 3px;
            }
        """)
        status_layout.addWidget(self.parent.batch_enable_checkbox)
        status_layout.addStretch()

        layout.addLayout(status_layout)

        # 🔒 日志显示区域（默认隐藏） - 与原模块完全一致
        self.parent.batch_log_display = QTextEdit()
        self.parent.batch_log_display.setMaximumHeight(80)  # 缩小高度从120到80
        self.parent.batch_log_display.setReadOnly(True)
        self.parent.batch_log_display.setVisible(False)  # 默认隐藏
        self.parent.batch_log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #ABB2BF;
                border: 1px solid #5C6370;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                padding: 5px;
            }
            QScrollBar:vertical {
                background-color: #2C2C2C;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #5C6370;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #61AFEF;
            }
        """)
        layout.addWidget(self.parent.batch_log_display)

        return widget
