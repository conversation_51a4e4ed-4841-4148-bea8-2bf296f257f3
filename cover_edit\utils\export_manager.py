#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 导出管理器
专门处理视频导出相关的操作
"""

import os
import subprocess
import tempfile
from typing import Optional, Dict, Any
from PyQt6.QtCore import QObject, pyqtSignal, QThread
from PyQt6.QtGui import QPixmap

from .logger import get_logger
from .constants import CoverEditConstants
from .decorators import error_handler, performance_monitor

class ExportManager(QObject):
    """导出管理器 - 专门处理视频导出操作"""
    
    # 信号定义
    export_started = pyqtSignal(str)  # 输出路径
    export_progress = pyqtSignal(int)  # 进度百分比
    export_finished = pyqtSignal(str, bool, str)  # 输出路径, 是否成功, 错误信息
    export_cancelled = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("ExportManager")
        
        # 导出相关属性
        self.current_export_thread = None
        self.is_exporting = False
        self.ffmpeg_path = self._find_ffmpeg_path()
        
        # 默认导出设置
        self.default_settings = {
            'output_format': 'MP4',
            'video_codec': 'H.264',
            'quality': 'high',
            'use_gpu': False,
            'bitrate': CoverEditConstants.DEFAULT_BITRATE,
            'fps': CoverEditConstants.DEFAULT_FPS
        }
    
    def _find_ffmpeg_path(self) -> str:
        """查找FFmpeg可执行文件路径"""
        try:
            from .path_manager import get_ffmpeg_path

            ffmpeg_path = get_ffmpeg_path()
            if ffmpeg_path:
                self.logger.info(f"找到FFmpeg: {ffmpeg_path}")
                return ffmpeg_path

            self.logger.warning("未找到FFmpeg，某些功能可能无法使用")
            return ''

        except Exception as e:
            self.logger.error(f"查找FFmpeg失败: {e}")
            return ''
    
    @error_handler(show_dialog=True)
    def export_video(self, video_path: str, output_path: str, 
                    cover_image: Optional[QPixmap] = None,
                    start_time: float = 0.0, duration: float = 0.0,
                    export_settings: Optional[Dict[str, Any]] = None) -> bool:
        """
        导出视频
        
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            cover_image: 封面图像（可选）
            start_time: 开始时间（秒）
            duration: 持续时间（秒）
            export_settings: 导出设置
            
        Returns:
            是否成功启动导出
        """
        try:
            if self.is_exporting:
                self.logger.warning("已有导出任务在进行中")
                return False
            
            if not self.ffmpeg_path:
                raise Exception("FFmpeg未找到，无法导出视频")
            
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"输入视频不存在: {video_path}")
            
            # 合并导出设置
            settings = self.default_settings.copy()
            if export_settings:
                settings.update(export_settings)
            
            # 创建导出线程
            self.current_export_thread = VideoExportThread(
                video_path=video_path,
                output_path=output_path,
                cover_image=cover_image,
                start_time=start_time,
                duration=duration,
                export_settings=settings,
                ffmpeg_path=self.ffmpeg_path
            )
            
            # 连接信号
            self.current_export_thread.progress_updated.connect(self.export_progress.emit)
            self.current_export_thread.export_finished.connect(self._on_export_finished)
            self.current_export_thread.finished.connect(self._on_thread_finished)
            
            # 启动导出
            self.is_exporting = True
            self.current_export_thread.start()
            
            self.logger.info(f"开始导出视频: {video_path} -> {output_path}")
            self.export_started.emit(output_path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动视频导出失败: {e}")
            self.is_exporting = False
            return False
    
    def cancel_export(self):
        """取消当前导出"""
        try:
            if self.current_export_thread and self.current_export_thread.isRunning():
                self.current_export_thread.terminate()
                self.current_export_thread.wait(5000)  # 等待5秒
                
                self.logger.info("导出已取消")
                self.export_cancelled.emit()
            
            self.is_exporting = False
            
        except Exception as e:
            self.logger.error(f"取消导出失败: {e}")
    
    def _on_export_finished(self, output_path: str, success: bool, error_msg: str):
        """导出完成处理"""
        try:
            self.is_exporting = False
            
            if success:
                self.logger.info(f"视频导出成功: {output_path}")
            else:
                self.logger.error(f"视频导出失败: {error_msg}")
            
            self.export_finished.emit(output_path, success, error_msg)
            
        except Exception as e:
            self.logger.error(f"导出完成处理失败: {e}")
    
    def _on_thread_finished(self):
        """线程完成处理"""
        try:
            if self.current_export_thread:
                self.current_export_thread.deleteLater()
                self.current_export_thread = None
            
            self.is_exporting = False
            
        except Exception as e:
            self.logger.error(f"线程清理失败: {e}")
    
    def get_export_status(self) -> Dict[str, Any]:
        """获取导出状态"""
        return {
            'is_exporting': self.is_exporting,
            'has_ffmpeg': bool(self.ffmpeg_path),
            'ffmpeg_path': self.ffmpeg_path,
            'thread_running': (self.current_export_thread is not None and 
                             self.current_export_thread.isRunning())
        }

class VideoExportThread(QThread):
    """视频导出线程"""
    
    progress_updated = pyqtSignal(int)
    export_finished = pyqtSignal(str, bool, str)
    
    def __init__(self, video_path: str, output_path: str, 
                 cover_image: Optional[QPixmap] = None,
                 start_time: float = 0.0, duration: float = 0.0,
                 export_settings: Optional[Dict[str, Any]] = None,
                 ffmpeg_path: str = 'ffmpeg'):
        super().__init__()
        
        self.video_path = video_path
        self.output_path = output_path
        self.cover_image = cover_image
        self.start_time = start_time
        self.duration = duration
        self.export_settings = export_settings or {}
        self.ffmpeg_path = ffmpeg_path
        
        self.logger = get_logger("VideoExportThread")
    
    def run(self):
        """执行导出"""
        try:
            self.logger.info("开始视频导出线程")
            
            if self.cover_image and not self.cover_image.isNull():
                # 有封面的导出
                success, error_msg = self._export_with_cover()
            else:
                # 无封面的简单导出
                success, error_msg = self._export_simple()
            
            self.export_finished.emit(self.output_path, success, error_msg)
            
        except Exception as e:
            self.logger.error(f"导出线程异常: {e}")
            self.export_finished.emit(self.output_path, False, str(e))
    
    def _export_with_cover(self) -> tuple[bool, str]:
        """带封面的导出"""
        try:
            # 保存封面到临时文件
            temp_cover = self._save_cover_to_temp()
            if not temp_cover:
                return False, "保存封面失败"
            
            # 构建FFmpeg命令
            cmd = self._build_ffmpeg_command_with_cover(temp_cover)
            
            # 执行命令
            success = self._execute_ffmpeg_command(cmd)
            
            # 清理临时文件
            try:
                os.remove(temp_cover)
            except Exception:
                pass
            
            return success, "" if success else "FFmpeg执行失败"
            
        except Exception as e:
            return False, str(e)
    
    def _export_simple(self) -> tuple[bool, str]:
        """简单导出（无封面）"""
        try:
            # 构建简单的FFmpeg命令
            cmd = self._build_simple_ffmpeg_command()
            
            # 执行命令
            success = self._execute_ffmpeg_command(cmd)
            
            return success, "" if success else "FFmpeg执行失败"
            
        except Exception as e:
            return False, str(e)
    
    def _save_cover_to_temp(self) -> Optional[str]:
        """保存封面到临时文件"""
        try:
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            if self.cover_image.save(temp_path, 'PNG'):
                return temp_path
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"保存临时封面失败: {e}")
            return None
    
    def _build_ffmpeg_command_with_cover(self, cover_path: str) -> list:
        """构建带封面的FFmpeg命令 - 修复封面时长问题"""
        # 🚀 修复：使用concat方式而不是overlay，确保封面只显示5帧

        # 计算封面时长（5帧以内，最大0.2秒）
        fps = getattr(self, 'fps', 30.0)  # 默认30fps
        max_frames = 5
        frame_duration = min(max_frames / fps, 0.2)

        self.logger.info(f"ExportManager封面时长: {frame_duration}秒 ({frame_duration * fps:.1f}帧)")

        cmd = [
            self.ffmpeg_path, '-y',
            # 输入1：封面图片（循环显示指定时长）
            '-loop', '1', '-t', str(frame_duration), '-i', cover_path,
            # 输入2：原视频（从指定时间开始）
            '-ss', str(self.start_time),
            '-t', str(self.duration) if self.duration > 0 else str(3600),
            '-i', self.video_path,
            # 使用concat滤镜连接封面和视频
            '-filter_complex', '[0:v][1:v]concat=n=2:v=1[outv]',
            '-map', '[outv]',
            '-map', '1:a?',  # 音频轨道可选
            '-c:v', self._get_video_codec(),
            '-c:a', 'aac',
            self.output_path
        ]
        return cmd
    
    def _build_simple_ffmpeg_command(self) -> list:
        """构建简单的FFmpeg命令"""
        cmd = [
            self.ffmpeg_path,
            '-i', self.video_path,
            '-ss', str(self.start_time),
            '-t', str(self.duration) if self.duration > 0 else str(3600),
            '-c:v', self._get_video_codec(),
            '-c:a', 'aac',
            '-y',
            self.output_path
        ]
        return cmd
    
    def _get_video_codec(self) -> str:
        """获取视频编码器"""
        codec_map = {
            'H.264': 'libx264',
            'H.265': 'libx265',
            'VP9': 'libvpx-vp9'
        }
        
        codec = self.export_settings.get('video_codec', 'H.264')
        return codec_map.get(codec, 'libx264')
    
    def _execute_ffmpeg_command(self, cmd: list) -> bool:
        """执行FFmpeg命令"""
        try:
            self.logger.info(f"执行FFmpeg命令: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            # 模拟进度更新
            for i in range(0, 101, 10):
                if self.isInterruptionRequested():
                    process.terminate()
                    return False
                
                self.progress_updated.emit(i)
                self.msleep(100)  # 模拟处理时间
            
            # 等待完成
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                self.progress_updated.emit(100)
                return True
            else:
                self.logger.error(f"FFmpeg错误: {stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"执行FFmpeg命令失败: {e}")
            return False

# ============================================================================
# 便捷函数
# ============================================================================

def create_export_manager() -> ExportManager:
    """创建导出管理器实例"""
    return ExportManager()

# 全局导出管理器实例（可选）
_global_export_manager = None

def get_global_export_manager() -> ExportManager:
    """获取全局导出管理器实例"""
    global _global_export_manager
    if _global_export_manager is None:
        _global_export_manager = ExportManager()
    return _global_export_manager
