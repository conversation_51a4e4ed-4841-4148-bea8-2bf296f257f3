#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理器
负责处理所有图像相关的业务逻辑，保持与原模块完全一致的行为
"""

import os
from typing import Optional
from PyQt6.QtWidgets import QFileDialog, QMessageBox
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt

# 导入常量和工具
from cover_edit.utils.constants import CoverEditConstants
from cover_edit.utils.decorators import error_handler, performance_monitor
from cover_edit.utils.resource_manager import register_pixmap

class ImageHandler:
    """
    图像处理器
    🔒 保证：与原模块的图像处理逻辑完全一致
    """

    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger

    @error_handler(show_dialog=True)
    @performance_monitor(threshold_ms=100.0)
    def import_cover_image(self) -> bool:
        """
        导入封面图像
        🔒 保证：与原模块的import_cover_image方法行为完全一致
        """
        try:
            # 🔒 文件选择对话框 - 与原模块完全一致
            file_path, _ = QFileDialog.getOpenFileName(
                self.parent,
                "选择封面图片",
                getattr(self.parent, 'last_image_path', ''),
                "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif *.tiff);;所有文件 (*)"
            )

            if file_path:
                # 🔒 保存路径 - 与原模块完全一致
                self.parent.last_image_path = os.path.dirname(file_path)

                # 导入全局路径管理器
                try:
                    from global_path_manager import set_path
                    set_path('cover_edit_image', file_path)
                except ImportError:
                    self.logger.warning("全局路径管理器不可用")

                # 🔒 加载图片并注册资源 - 与原模块完全一致
                original_pixmap = QPixmap(file_path)
                if not original_pixmap.isNull():
                    # 🔒 注册原始图片资源
                    try:
                        register_pixmap(original_pixmap, f"原始图片: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.logger.warning(f"注册图片资源失败: {e}")

                    # 🔒 缩放图片到视频分辨率 - 与原模块完全一致
                    scaled_pixmap = self._scale_to_video_resolution(original_pixmap)

                    # 🔒 注册缩放后的图片资源
                    try:
                        register_pixmap(scaled_pixmap, f"缩放图片: {os.path.basename(file_path)}")
                    except Exception as e:
                        self.logger.warning(f"注册缩放图片资源失败: {e}")

                    # 🔒 添加为封面图层 - 与原模块完全一致
                    if hasattr(self.parent, 'video_width') and hasattr(self.parent, 'video_height'):
                        source_info = f"导入图片: {os.path.basename(file_path)} - 缩放至 {self.parent.video_width}x{self.parent.video_height}"
                        self.logger.info(f"导入封面图片: {os.path.basename(file_path)} (原始: {original_pixmap.width()}x{original_pixmap.height()}, 缩放至: {self.parent.video_width}x{self.parent.video_height})")
                    else:
                        source_info = f"导入图片: {os.path.basename(file_path)}"
                        self.logger.info(f"导入封面图片: {os.path.basename(file_path)} (原始: {original_pixmap.width()}x{original_pixmap.height()})")

                    # 🔒 添加图层 - 与原模块完全一致
                    self.add_cover_layer(
                        scaled_pixmap,
                        layer_type="imported",
                        source_info=source_info
                    )

                    QMessageBox.information(self.parent, CoverEditConstants.MSG_SUCCESS, "成功导入封面图片")
                    return True
                else:
                    QMessageBox.warning(self.parent, CoverEditConstants.MSG_WARNING, "无法加载选择的图片文件")
                    return False

            return False

        except Exception as e:
            self.logger.error(f"导入封面失败: {e}")
            QMessageBox.critical(self.parent, CoverEditConstants.MSG_ERROR, f"导入封面失败: {str(e)}")
            return False

    def _scale_to_video_resolution(self, pixmap: QPixmap) -> QPixmap:
        """
        将图片缩放到视频分辨率，保持宽高比
        🔒 保证：与原模块的_scale_to_video_resolution方法行为完全一致
        """
        try:
            if not pixmap or pixmap.isNull():
                return pixmap

            if not hasattr(self.parent, 'video_width') or not hasattr(self.parent, 'video_height'):
                self.logger.warning("没有视频分辨率信息，返回原始图片")
                return pixmap

            # 🔒 缩放到视频分辨率，保持宽高比 - 与原模块完全一致
            scaled_pixmap = pixmap.scaled(
                self.parent.video_width,
                self.parent.video_height,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            return scaled_pixmap

        except Exception as e:
            self.logger.error(f"缩放图片失败: {e}")
            return pixmap

    def add_cover_layer(self, pixmap: QPixmap, layer_type: str = "imported", source_info: str = ""):
        """
        添加封面图层
        🔒 保证：与原模块的add_cover_layer方法行为完全一致
        """
        try:
            if not pixmap or pixmap.isNull():
                return

            # 🔒 确保图层列表存在
            if not hasattr(self.parent, 'cover_layers'):
                self.parent.cover_layers = []
            if not hasattr(self.parent, 'layer_counter'):
                self.parent.layer_counter = 0

            # 🔒 创建图层信息 - 与原模块完全一致
            layer_info = {
                'id': self.parent.layer_counter,
                'pixmap': pixmap,
                'type': layer_type,
                'source': source_info,
                'visible': True,
                'opacity': 1.0
            }

            # 🔒 添加到图层列表 - 与原模块完全一致
            self.parent.cover_layers.append(layer_info)
            self.parent.layer_counter += 1

            self.logger.info(f"添加图层: {source_info} (总图层数: {len(self.parent.cover_layers)})")

            # 🔒 更新合成封面 - 与原模块完全一致
            self.update_composite_cover()

        except Exception as e:
            self.logger.error(f"添加图层失败: {e}")

    def update_composite_cover(self):
        """
        更新合成封面
        🔒 保证：与原模块的update_composite_cover方法行为完全一致
        """
        try:
            if not hasattr(self.parent, 'cover_layers') or not self.parent.cover_layers:
                # 🔒 没有图层时显示默认状态 - 与原模块完全一致
                if hasattr(self.parent, 'cover_preview'):
                    self.parent.cover_preview.setText("请先导入视频\n系统将自动匹配合适的封面")
                return

            # 🔒 创建合成图像 - 与原模块完全一致
            composite_pixmap = self.create_composite_image()

            if composite_pixmap and hasattr(self.parent, 'cover_preview'):
                self.parent.cover_preview.setPixmap(composite_pixmap)

        except Exception as e:
            self.logger.error(f"更新合成封面失败: {e}")

    @performance_monitor(threshold_ms=200.0)
    def create_composite_image(self) -> Optional[QPixmap]:
        """
        创建合成图像
        🔒 保证：与原模块的create_composite_image方法行为完全一致
        """
        try:
            if not hasattr(self.parent, 'cover_layers') or not self.parent.cover_layers:
                return None

            # 🔒 获取基础图像尺寸 - 与原模块完全一致
            base_layer = self.parent.cover_layers[0]
            base_pixmap = base_layer['pixmap']

            if not base_pixmap or base_pixmap.isNull():
                return None

            # 🔒 创建合成画布 - 与原模块完全一致
            composite_pixmap = QPixmap(base_pixmap.size())
            composite_pixmap.fill(Qt.GlobalColor.transparent)

            # 🔒 创建画笔进行合成 - 与原模块完全一致
            from PyQt6.QtGui import QPainter
            painter = QPainter(composite_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)

            # 🔒 绘制所有可见图层 - 与原模块完全一致
            for layer in self.parent.cover_layers:
                if layer.get('visible', True):
                    layer_pixmap = layer['pixmap']
                    opacity = layer.get('opacity', 1.0)

                    if layer_pixmap and not layer_pixmap.isNull():
                        painter.setOpacity(opacity)
                        painter.drawPixmap(0, 0, layer_pixmap)

            painter.end()

            # 🔒 注册合成图像资源
            try:
                register_pixmap(composite_pixmap, f"合成封面 {composite_pixmap.width()}x{composite_pixmap.height()}")
            except Exception as e:
                self.logger.warning(f"注册合成图像资源失败: {e}")

            return composite_pixmap

        except Exception as e:
            self.logger.error(f"创建合成图像失败: {e}")
            return None
