#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程安全管理器 - 解决线程安全和竞态条件问题
"""

import threading
import queue
from typing import Any, Callable, Optional, Dict, List
from PyQt6.QtCore import QObject, QMutex, QMutexLocker, pyqtSignal, QTimer
from PyQt6.QtWidgets import QApplication

from .logger import get_logger
from .decorators import error_handler


class ThreadSafeManager(QObject):
    """线程安全管理器"""
    
    # 信号定义
    ui_update_requested = pyqtSignal(object, object)  # 函数, 参数
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("ThreadSafeManager")
        
        # 互斥锁
        self.mutex = QMutex()
        
        # 批量处理锁
        self.batch_processing_lock = threading.RLock()
        self.batch_processing_state = {
            'is_processing': False,
            'current_file': None,
            'queue_lock': threading.Lock()
        }
        
        # UI更新队列
        self.ui_update_queue = queue.Queue()
        
        # 连接UI更新信号
        self.ui_update_requested.connect(self._execute_ui_update)
        
        # UI更新定时器
        self.ui_timer = QTimer()
        self.ui_timer.timeout.connect(self._process_ui_updates)
        self.ui_timer.start(16)  # 约60FPS
    
    @error_handler(show_dialog=False)
    def safe_ui_update(self, func: Callable, *args, **kwargs):
        """线程安全的UI更新"""
        try:
            if QApplication.instance() is None:
                self.logger.warning("QApplication未初始化，跳过UI更新")
                return
            
            # 检查是否在主线程
            if threading.current_thread() == threading.main_thread():
                # 在主线程，直接执行
                return func(*args, **kwargs)
            else:
                # 在工作线程，通过信号更新
                self.ui_update_requested.emit(func, (args, kwargs))
                
        except Exception as e:
            self.logger.error(f"线程安全UI更新失败: {e}")
    
    def _execute_ui_update(self, func: Callable, params: tuple):
        """执行UI更新"""
        try:
            args, kwargs = params
            func(*args, **kwargs)
            
        except Exception as e:
            self.logger.error(f"执行UI更新失败: {e}")
    
    def _process_ui_updates(self):
        """处理UI更新队列"""
        try:
            # 批量处理UI更新，避免阻塞
            processed = 0
            max_updates_per_frame = 10
            
            while not self.ui_update_queue.empty() and processed < max_updates_per_frame:
                try:
                    func, args, kwargs = self.ui_update_queue.get_nowait()
                    func(*args, **kwargs)
                    processed += 1
                    
                except queue.Empty:
                    break
                except Exception as e:
                    self.logger.error(f"处理UI更新失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"处理UI更新队列失败: {e}")
    
    @error_handler(show_dialog=False)
    def safe_batch_operation(self, operation: Callable, *args, **kwargs) -> Any:
        """线程安全的批量操作"""
        with self.batch_processing_lock:
            try:
                # 检查是否已在处理中
                if self.batch_processing_state['is_processing']:
                    self.logger.warning("批量操作已在进行中")
                    return None
                
                # 设置处理状态
                self.batch_processing_state['is_processing'] = True
                
                try:
                    # 执行操作
                    result = operation(*args, **kwargs)
                    return result
                    
                finally:
                    # 重置状态
                    self.batch_processing_state['is_processing'] = False
                    self.batch_processing_state['current_file'] = None
                    
            except Exception as e:
                self.logger.error(f"批量操作失败: {e}")
                self.batch_processing_state['is_processing'] = False
                return None
    
    @error_handler(show_dialog=False)
    def safe_queue_operation(self, queue_list: List, operation: str, item: Any = None) -> bool:
        """线程安全的队列操作"""
        with self.batch_processing_state['queue_lock']:
            try:
                if operation == 'add' and item is not None:
                    if item not in queue_list:
                        queue_list.append(item)
                        return True
                        
                elif operation == 'remove' and item is not None:
                    if item in queue_list:
                        queue_list.remove(item)
                        return True
                        
                elif operation == 'pop':
                    if queue_list:
                        return queue_list.pop(0)
                        
                elif operation == 'clear':
                    queue_list.clear()
                    return True
                    
                elif operation == 'get_copy':
                    return queue_list.copy()
                    
                return False
                
            except Exception as e:
                self.logger.error(f"队列操作失败: {e}")
                return False
    
    @error_handler(show_dialog=False)
    def safe_state_update(self, state_dict: Dict, updates: Dict) -> bool:
        """线程安全的状态更新"""
        with QMutexLocker(self.mutex):
            try:
                for key, value in updates.items():
                    state_dict[key] = value
                return True
                
            except Exception as e:
                self.logger.error(f"状态更新失败: {e}")
                return False
    
    @error_handler(show_dialog=False)
    def safe_state_read(self, state_dict: Dict, key: str, default: Any = None) -> Any:
        """线程安全的状态读取"""
        with QMutexLocker(self.mutex):
            try:
                return state_dict.get(key, default)
                
            except Exception as e:
                self.logger.error(f"状态读取失败: {e}")
                return default
    
    def is_batch_processing(self) -> bool:
        """检查是否在批量处理中"""
        with self.batch_processing_lock:
            return self.batch_processing_state['is_processing']
    
    def get_current_processing_file(self) -> Optional[str]:
        """获取当前处理的文件"""
        with self.batch_processing_lock:
            return self.batch_processing_state['current_file']
    
    def set_current_processing_file(self, file_path: Optional[str]):
        """设置当前处理的文件"""
        with self.batch_processing_lock:
            self.batch_processing_state['current_file'] = file_path


class SafeBatchProcessor:
    """线程安全的批量处理器"""
    
    def __init__(self, parent_widget, thread_manager: ThreadSafeManager):
        self.parent = parent_widget
        self.thread_manager = thread_manager
        self.logger = get_logger("SafeBatchProcessor")
    
    @error_handler(show_dialog=False)
    def process_next_file(self) -> bool:
        """线程安全的处理下一个文件"""
        def _process_operation():
            try:
                # 检查队列
                if not hasattr(self.parent, 'processing_queue'):
                    return False
                
                # 安全获取下一个文件
                next_file = self.thread_manager.safe_queue_operation(
                    self.parent.processing_queue, 'pop'
                )
                
                if not next_file:
                    return False
                
                # 设置当前处理文件
                self.thread_manager.set_current_processing_file(next_file)
                
                # 更新UI状态
                self.thread_manager.safe_ui_update(
                    self._update_batch_status,
                    f"处理中 - {os.path.basename(next_file)}"
                )
                
                # 处理文件
                success = self._process_single_file(next_file)
                
                # 更新处理结果
                if success:
                    self.thread_manager.safe_queue_operation(
                        getattr(self.parent, 'processed_files', set()), 'add', next_file
                    )
                else:
                    self.thread_manager.safe_queue_operation(
                        getattr(self.parent, 'failed_files', set()), 'add', next_file
                    )
                
                return success
                
            except Exception as e:
                self.logger.error(f"处理文件失败: {e}")
                return False
            
            finally:
                # 清除当前处理文件
                self.thread_manager.set_current_processing_file(None)
        
        # 使用线程安全的批量操作
        return self.thread_manager.safe_batch_operation(_process_operation)
    
    def _process_single_file(self, file_path: str) -> bool:
        """处理单个文件"""
        try:
            # 这里实现具体的文件处理逻辑
            # 委托给父组件的处理方法
            if hasattr(self.parent, '_auto_process_video'):
                return self.parent._auto_process_video(file_path)
            
            return False
            
        except Exception as e:
            self.logger.error(f"处理单个文件失败: {file_path}, {e}")
            return False
    
    def _update_batch_status(self, status: str):
        """更新批量处理状态"""
        try:
            if hasattr(self.parent, 'batch_status_label'):
                self.parent.batch_status_label.setText(f"状态: {status}")
                
        except Exception as e:
            self.logger.error(f"更新批量状态失败: {e}")


# 全局线程安全管理器实例
_thread_safe_manager = ThreadSafeManager()

# 便捷函数
def safe_ui_update(func: Callable, *args, **kwargs):
    """线程安全的UI更新"""
    _thread_safe_manager.safe_ui_update(func, *args, **kwargs)

def safe_batch_operation(operation: Callable, *args, **kwargs) -> Any:
    """线程安全的批量操作"""
    return _thread_safe_manager.safe_batch_operation(operation, *args, **kwargs)

def safe_queue_operation(queue_list: List, operation: str, item: Any = None) -> Any:
    """线程安全的队列操作"""
    return _thread_safe_manager.safe_queue_operation(queue_list, operation, item)

def is_batch_processing() -> bool:
    """检查是否在批量处理中"""
    return _thread_safe_manager.is_batch_processing()

def create_safe_batch_processor(parent_widget) -> SafeBatchProcessor:
    """创建线程安全的批量处理器"""
    return SafeBatchProcessor(parent_widget, _thread_safe_manager)
