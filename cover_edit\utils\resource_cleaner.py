#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源清理器 - 解决资源泄漏和清理不完整问题
"""

import os
import gc
import time
import tempfile
from typing import List, Dict, Any, Optional
from pathlib import Path
from PyQt6.QtCore import QObject, QTimer, pyqtSignal

from .logger import get_logger
from .decorators import error_handler


class ResourceCleaner(QObject):
    """资源清理器 - 统一管理资源清理"""
    
    # 信号定义
    cleanup_completed = pyqtSignal(dict)  # 清理统计
    cleanup_failed = pyqtSignal(str)      # 清理失败
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("ResourceCleaner")

        # 待清理资源
        self.pending_cleanup = {
            'video_captures': [],
            'temp_files': [],
            'pixmaps': [],
            'threads': []
        }

        # 清理统计
        self.cleanup_stats = {
            'total_cleaned': 0,
            'failed_cleanups': 0,
            'last_cleanup_time': None
        }

        # 定时清理器 - 延迟初始化，避免在非主线程中创建
        self.cleanup_timer = None
        self._timer_initialized = False
    
    @error_handler(show_dialog=False)
    def register_video_capture(self, video_cap, description: str = ""):
        """注册视频捕获对象待清理"""
        try:
            if video_cap and hasattr(video_cap, 'release'):
                self.pending_cleanup['video_captures'].append({
                    'object': video_cap,
                    'description': description,
                    'registered_time': time.time()
                })
                self.logger.debug(f"注册视频捕获待清理: {description}")
                
        except Exception as e:
            self.logger.error(f"注册视频捕获失败: {e}")
    
    @error_handler(show_dialog=False)
    def register_temp_file(self, file_path: str, description: str = ""):
        """注册临时文件待清理"""
        try:
            if file_path and os.path.exists(file_path):
                self.pending_cleanup['temp_files'].append({
                    'path': file_path,
                    'description': description,
                    'registered_time': time.time()
                })
                self.logger.debug(f"注册临时文件待清理: {file_path}")
                
        except Exception as e:
            self.logger.error(f"注册临时文件失败: {e}")
    
    @error_handler(show_dialog=False)
    def register_thread(self, thread, description: str = ""):
        """注册线程待清理"""
        try:
            if thread and hasattr(thread, 'terminate'):
                self.pending_cleanup['threads'].append({
                    'object': thread,
                    'description': description,
                    'registered_time': time.time()
                })
                self.logger.debug(f"注册线程待清理: {description}")
                
        except Exception as e:
            self.logger.error(f"注册线程失败: {e}")
    
    @error_handler(show_dialog=False)
    def cleanup_video_captures(self) -> int:
        """清理视频捕获对象"""
        cleaned_count = 0
        failed_items = []
        
        for item in self.pending_cleanup['video_captures'][:]:
            try:
                video_cap = item['object']
                if video_cap and hasattr(video_cap, 'release'):
                    if hasattr(video_cap, 'isOpened') and video_cap.isOpened():
                        video_cap.release()
                    
                    # 从待清理列表中移除
                    self.pending_cleanup['video_captures'].remove(item)
                    cleaned_count += 1
                    
                    self.logger.debug(f"清理视频捕获: {item['description']}")
                    
            except Exception as e:
                self.logger.error(f"清理视频捕获失败: {e}")
                failed_items.append(item)
        
        # 移除失败的项目（避免重复尝试）
        for item in failed_items:
            if item in self.pending_cleanup['video_captures']:
                self.pending_cleanup['video_captures'].remove(item)
        
        return cleaned_count
    
    @error_handler(show_dialog=False)
    def cleanup_temp_files(self, max_retries: int = 3) -> int:
        """清理临时文件"""
        cleaned_count = 0
        failed_items = []
        
        for item in self.pending_cleanup['temp_files'][:]:
            try:
                file_path = item['path']
                
                # 重试机制
                for attempt in range(max_retries):
                    try:
                        if os.path.exists(file_path):
                            # 尝试删除文件
                            os.remove(file_path)
                            
                            # 验证删除成功
                            if not os.path.exists(file_path):
                                self.pending_cleanup['temp_files'].remove(item)
                                cleaned_count += 1
                                self.logger.debug(f"清理临时文件: {file_path}")
                                break
                        else:
                            # 文件已不存在，从列表中移除
                            self.pending_cleanup['temp_files'].remove(item)
                            cleaned_count += 1
                            break
                            
                    except PermissionError:
                        if attempt < max_retries - 1:
                            time.sleep(0.1)  # 等待100ms后重试
                            continue
                        else:
                            self.logger.warning(f"权限不足，无法删除临时文件: {file_path}")
                            failed_items.append(item)
                            break
                    except Exception as e:
                        if attempt < max_retries - 1:
                            time.sleep(0.1)
                            continue
                        else:
                            self.logger.error(f"删除临时文件失败: {file_path}, {e}")
                            failed_items.append(item)
                            break
                            
            except Exception as e:
                self.logger.error(f"处理临时文件清理失败: {e}")
                failed_items.append(item)
        
        return cleaned_count
    
    @error_handler(show_dialog=False)
    def cleanup_threads(self, timeout_ms: int = 5000) -> int:
        """清理线程"""
        cleaned_count = 0
        failed_items = []
        
        for item in self.pending_cleanup['threads'][:]:
            try:
                thread = item['object']
                if thread and hasattr(thread, 'isRunning'):
                    if thread.isRunning():
                        # 尝试正常终止
                        if hasattr(thread, 'quit'):
                            thread.quit()
                            if thread.wait(timeout_ms):
                                self.logger.debug(f"线程正常退出: {item['description']}")
                            else:
                                # 强制终止
                                thread.terminate()
                                thread.wait(1000)
                                self.logger.warning(f"线程强制终止: {item['description']}")
                    
                    self.pending_cleanup['threads'].remove(item)
                    cleaned_count += 1
                    
            except Exception as e:
                self.logger.error(f"清理线程失败: {e}")
                failed_items.append(item)
        
        return cleaned_count
    
    @error_handler(show_dialog=False)
    def cleanup_old_temp_files(self, max_age_hours: int = 24) -> int:
        """清理系统临时目录中的旧文件"""
        cleaned_count = 0
        
        try:
            temp_dir = Path(tempfile.gettempdir())
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            # 查找相关的临时文件
            patterns = ['cover_edit_*', 'video_export_*', 'temp_cover_*']
            
            for pattern in patterns:
                for temp_file in temp_dir.glob(pattern):
                    try:
                        # 检查文件年龄
                        file_age = current_time - temp_file.stat().st_mtime
                        
                        if file_age > max_age_seconds:
                            temp_file.unlink()
                            cleaned_count += 1
                            self.logger.debug(f"清理旧临时文件: {temp_file}")
                            
                    except Exception as e:
                        self.logger.debug(f"清理旧临时文件失败: {temp_file}, {e}")
                        
        except Exception as e:
            self.logger.error(f"清理旧临时文件失败: {e}")
        
        return cleaned_count
    
    @error_handler(show_dialog=False)
    def force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            # 多次调用gc.collect()以确保彻底清理
            for _ in range(3):
                collected = gc.collect()
                if collected > 0:
                    self.logger.debug(f"垃圾回收清理了 {collected} 个对象")
                    
        except Exception as e:
            self.logger.error(f"强制垃圾回收失败: {e}")
    
    def _init_timer_if_needed(self):
        """在主线程中初始化定时器"""
        try:
            if not self._timer_initialized:
                from PyQt6.QtWidgets import QApplication
                if QApplication.instance() is not None:
                    self.cleanup_timer = QTimer()
                    self.cleanup_timer.timeout.connect(self._periodic_cleanup)
                    self.cleanup_timer.start(30000)  # 每30秒清理一次
                    self._timer_initialized = True
                    self.logger.debug("定时清理器初始化完成")
        except Exception as e:
            self.logger.debug(f"定时器初始化失败: {e}")

    def _periodic_cleanup(self):
        """定期清理"""
        try:
            stats = self.cleanup_all()

            if stats['total_cleaned'] > 0:
                self.logger.info(f"定期清理完成: {stats}")

        except Exception as e:
            self.logger.error(f"定期清理失败: {e}")
    
    @error_handler(show_dialog=False)
    def cleanup_all(self) -> Dict[str, int]:
        """清理所有资源"""
        # 尝试初始化定时器（如果在主线程中）
        self._init_timer_if_needed()

        stats = {
            'video_captures': 0,
            'temp_files': 0,
            'threads': 0,
            'old_temp_files': 0,
            'total_cleaned': 0
        }

        try:
            # 清理各类资源
            stats['video_captures'] = self.cleanup_video_captures()
            stats['temp_files'] = self.cleanup_temp_files()
            stats['threads'] = self.cleanup_threads()
            stats['old_temp_files'] = self.cleanup_old_temp_files()

            # 强制垃圾回收
            self.force_garbage_collection()

            # 计算总数
            stats['total_cleaned'] = sum(stats.values()) - stats['total_cleaned']

            # 更新统计
            self.cleanup_stats['total_cleaned'] += stats['total_cleaned']
            self.cleanup_stats['last_cleanup_time'] = time.time()

            # 发送完成信号（如果有接收者）
            try:
                self.cleanup_completed.emit(stats)
            except:
                pass  # 忽略信号发送失败

            return stats

        except Exception as e:
            self.logger.error(f"清理所有资源失败: {e}")
            try:
                self.cleanup_failed.emit(str(e))
            except:
                pass  # 忽略信号发送失败
            return stats
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """获取清理统计"""
        return self.cleanup_stats.copy()
    
    def get_pending_count(self) -> Dict[str, int]:
        """获取待清理资源数量"""
        return {
            'video_captures': len(self.pending_cleanup['video_captures']),
            'temp_files': len(self.pending_cleanup['temp_files']),
            'threads': len(self.pending_cleanup['threads'])
        }


# 全局资源清理器实例
_resource_cleaner = ResourceCleaner()

# 便捷函数
def register_video_capture_for_cleanup(video_cap, description: str = ""):
    """注册视频捕获对象待清理"""
    _resource_cleaner.register_video_capture(video_cap, description)

def register_temp_file_for_cleanup(file_path: str, description: str = ""):
    """注册临时文件待清理"""
    _resource_cleaner.register_temp_file(file_path, description)

def register_thread_for_cleanup(thread, description: str = ""):
    """注册线程待清理"""
    _resource_cleaner.register_thread(thread, description)

def cleanup_all_resources() -> Dict[str, int]:
    """清理所有资源"""
    return _resource_cleaner.cleanup_all()

def get_cleanup_stats() -> Dict[str, Any]:
    """获取清理统计"""
    return _resource_cleaner.get_cleanup_stats()
