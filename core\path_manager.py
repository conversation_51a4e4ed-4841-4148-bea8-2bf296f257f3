#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全新的项目路径记忆系统
完全重写，解决"牛皮癣"问题

设计原则：
1. 简单直接：只记忆用户最后的选择
2. 不干扰用户：绝不自动弹出、设置、或覆盖用户选择
3. 统一管理：所有模块使用同一套API
4. 可控制：用户可以随时清除记忆
"""

import os
import json
from pathlib import Path
from typing import Dict


class ProjectPathMemory:
    """项目路径记忆系统"""

    _instance = None

    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """初始化路径记忆"""
        if not hasattr(self, '_initialized'):
            self.memory_file = self._get_memory_file()
            self.memory = {}
            self._load_memory()
            self._initialized = True

    def _get_memory_file(self) -> str:
        """获取记忆文件路径"""
        try:
            config_dir = Path(__file__).parent.parent / 'config'
            config_dir.mkdir(exist_ok=True)
            return str(config_dir / 'project_path_memory.json')
        except Exception as e:
            print(f"⚠️ 获取记忆文件路径失败: {e}")
            return 'project_path_memory.json'

    def _load_memory(self):
        """加载路径记忆"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, 'r', encoding='utf-8') as f:
                    self.memory = json.load(f)
                print(f"📁 项目路径记忆加载: {len(self.memory)} 个路径")
            else:
                print(f"📁 首次使用，无路径记忆")
                self.memory = {}
        except Exception as e:
            print(f"⚠️ 加载路径记忆失败: {e}")
            self.memory = {}

    def _save_memory(self):
        """保存路径记忆"""
        try:
            with open(self.memory_file, 'w', encoding='utf-8') as f:
                json.dump(self.memory, f, indent=2, ensure_ascii=False)
            print(f"💾 项目路径记忆已保存")
        except Exception as e:
            print(f"⚠️ 保存路径记忆失败: {e}")

    def remember_path(self, key: str, path: str):
        """记忆用户选择的路径"""
        if not path:
            return

        # 如果是文件路径，提取目录
        if os.path.isfile(path):
            path = os.path.dirname(path)

        # 只记忆真实存在的路径
        if os.path.exists(path):
            old_path = self.memory.get(key, '')
            self.memory[key] = path
            self._save_memory()

            if old_path != path:
                print(f"📁 记忆路径 {key}: {path}")
        else:
            print(f"⚠️ 路径不存在，不记忆: {path}")

    def get_remembered_path(self, key: str) -> str:
        """获取记忆的路径"""
        path = self.memory.get(key, '')

        # 验证路径是否还存在
        if path and os.path.exists(path):
            return path
        elif path:
            # 路径不存在了，清除记忆
            print(f"📁 清除无效记忆 {key}: {path}")
            del self.memory[key]
            self._save_memory()

        return ''

    def forget_path(self, key: str):
        """忘记指定路径"""
        if key in self.memory:
            del self.memory[key]
            self._save_memory()
            print(f"🗑️ 已忘记路径 {key}")

    def forget_all(self):
        """忘记所有路径"""
        self.memory.clear()
        self._save_memory()
        print(f"🗑️ 已清除所有路径记忆")

    def get_all_memory(self) -> Dict[str, str]:
        """获取所有记忆的路径"""
        return self.memory.copy()

    # 兼容旧API的方法
    def get_path(self, key: str) -> str:
        """兼容旧API：获取路径"""
        return self.get_remembered_path(key)

    def set_path(self, key: str, path: str):
        """兼容旧API：设置路径"""
        self.remember_path(key, path)

    def get_all_paths(self) -> Dict[str, str]:
        """兼容旧API：获取所有路径"""
        return self.get_all_memory()

    def reset_paths(self):
        """兼容旧API：重置路径"""
        self.forget_all()


# 全局路径记忆实例
path_memory = ProjectPathMemory()


# 新的API
def remember_user_path(key: str, path: str):
    """记忆用户选择的路径"""
    path_memory.remember_path(key, path)


def get_user_last_path(key: str) -> str:
    """获取用户上次选择的路径"""
    return path_memory.get_remembered_path(key)


def forget_user_path(key: str):
    """忘记用户的路径选择"""
    path_memory.forget_path(key)


def clear_all_path_memory():
    """清除所有路径记忆"""
    path_memory.forget_all()


def get_all_remembered_paths() -> Dict[str, str]:
    """获取所有记忆的路径"""
    return path_memory.get_all_memory()


# 兼容旧API的函数
def get_path(key: str) -> str:
    """兼容旧API：获取路径"""
    return path_memory.get_path(key)


def set_path(key: str, path: str):
    """兼容旧API：设置路径"""
    path_memory.set_path(key, path)


def save_path(key: str, path: str):
    """兼容旧API：保存路径"""
    path_memory.set_path(key, path)


def get_all_paths() -> Dict[str, str]:
    """兼容旧API：获取所有路径"""
    return path_memory.get_all_paths()


def reset_paths():
    """兼容旧API：重置路径"""
    path_memory.reset_paths()


# 紧急清理函数
def emergency_clear_all_memory():
    """紧急清除所有路径记忆"""
    try:
        # 清除新的记忆文件
        config_dir = Path(__file__).parent.parent / 'config'

        # 清除项目路径记忆文件
        project_memory_file = config_dir / 'project_path_memory.json'
        if project_memory_file.exists():
            project_memory_file.unlink()
            print("🗑️ 已删除项目路径记忆文件")

        # 清除旧的配置文件
        old_config_file = config_dir / 'global_paths.json'
        if old_config_file.exists():
            old_config_file.unlink()
            print("🗑️ 已删除旧配置文件")

        # 重新初始化
        global path_memory
        path_memory = ProjectPathMemory()

        print("🎉 所有路径记忆已彻底清除！")
        return True

    except Exception as e:
        print(f"❌ 清除路径记忆失败: {e}")
        return False


if __name__ == "__main__":
    print("🧪 测试新项目路径记忆系统...")

    # 测试基本功能
    print("\n1. 测试记忆功能:")
    remember_user_path('test_key', 'C:/Users')
    remembered = get_user_last_path('test_key')
    print(f"记忆测试: {'✅' if remembered == 'C:/Users' else '❌'}")

    # 测试清除功能
    print("\n2. 测试清除功能:")
    forget_user_path('test_key')
    forgotten = get_user_last_path('test_key')
    print(f"清除测试: {'✅' if forgotten == '' else '❌'}")

    print("\n🎉 新项目路径记忆系统测试完成！")
