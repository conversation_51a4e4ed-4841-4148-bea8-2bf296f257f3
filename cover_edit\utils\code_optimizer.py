#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码优化工具
用于清理冗余代码、优化导入、移除无用注释等
"""

import re
import ast
from pathlib import Path
from typing import List, Set, Dict, Tuple
from cover_edit.utils.logger import get_logger

class CodeOptimizer:
    """代码优化器"""
    
    def __init__(self):
        self.logger = get_logger("CodeOptimizer")
        
        # 冗余注释模式
        self.redundant_comment_patterns = [
            r'# 🔒.*与原模块完全一致.*',
            r'# 🔒.*保证：.*',
            r'# 🔒.*确保.*',
            r'# 从原模块迁移而来',
            r'# 与原模块完全一致',
        ]
        
        # 需要保留的重要注释模式
        self.important_comment_patterns = [
            r'# TODO:',
            r'# FIXME:',
            r'# NOTE:',
            r'# WARNING:',
            r'# 🚀.*',  # 保留优化标记
            r'# ========.*',  # 保留分割线
        ]
    
    def analyze_imports(self, file_path: Path) -> Dict[str, List[str]]:
        """分析文件的导入情况"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            imports = {
                'used': [],
                'unused': [],
                'duplicated': []
            }
            
            # 提取所有导入
            imported_names = set()
            import_nodes = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        name = alias.asname or alias.name
                        if name in imported_names:
                            imports['duplicated'].append(name)
                        imported_names.add(name)
                        import_nodes.append((name, node))
                        
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        name = alias.asname or alias.name
                        if name in imported_names:
                            imports['duplicated'].append(name)
                        imported_names.add(name)
                        import_nodes.append((name, node))
            
            # 检查使用情况
            used_names = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Name):
                    used_names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # 处理 module.function 形式
                    if isinstance(node.value, ast.Name):
                        used_names.add(node.value.id)
            
            # 分类导入
            for name, _ in import_nodes:
                if name in used_names:
                    imports['used'].append(name)
                else:
                    imports['unused'].append(name)
            
            return imports
            
        except Exception as e:
            self.logger.error(f"分析导入失败 {file_path}: {e}")
            return {'used': [], 'unused': [], 'duplicated': []}
    
    def remove_redundant_comments(self, content: str) -> str:
        """移除冗余注释"""
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # 检查是否是重要注释
            is_important = any(re.search(pattern, line) for pattern in self.important_comment_patterns)
            
            if is_important:
                cleaned_lines.append(line)
                continue
            
            # 检查是否是冗余注释
            is_redundant = any(re.search(pattern, line) for pattern in self.redundant_comment_patterns)
            
            if not is_redundant:
                cleaned_lines.append(line)
            else:
                self.logger.debug(f"移除冗余注释: {line.strip()}")
        
        return '\n'.join(cleaned_lines)
    
    def optimize_imports(self, content: str) -> str:
        """优化导入语句"""
        lines = content.split('\n')
        
        # 分组导入
        stdlib_imports = []
        third_party_imports = []
        local_imports = []
        
        import_section_start = -1
        import_section_end = -1
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            if stripped.startswith('import ') or stripped.startswith('from '):
                if import_section_start == -1:
                    import_section_start = i
                import_section_end = i
                
                # 分类导入
                if 'from .' in stripped or 'from ..' in stripped:
                    local_imports.append(line)
                elif any(lib in stripped for lib in ['PyQt6', 'cv2', 'numpy', 'PIL']):
                    third_party_imports.append(line)
                else:
                    stdlib_imports.append(line)
        
        if import_section_start != -1:
            # 重新组织导入
            new_imports = []
            
            if stdlib_imports:
                new_imports.extend(sorted(set(stdlib_imports)))
                new_imports.append('')
            
            if third_party_imports:
                new_imports.extend(sorted(set(third_party_imports)))
                new_imports.append('')
            
            if local_imports:
                new_imports.extend(sorted(set(local_imports)))
                new_imports.append('')
            
            # 替换原有导入
            new_lines = (lines[:import_section_start] + 
                        new_imports + 
                        lines[import_section_end + 1:])
            
            return '\n'.join(new_lines)
        
        return content
    
    def remove_empty_lines(self, content: str, max_consecutive: int = 2) -> str:
        """移除多余的空行"""
        lines = content.split('\n')
        cleaned_lines = []
        empty_count = 0
        
        for line in lines:
            if line.strip() == '':
                empty_count += 1
                if empty_count <= max_consecutive:
                    cleaned_lines.append(line)
            else:
                empty_count = 0
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def optimize_file(self, file_path: Path, backup: bool = True) -> bool:
        """优化单个文件"""
        try:
            if not file_path.exists() or file_path.suffix != '.py':
                return False
            
            # 备份原文件
            if backup:
                backup_path = file_path.with_suffix('.py.bak')
                backup_path.write_text(file_path.read_text(encoding='utf-8'), encoding='utf-8')
            
            # 读取文件内容
            content = file_path.read_text(encoding='utf-8')
            
            # 应用优化
            content = self.remove_redundant_comments(content)
            content = self.optimize_imports(content)
            content = self.remove_empty_lines(content)
            
            # 写回文件
            file_path.write_text(content, encoding='utf-8')
            
            self.logger.info(f"优化完成: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"优化文件失败 {file_path}: {e}")
            return False
    
    def optimize_directory(self, directory: Path, recursive: bool = True) -> Dict[str, int]:
        """优化目录中的所有Python文件"""
        stats = {
            'processed': 0,
            'optimized': 0,
            'failed': 0
        }
        
        pattern = '**/*.py' if recursive else '*.py'
        
        for py_file in directory.glob(pattern):
            stats['processed'] += 1
            
            if self.optimize_file(py_file):
                stats['optimized'] += 1
            else:
                stats['failed'] += 1
        
        self.logger.info(f"优化统计: {stats}")
        return stats

# 便捷函数
def optimize_cover_edit_module():
    """优化整个cover_edit模块"""
    from cover_edit.utils.path_manager import get_project_root
    
    optimizer = CodeOptimizer()
    module_dir = get_project_root() / 'modules' / 'cover_edit'
    
    return optimizer.optimize_directory(module_dir, recursive=True)

if __name__ == "__main__":
    # 运行优化
    stats = optimize_cover_edit_module()
    print(f"优化完成: {stats}")
