#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 主模块 - 极简版本
真正的模块化架构，主模块只负责协调，具体实现全部委托给子模块
"""

import os
from PyQt6.QtWidgets import QMainWindow, QWidget
from PyQt6.QtCore import pyqtSignal, Qt
# 🏗️ 新设计：不再需要QColor导入

from .utils.logger import get_logger
from .utils.constants import CoverEditConstants


class CoverEditOptimizedModule(QWidget):
    """
    🚀 极简化的Cover Edit主模块
    
    职责：
    1. 初始化各个子模块
    2. 协调子模块之间的通信
    3. 提供统一的对外接口
    4. 管理模块生命周期
    
    原则：
    - 主模块不包含具体业务逻辑
    - 所有实现都委托给专门的子模块
    - 保持与原模块完全相同的对外接口
    """
    
    # 对外信号 - 保持与原模块一致
    video_loaded = pyqtSignal(str, int, int, float, int, float)
    export_finished = pyqtSignal(bool, str)
    batch_status_changed = pyqtSignal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 基础设置
        self.logger = get_logger("CoverEditModule")
        self.logger.info("初始化封面编辑模块")

        # 🚀 初始化基础属性（保持兼容性）
        self._init_basic_attributes()

        # 初始化子模块
        self._init_managers()
        self._init_ui()
        self._setup_media_player()  # 🚀 添加媒体播放器设置
        self._connect_signals()

        # 🚀 修复：UI构建完成后立即连接控件信号
        self._connect_ui_signals_immediately()

        # 🚀 设置快捷键
        self._setup_shortcuts()

        self.logger.info("封面编辑模块初始化完成")

    def _connect_ui_signals_immediately(self):
        """立即连接UI信号 - 确保UI组件创建后立即连接"""
        try:
            # 连接帧滑块信号
            if hasattr(self, 'frame_slider') and self.frame_slider:
                self.frame_slider.valueChanged.connect(self.on_frame_slider_changed)

            # 连接播放滑块信号
            if hasattr(self, 'playback_slider') and self.playback_slider:
                self.playback_slider.sliderPressed.connect(self.on_playback_slider_pressed)
                self.playback_slider.sliderReleased.connect(self.on_playback_slider_released)
                self.playback_slider.valueChanged.connect(self.on_playback_slider_changed)

            self.logger.info("UI信号连接完成")

        except Exception as e:
            self.logger.error(f"连接UI信号失败: {e}")

    def _setup_shortcuts(self):
        """设置快捷键 - 与原版完全一致"""
        try:
            from PyQt6.QtGui import QShortcut, QKeySequence

            # 设置焦点策略，使窗口能接收键盘事件
            self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

            # Ctrl+1: 切换批量处理日志显示
            self.log_toggle_shortcut = QShortcut(QKeySequence("Ctrl+1"), self)
            self.log_toggle_shortcut.activated.connect(self.toggle_batch_log_display)

            self.logger.info("📋 快捷键设置完成: Ctrl+1 切换批量处理日志")

        except Exception as e:
            self.logger.error(f"设置快捷键失败: {e}")

    def _init_basic_attributes(self):
        """初始化基础属性，保持与原模块的兼容性"""
        # 🚀 修复：使用内部属性，避免与property冲突
        # 视频相关属性
        self._video_path = ""
        self.video_cap = None
        self._current_frame = 0
        self._total_frames = 0
        self._fps = 30.0
        self._video_duration = 0.0
        self.video_width = CoverEditConstants.DEFAULT_VIDEO_SIZE[0]
        self.video_height = CoverEditConstants.DEFAULT_VIDEO_SIZE[1]
        self.video_aspect_ratio = CoverEditConstants.DEFAULT_ASPECT_RATIO

        # 界面相关属性
        self.video_preview = None
        self.cover_preview = None
        self.export_btn = None
        self.load_video_btn = None
        self.import_cover_btn = None

        # 🚀 添加媒体播放器相关属性
        self.media_player = None
        self.audio_output = None
        self.video_widget = None
        self.preview_stack = None
        self.control_stack = None
        self.play_pause_btn = None
        self.play_mode_btn = None
        self.snapshot_mode_btn = None

        # 批量处理相关属性
        self.batch_mode_checkbox = None
        self.batch_enable_checkbox = None
        self.batch_status_label = None
        self.batch_log_display = None
        self.batch_settings_widget = None
        self.watch_folder_btn = None
        self.output_folder_btn = None
        self.start_batch_btn = None
        self.watch_folder = ""
        self.output_folder = ""
        self.batch_mode_enabled = False
        self.batch_settings_shown = False  # 记录设置是否已显示
        self.batch_log_visible = False  # 记录日志是否显示

        # 文字编辑模式状态 - 与原模块完全一致
        # 🏗️ 新设计：不再需要文字编辑模式状态

        # 🏗️ 新设计：文字属性已移至独立窗口，不再在主模块中定义
        self.current_processing = False
        self.processing_queue = []
        self.processed_files = set()
        self.failed_files = set()
        self.retry_count = {}
        self.scan_in_progress = False

        # 导出设置相关属性
        self.export_settings_window = None
        self.export_settings = {}

        # 裁剪相关属性
        self.cropped_start = 0.0
        self.cropped_end = 0.0
        self.cropped_start_frames = CoverEditConstants.DEFAULT_CROP_START
        self.cropped_end_frames = CoverEditConstants.DEFAULT_CROP_END

        # 视频相关属性 - 与原版一致
        self.video_cap = None
        self.video_path = ""
        self.fps = 30.0
        self.total_frames = 0
        self.video_width = 0
        self.video_height = 0
        self.duration = 0.0
        self.current_frame = 0

        # 其他属性
        self._cover_image = None
        self.text_layers = []
        self.cover_layers = []
        self.export_thread = None
        self.batch_thread = None
        self.file_watcher = None
        self.export_settings = None
    
    def _init_managers(self):
        """初始化各个管理器"""
        try:
            # 🚀 基于现有框架初始化管理器
            # UI构建器 - 负责界面构建
            from .ui.main_widget_builder import MainWidgetBuilder
            from .ui.preview_panel_builder import PreviewPanelBuilder
            from .ui.control_panel_builder import ControlPanelBuilder
            from .ui.right_panel_builder import RightPanelBuilder

            self.main_widget_builder = MainWidgetBuilder(self)
            self.preview_panel_builder = PreviewPanelBuilder(self)
            self.control_panel_builder = ControlPanelBuilder(self)
            self.right_panel_builder = RightPanelBuilder(self)

            # 核心处理器 - 负责业务逻辑
            from .core.video_handler import VideoHandler
            from .core.image_handler import ImageHandler
            from .core.text_handler import TextHandler
            from .core.layer_manager import LayerManager
            from .core.batch_processor import SimpleBatchProcessor
            from .core.export_coordinator import ExportCoordinator
            # 🏗️ 新设计：不再使用text_editor_builder，改为独立窗口

            self.video_handler = VideoHandler(self)
            self.image_handler = ImageHandler(self)
            self.text_handler = TextHandler(self)
            self.layer_manager = LayerManager(self)
            self.batch_processor = SimpleBatchProcessor(self)
            self.export_coordinator = ExportCoordinator(self)
            # 🏗️ 新设计：不再使用text_editor_builder，改为独立窗口

            # 工作线程管理器
            from .workers.export_worker import ExportWorkerManager
            from .workers.batch_worker import BatchWorkerManager

            self.export_worker_manager = ExportWorkerManager(self)
            self.batch_worker_manager = BatchWorkerManager(self)

            self.logger.info("所有管理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化管理器失败: {e}")
            # 创建占位符管理器，避免程序崩溃
            self._create_fallback_managers()
    
    def _create_fallback_managers(self):
        """创建占位符管理器"""
        from PyQt6.QtCore import QObject

        # 创建简单的占位符类
        class FallbackHandler(QObject):
            def __init__(self, name, parent=None):
                super().__init__(parent)
                self.name = name
                self.logger = parent.logger if parent else None

            def __getattr__(self, attr_name):
                def dummy_method(*args, **kwargs):
                    if self.logger:
                        self.logger.warning(f"调用了占位符方法: {self.name}.{attr_name}")
                    return None
                return dummy_method

        # 创建占位符处理器
        self.video_handler = FallbackHandler("VideoHandler", self)
        self.image_handler = FallbackHandler("ImageHandler", self)
        self.text_handler = FallbackHandler("TextHandler", self)
        self.layer_manager = FallbackHandler("LayerManager", self)
        self.batch_processor = FallbackHandler("BatchProcessor", self)
        self.export_coordinator = FallbackHandler("ExportCoordinator", self)

        self.logger.warning("使用占位符管理器，部分功能可能不可用")
    
    def _init_ui(self):
        """初始化UI - 与原版cover_edit._setup_ui()完全一致"""
        try:
            from PyQt6.QtWidgets import QHBoxLayout

            self.logger.info("🔧 开始初始化UI...")

            # 🔒 与原版完全一致：创建主布局
            main_layout = QHBoxLayout(self)
            main_layout.setContentsMargins(10, 10, 10, 10)

            # 🔒 与原版完全一致：创建分割器
            self.splitter = self._create_splitter()

            # 🔒 与原版完全一致：创建左右面板
            left_panel = self._create_left_panel()
            right_panel = self._create_right_panel()

            # 🔒 与原版完全一致：添加面板到分割器
            self.splitter.addWidget(left_panel)
            self.splitter.addWidget(right_panel)

            # 🔒 与原版完全一致：设置1:1分配
            self.splitter.setSizes([500, 500])  # 左右栏1:1分配

            main_layout.addWidget(self.splitter)

            # 设置窗口属性
            self.setWindowTitle("封面编辑")
            self.setMinimumSize(1200, 800)

            # 🔒 与原版完全一致：应用基础样式
            self.setStyleSheet(self._get_base_style())

            # 连接信号
            self._connect_signals()

            # 默认切换到截图模式
            self.switch_to_snapshot_mode()

            self.logger.info("✅ UI初始化完成")

        except Exception as e:
            self.logger.error(f"初始化UI失败: {e}")
            import traceback
            self.logger.error(f"UI初始化详细错误: {traceback.format_exc()}")

    def _create_splitter(self):
        """创建分割器 - 与原版完全一致"""
        from PyQt6.QtWidgets import QSplitter
        from PyQt6.QtCore import Qt

        splitter = QSplitter(Qt.Orientation.Horizontal)
        # 使用原版的分割器样式
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #303238;
                width: 2px;
            }
        """)
        # 设置拉伸因子为相等
        splitter.setStretchFactor(0, 1)  # 左侧面板
        splitter.setStretchFactor(1, 1)  # 右侧面板
        return splitter

    def _create_left_panel(self):
        """创建左侧面板 - 与原版完全一致"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout

        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)

        # 创建预览区域
        self._create_preview_area(left_layout)

        # 创建控制按钮
        self._create_control_buttons(left_layout)

        # 创建控制面板
        self._create_control_panels(left_layout)

        # 导出进度条（放在左侧栏最下方，默认隐藏）
        from PyQt6.QtWidgets import QProgressBar
        self.export_progress = QProgressBar()
        self.export_progress.setVisible(False)  # 默认隐藏
        self.export_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 4px;
                text-align: center;
                background-color: #2b2b2b;
                color: white;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        left_layout.addWidget(self.export_progress)

        return left_panel

    def _create_right_panel(self):
        """创建右侧面板 - 与原版完全一致"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout

        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)

        # 封面预览
        self._create_cover_preview(right_layout)

        # 🏗️ 新设计：文字属性组改为独立窗口，不再在主界面显示

        # 导出按钮（包含功能按钮）
        self._create_export_button(right_layout)

        # 批量处理组
        batch_group = self._create_batch_group()
        right_layout.addWidget(batch_group)

        return right_panel

    def _get_base_style(self):
        """获取基础样式 - 与原版完全一致"""
        return """
            QWidget {
                background-color: #181A1F;
                color: #D0D0D0;
                font-family: 'Microsoft YaHei UI', 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #303238;
                border-radius: 6px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLabel {
                color: #ABB2BF;
            }
            QLineEdit {
                background-color: #2C2E34;
                border: 1px solid #5C6370;
                border-radius: 4px;
                padding: 5px;
                color: #D0D0D0;
            }
            QLineEdit:focus {
                border-color: #61AFEF;
            }
            QPushButton {
                background-color: #3A3F4B;
                color: #ABB2BF;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4A4F5B;
            }
        """

    def _create_preview_area(self, layout):
        """创建预览区域 - 与原版完全一致"""
        from PyQt6.QtWidgets import QStackedWidget, QSizePolicy
        from PyQt6.QtCore import Qt

        # 视频预览区域 - 使用堆叠窗口
        self.preview_stack = QStackedWidget()
        self.preview_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 截图模式预览
        from .ui.preview_label import PreviewLabel
        self.snapshot_preview = PreviewLabel()
        self.snapshot_preview.setMinimumSize(500, 300)
        self.snapshot_preview.setStyleSheet(self._get_preview_style())
        self.snapshot_preview.setText("请加载视频文件")
        self.snapshot_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 播放模式预览
        self.playback_widget = self._create_playback_widget()

        # 添加到堆叠窗口
        self.preview_stack.addWidget(self.snapshot_preview)
        self.preview_stack.addWidget(self.playback_widget)

        layout.addWidget(self.preview_stack)

    def _create_playback_widget(self):
        """创建播放模式组件 - 与原版完全一致"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout
        from PyQt6.QtMultimediaWidgets import QVideoWidget
        from PyQt6.QtCore import Qt

        playback_widget = QWidget()
        playback_layout = QVBoxLayout(playback_widget)
        playback_layout.setContentsMargins(0, 0, 0, 0)
        playback_layout.setSpacing(0)

        # 视频播放器容器
        self.video_container = QWidget()
        video_container_layout = QVBoxLayout(self.video_container)
        video_container_layout.setContentsMargins(0, 0, 0, 0)
        video_container_layout.setSpacing(0)

        # 视频播放器
        self.video_widget = QVideoWidget()
        self.video_widget.setAspectRatioMode(Qt.AspectRatioMode.KeepAspectRatio)
        self.video_widget.setMinimumSize(500, 300)
        self.video_widget.setStyleSheet("""
            QVideoWidget {
                background-color: #181A1F;
                border: 2px dashed #5C6370;
                border-radius: 6px;
            }
        """)

        video_container_layout.addWidget(self.video_widget)
        playback_layout.addWidget(self.video_container)

        return playback_widget

    def _get_preview_style(self):
        """获取预览样式 - 与原版完全一致"""
        return """
            QLabel {
                background-color: #181A1F;
                border: 2px dashed #5C6370;
                border-radius: 6px;
                color: #5C6370;
                font-size: 16px;
                font-style: italic;
                padding: 0px;
            }
        """

    def _create_control_buttons(self, layout):
        """创建控制按钮 - 与原版完全一致"""
        from PyQt6.QtWidgets import QHBoxLayout, QButtonGroup

        # 模式按钮行
        mode_button_layout = QHBoxLayout()
        mode_button_layout.setContentsMargins(0, 5, 0, 5)
        mode_button_layout.setSpacing(10)

        # 创建按钮组确保单选行为
        self.mode_button_group = QButtonGroup(self)

        # 创建各种按钮
        self._create_mode_buttons(mode_button_layout)
        self._create_action_buttons(mode_button_layout)

        layout.addLayout(mode_button_layout)

    def _create_mode_buttons(self, layout):
        """创建模式切换按钮 - 与原版完全一致"""
        from PyQt6.QtWidgets import QPushButton

        button_style = self._get_button_style()

        # 播放模式按钮
        self.play_mode_btn = QPushButton("播放模式")
        self.play_mode_btn.setCheckable(True)
        self.play_mode_btn.setFixedWidth(120)
        self.play_mode_btn.setStyleSheet(button_style['mode'])
        self.play_mode_btn.clicked.connect(self.switch_to_play_mode)

        # 截图模式按钮
        self.snapshot_mode_btn = QPushButton("截图模式")
        self.snapshot_mode_btn.setCheckable(True)
        self.snapshot_mode_btn.setChecked(True)  # 默认选中
        self.snapshot_mode_btn.setFixedWidth(120)
        self.snapshot_mode_btn.setStyleSheet(button_style['mode'])
        self.snapshot_mode_btn.clicked.connect(self.switch_to_snapshot_mode)

        # 添加到按钮组
        self.mode_button_group.addButton(self.play_mode_btn, 0)
        self.mode_button_group.addButton(self.snapshot_mode_btn, 1)

        layout.addWidget(self.play_mode_btn)
        layout.addWidget(self.snapshot_mode_btn)

    def _create_action_buttons(self, layout):
        """创建操作按钮 - 与原版完全一致"""
        from PyQt6.QtWidgets import QPushButton

        button_style = self._get_button_style()

        # 截取帧按钮
        self.capture_frame_btn = QPushButton("截取当前帧")
        self.capture_frame_btn.setFixedWidth(120)
        self.capture_frame_btn.setStyleSheet(button_style['action'])
        self.capture_frame_btn.clicked.connect(self.capture_current_frame)
        self.capture_frame_btn.setEnabled(True)  # 默认启用



        # 导入封面按钮
        self.import_cover_btn = QPushButton("导入封面")
        self.import_cover_btn.setFixedWidth(120)
        self.import_cover_btn.setStyleSheet(button_style['import'])
        self.import_cover_btn.clicked.connect(self.import_cover_image)

        layout.addWidget(self.capture_frame_btn)
        layout.addWidget(self.import_cover_btn)

    def _get_button_style(self):
        """获取按钮样式 - 与原版完全一致"""
        return {
            'mode': """
                QPushButton {
                    background-color: #3A3F4B;
                    color: #ABB2BF;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:checked {
                    background-color: #61AFEF;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #4A4F5B;
                }
            """,
            'action': """
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """,
            'smart': """
                QPushButton {
                    background-color: #9C27B0;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7B1FA2;
                }
            """,
            'import': """
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 0;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #43A047;
                }
            """
        }

    def _create_control_panels(self, layout):
        """创建控制面板 - 与原版完全一致"""
        from PyQt6.QtWidgets import QStackedWidget, QSizePolicy

        # 控制面板堆叠区域
        self.control_stack = QStackedWidget()
        self.control_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.control_stack.setMaximumHeight(60)

        # 创建播放和截图控制面板
        play_control_panel = self._create_play_control_panel()
        snapshot_control_panel = self._create_snapshot_control_panel()

        self.control_stack.addWidget(play_control_panel)
        self.control_stack.addWidget(snapshot_control_panel)
        self.control_stack.setCurrentIndex(1)  # 默认显示截图控制面板

        layout.addWidget(self.control_stack)

        # 添加视频信息组
        info_group = self._create_video_info_group()
        layout.addWidget(info_group)

    def _create_play_control_panel(self):
        """创建播放控制面板 - 与原版完全一致"""
        from PyQt6.QtWidgets import QWidget, QHBoxLayout, QPushButton, QSlider, QLabel
        from PyQt6.QtCore import Qt

        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 播放/暂停按钮
        self.play_pause_btn = QPushButton("播放")
        self.play_pause_btn.setFixedSize(80, 40)
        self.play_pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45A049;
            }
        """)
        self.play_pause_btn.clicked.connect(self.toggle_playback)

        # 进度条
        self.playback_slider = QSlider(Qt.Orientation.Horizontal)
        self.playback_slider.setMinimum(0)
        self.playback_slider.setMaximum(100)
        self.playback_slider.setValue(0)
        self.playback_slider.setStyleSheet(self._get_slider_style("#4CAF50"))

        # 时间标签
        self.time_label = QLabel("00:00 / 00:00")
        self.time_label.setMinimumWidth(100)
        self.time_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.play_pause_btn)
        layout.addWidget(self.playback_slider, 1)
        layout.addWidget(self.time_label)

        return panel

    def _create_snapshot_control_panel(self):
        """创建截图控制面板 - 与原版完全一致"""
        from PyQt6.QtWidgets import QWidget, QHBoxLayout, QSlider, QLabel
        from PyQt6.QtCore import Qt

        panel = QWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 帧滑块
        self.frame_slider = QSlider(Qt.Orientation.Horizontal)
        self.frame_slider.setMinimum(0)
        self.frame_slider.setMaximum(100)
        self.frame_slider.setValue(0)
        self.frame_slider.setStyleSheet(self._get_slider_style("#FF9800"))
        self.frame_slider.valueChanged.connect(self.on_frame_slider_changed)

        # 帧数显示
        self.frame_label = QLabel("帧: 0/0")
        self.frame_label.setMinimumWidth(100)
        self.frame_label.setStyleSheet("color: #ABB2BF; font-size: 12px;")

        layout.addWidget(self.frame_slider, 1)
        layout.addWidget(self.frame_label)

        return panel

    def _get_slider_style(self, color):
        """获取滑块样式 - 与原版完全一致"""
        return f"""
            QSlider::groove:horizontal {{
                border: 1px solid #999999;
                height: 8px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4);
                margin: 2px 0;
                border-radius: 4px;
            }}
            QSlider::handle:horizontal {{
                background: {color};
                border: 1px solid #5c5c5c;
                width: 18px;
                margin: -2px 0;
                border-radius: 9px;
            }}
            QSlider::handle:horizontal:hover {{
                background: {color};
                opacity: 0.8;
            }}
        """

    def _create_video_info_group(self):
        """创建视频信息组 - 与原版完全一致"""
        from PyQt6.QtWidgets import QGroupBox, QGridLayout, QLabel, QLineEdit, QPushButton

        info_group = QGroupBox("视频信息与裁剪设置")
        info_layout = QGridLayout(info_group)
        info_layout.setContentsMargins(10, 15, 10, 10)
        info_layout.setSpacing(8)

        # 视频路径
        info_layout.addWidget(QLabel("视频文件:"), 0, 0)
        self.video_path_edit = QLineEdit()
        self.video_path_edit.setReadOnly(True)
        self.video_path_edit.setPlaceholderText("请选择视频文件...")
        info_layout.addWidget(self.video_path_edit, 0, 1, 1, 2)

        self.browse_video_btn = QPushButton("浏览")
        self.browse_video_btn.setFixedWidth(80)
        self.browse_video_btn.clicked.connect(self.load_video)
        info_layout.addWidget(self.browse_video_btn, 0, 3)

        # 视频信息
        info_layout.addWidget(QLabel("分辨率:"), 1, 0)
        self.resolution_label = QLabel("未知")
        info_layout.addWidget(self.resolution_label, 1, 1)

        info_layout.addWidget(QLabel("时长:"), 1, 2)
        self.duration_label = QLabel("未知")
        info_layout.addWidget(self.duration_label, 1, 3)

        # 裁剪设置
        info_layout.addWidget(QLabel("裁剪开头:"), 2, 0)
        self.crop_start_input = QLineEdit("0")
        self.crop_start_input.setFixedWidth(60)
        self.crop_start_input.textChanged.connect(self.on_crop_settings_changed)
        info_layout.addWidget(self.crop_start_input, 2, 1)

        info_layout.addWidget(QLabel("裁剪结尾:"), 2, 2)
        self.crop_end_input = QLineEdit("0")
        self.crop_end_input.setFixedWidth(60)
        self.crop_end_input.textChanged.connect(self.on_crop_settings_changed)
        info_layout.addWidget(self.crop_end_input, 2, 3)

        return info_group

    # === 模式切换方法 ===
    def switch_to_play_mode(self):
        """切换到播放模式"""
        if hasattr(self, 'preview_stack'):
            self.preview_stack.setCurrentIndex(1)
        if hasattr(self, 'control_stack'):
            self.control_stack.setCurrentIndex(0)

    # switch_to_snapshot_mode 方法已在下面定义，删除重复定义

    def on_frame_slider_changed(self, value):
        """帧滑块变化处理 - 与原版完全一致"""
        try:
            # 显示指定帧
            self.show_frame(value)

            # 更新帧数显示
            if hasattr(self, 'frame_label') and self.frame_label:
                total_frames = getattr(self, 'total_frames', 0)
                self.frame_label.setText(f"帧: {value}/{total_frames}")

        except Exception as e:
            self.logger.error(f"帧滑块变化处理失败: {e}")

    def on_playback_slider_pressed(self):
        """播放滑块按下处理"""
        try:
            # 暂停播放以便拖拽
            if hasattr(self, 'media_player') and self.media_player:
                from PyQt6.QtMultimedia import QMediaPlayer
                if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                    self.media_player.pause()
                    self._was_playing = True
                else:
                    self._was_playing = False
        except Exception as e:
            self.logger.error(f"播放滑块按下处理失败: {e}")

    def on_playback_slider_released(self):
        """播放滑块释放处理"""
        try:
            # 如果之前在播放，恢复播放
            if hasattr(self, '_was_playing') and self._was_playing:
                if hasattr(self, 'media_player') and self.media_player:
                    self.media_player.play()
        except Exception as e:
            self.logger.error(f"播放滑块释放处理失败: {e}")

    def on_playback_slider_changed(self, value):
        """播放滑块位置变化处理"""
        try:
            if hasattr(self, 'media_player') and self.media_player:
                # 设置播放位置
                self.media_player.setPosition(value)
        except Exception as e:
            self.logger.error(f"播放滑块位置变化处理失败: {e}")

    def on_crop_settings_changed(self):
        """裁剪设置变化处理 - 与原版完全一致"""
        try:
            # 获取裁剪设置
            start_text = self.crop_start_input.text().strip()
            end_text = self.crop_end_input.text().strip()

            # 更新裁剪帧数 - 空值时默认为0（不裁剪）
            old_start_frames = getattr(self, 'cropped_start_frames', CoverEditConstants.DEFAULT_CROP_START)
            old_end_frames = getattr(self, 'cropped_end_frames', CoverEditConstants.DEFAULT_CROP_END)

            # 处理输入值：空值或非数字时默认为0（不裁剪）
            if start_text == '' or not start_text.isdigit():
                new_start_frames = 0
            else:
                new_start_frames = int(start_text)

            if end_text == '' or not end_text.isdigit():
                new_end_frames = 0
            else:
                new_end_frames = int(end_text)

            # 检查是否有实际变化
            start_changed = old_start_frames != new_start_frames
            end_changed = old_end_frames != new_end_frames

            # 更新值
            self.cropped_start_frames = new_start_frames
            self.cropped_end_frames = new_end_frames

            self.logger.info(f"裁剪设置更新: 开头 {old_start_frames}->{new_start_frames}, 结尾 {old_end_frames}->{new_end_frames}")

            # 更新时长显示
            self.update_duration_label()

            # 如果在播放模式，更新媒体播放器
            if hasattr(self, 'play_mode_btn') and self.play_mode_btn.isChecked():
                # 更新播放范围
                self.update_media_player_range()

                # 如果开头裁剪帧数发生变化，跳转到新的开始位置
                if start_changed:
                    start_time_ms = int((self.cropped_start_frames / self.fps) * 1000) if self.fps > 0 else 0
                    if hasattr(self, 'media_player') and self.media_player:
                        self.media_player.setPosition(start_time_ms)

                    # 同时更新播放滑块位置
                    if hasattr(self, 'playback_slider'):
                        self.playback_slider.setValue(start_time_ms)

                    # 特殊处理：当裁剪值为0时，确保跳转到真正的第一帧
                    if self.cropped_start_frames == 0:
                        self.logger.info(f"跳转到原始视频第一帧: 0ms (第0帧)")
                    else:
                        self.logger.info(f"跳转到新的开始位置: {start_time_ms}ms (第{self.cropped_start_frames}帧)")

                # 如果结尾裁剪帧数发生变化，也提供反馈
                if end_changed:
                    self.logger.info(f"更新结尾裁剪: 第{self.total_frames - self.cropped_end_frames}帧结束")

            # 如果在截图模式，更新帧滑块的有效范围显示
            elif hasattr(self, 'snapshot_mode_btn') and self.snapshot_mode_btn.isChecked():
                # 更新帧滑块的样式或提示，显示裁剪范围
                if start_changed or end_changed:
                    self._update_frame_slider_range_indicator()

                    # 如果开头裁剪变为0，跳转到第一帧
                    if start_changed and self.cropped_start_frames == 0:
                        if hasattr(self, 'frame_slider'):
                            self.frame_slider.setValue(0)
                        self.current_frame = 0
                        self.show_frame(0)
                        self.logger.info("截图模式跳转到原始视频第一帧: 第0帧")

        except Exception as e:
            self.logger.error(f"裁剪设置变化处理失败: {e}")

    def update_duration_label(self):
        """更新时长标签 - 与原版完全一致"""
        try:
            if not hasattr(self, 'duration_label') or not hasattr(self, 'total_frames') or not hasattr(self, 'fps'):
                return

            # 计算裁剪后的时长
            start_frame = getattr(self, 'cropped_start_frames', 0)
            end_frame = self.total_frames - getattr(self, 'cropped_end_frames', 0)

            if end_frame > start_frame:
                cropped_duration = (end_frame - start_frame) / self.fps if self.fps > 0 else 0
                self.duration_label.setText(f"裁剪后时长: {self.format_time(cropped_duration)}")
            else:
                self.duration_label.setText("裁剪后时长: 无效")

        except Exception as e:
            self.logger.error(f"更新时长标签失败: {e}")

    def _update_frame_slider_range_indicator(self):
        """更新帧滑块的裁剪范围指示器 - 与原版完全一致"""
        try:
            if not hasattr(self, 'frame_slider') or not self.frame_slider:
                return

            # 计算有效范围
            start_frame = max(0, getattr(self, 'cropped_start_frames', 0))
            end_frame = max(start_frame + 1, self.total_frames - getattr(self, 'cropped_end_frames', 0))

            # 确保范围有效
            if end_frame <= start_frame:
                end_frame = start_frame + 1

            # 更新滑块的工具提示
            if getattr(self, 'cropped_start_frames', 0) == 0 and getattr(self, 'cropped_end_frames', 0) == 0:
                tooltip = "无裁剪 - 完整视频"
            else:
                tooltip = f"有效范围: 第{start_frame}帧 - 第{end_frame-1}帧"
            self.frame_slider.setToolTip(tooltip)

            # 如果当前帧在裁剪范围外，跳转到范围内
            current_frame = self.frame_slider.value()
            jumped = False

            if current_frame < start_frame:
                self.frame_slider.setValue(start_frame)
                self.show_frame(start_frame)
                self.logger.info(f"截图模式跳转: 当前帧({current_frame}) -> 开始帧({start_frame})")
                jumped = True
            elif current_frame >= end_frame:
                target_frame = max(start_frame, end_frame - 1)
                self.frame_slider.setValue(target_frame)
                self.show_frame(target_frame)
                self.logger.info(f"截图模式跳转: 当前帧({current_frame}) -> 结束帧({target_frame})")
                jumped = True

            if not jumped:
                self.logger.info(f"截图模式范围更新: 第{start_frame}-{end_frame-1}帧，当前第{current_frame}帧")

        except Exception as e:
            self.logger.error(f"更新帧滑块范围指示器失败: {e}")

    def update_media_player_range(self):
        """更新媒体播放器的播放范围 - 与原版完全一致"""
        try:
            if not hasattr(self, 'media_player') or not self.media_player:
                return

            # 计算播放范围（毫秒）
            start_time_ms = int((getattr(self, 'cropped_start_frames', 0) / self.fps) * 1000)
            end_time_ms = int(((self.total_frames - getattr(self, 'cropped_end_frames', 0)) / self.fps) * 1000)

            # 更新位置滑块的范围
            if hasattr(self, 'playback_slider'):
                self.playback_slider.setRange(start_time_ms, end_time_ms)

            # 根据当前帧计算对应的播放位置
            current_time_ms = int((self.current_frame / self.fps) * 1000)

            # 如果当前位置在有效范围内，设置到当前位置；否则设置到开始位置
            if start_time_ms <= current_time_ms <= end_time_ms:
                self.media_player.setPosition(current_time_ms)
                if hasattr(self, 'playback_slider'):
                    self.playback_slider.setValue(current_time_ms)
            else:
                self.media_player.setPosition(start_time_ms)
                if hasattr(self, 'playback_slider'):
                    self.playback_slider.setValue(start_time_ms)

        except Exception as e:
            self.logger.error(f"更新媒体播放器范围失败: {e}")

    def format_time(self, seconds):
        """格式化时间显示 - 与原版完全一致"""
        try:
            if seconds < 0:
                return "00:00"

            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            secs = int(seconds % 60)

            if hours > 0:
                return f"{hours:02d}:{minutes:02d}:{secs:02d}"
            else:
                return f"{minutes:02d}:{secs:02d}"

        except Exception:
            return "00:00"

    def _create_cover_preview(self, layout):
        """创建封面预览 - 自动缩放容器版本"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy
        from PyQt6.QtCore import Qt

        # 创建封面预览容器
        cover_container = QWidget()
        cover_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        cover_layout = QVBoxLayout(cover_container)
        cover_layout.setContentsMargins(5, 5, 5, 5)
        cover_layout.setSpacing(0)

        # 创建封面预览标签
        from .ui.preview_label import PreviewLabel
        self.cover_preview = PreviewLabel()
        self.cover_preview.setStyleSheet(self._get_preview_style())
        self.cover_preview.setText("请先导入视频\n系统将自动匹配合适的封面")
        self.cover_preview.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # 设置最小尺寸，确保预览质量
        self.cover_preview.setMinimumSize(300, 400)
        self.cover_preview.setScaledContents(False)  # 保持宽高比

        # 🏗️ 新设计：不再创建文字图层，改为独立窗口模式
        print("✅ 封面预览已创建，使用自动缩放容器模式")

        # 初始化尺寸记录
        self._last_preview_size = None

        # 添加预览到容器
        cover_layout.addWidget(self.cover_preview)

        # 保存容器引用以便后续使用
        self.cover_container = cover_container

        # 设置容器大小变化事件处理
        self._setup_container_resize_handler()

        # 添加容器到主布局，使用拉伸因子使预览区占据更多空间
        layout.addWidget(cover_container, 1)

    def _setup_container_resize_handler(self):
        """设置容器大小变化事件处理"""
        try:
            # 保存原始的resizeEvent方法
            if not hasattr(self, '_original_preview_resize_event'):
                self._original_preview_resize_event = self.cover_preview.resizeEvent

            # 替换resizeEvent方法
            def resize_event_with_text_scaling(event):
                # 调用原始事件处理
                if hasattr(self, '_original_preview_resize_event'):
                    self._original_preview_resize_event(event)

                # 如果有文字输入框，调整其位置
                if hasattr(self, 'text_input') and self.text_input and hasattr(self, 'text_input_active') and self.text_input_active:
                    self._adjust_text_input_for_resize()

            # 应用新的事件处理
            self.cover_preview.resizeEvent = resize_event_with_text_scaling

            print("✅ 容器大小变化事件处理已设置")

        except Exception as e:
            print(f"❌ 设置容器大小变化事件失败: {e}")
            self.logger.error(f"设置容器大小变化事件失败: {e}")

    def _adjust_text_input_for_resize(self):
        """调整文字输入框以适应容器大小变化"""
        try:
            if not hasattr(self, 'text_input') or not self.text_input:
                return

            # 如果有保存的相对位置，使用相对位置重新计算
            if hasattr(self, 'text_input_relative_x') and hasattr(self, 'text_input_relative_y'):
                # 获取当前实际图像显示区域
                image_rect = self._get_actual_image_rect()

                if image_rect and image_rect.width() > 0 and image_rect.height() > 0:
                    # 基于实际图像区域计算新的绝对位置
                    new_x = int(image_rect.x() + self.text_input_relative_x * image_rect.width())
                    new_y = int(image_rect.y() + self.text_input_relative_y * image_rect.height())

                    # 获取当前输入框大小
                    current_geometry = self.text_input.geometry()

                    # 确保不超出预览区域边界
                    max_x = self.cover_preview.width() - current_geometry.width()
                    max_y = self.cover_preview.height() - current_geometry.height()

                    new_x = max(0, min(new_x, max_x))
                    new_y = max(0, min(new_y, max_y))

                    # 更新输入框位置
                    self.text_input.setGeometry(
                        new_x, new_y,
                        current_geometry.width(),
                        current_geometry.height()
                    )

                    print(f"📐 文字输入框位置已按图像比例调整: ({new_x}, {new_y})")
                    print(f"📐 图像区域: {image_rect.x()}, {image_rect.y()}, {image_rect.width()}x{image_rect.height()}")
                else:
                    # 备用方案：使用整个预览区域
                    new_x = int(self.text_input_relative_x * self.cover_preview.width())
                    new_y = int(self.text_input_relative_y * self.cover_preview.height())

                    current_geometry = self.text_input.geometry()
                    max_x = self.cover_preview.width() - current_geometry.width()
                    max_y = self.cover_preview.height() - current_geometry.height()

                    new_x = max(0, min(new_x, max_x))
                    new_y = max(0, min(new_y, max_y))

                    self.text_input.setGeometry(
                        new_x, new_y,
                        current_geometry.width(),
                        current_geometry.height()
                    )

                    print(f"📐 文字输入框位置已按预览区域比例调整: ({new_x}, {new_y})")
            else:
                # 如果没有相对位置，使用原来的边界检查方法
                current_geometry = self.text_input.geometry()

                max_x = self.cover_preview.width() - current_geometry.width()
                max_y = self.cover_preview.height() - current_geometry.height()

                new_x = min(current_geometry.x(), max(0, max_x))
                new_y = min(current_geometry.y(), max(0, max_y))

                if new_x != current_geometry.x() or new_y != current_geometry.y():
                    self.text_input.setGeometry(
                        new_x, new_y,
                        current_geometry.width(),
                        current_geometry.height()
                    )
                    print(f"📐 文字输入框位置已边界调整: ({new_x}, {new_y})")

        except Exception as e:
            print(f"❌ 调整文字输入框位置失败: {e}")
            self.logger.error(f"调整文字输入框位置失败: {e}")

    # 🏗️ 新设计：文字属性组已改为独立窗口，此方法已移除

    def _create_export_button(self, layout):
        """创建导出按钮区域 - 与原版完全一致"""
        from PyQt6.QtWidgets import QHBoxLayout, QPushButton

        # 创建导出区域的水平布局
        export_layout = QHBoxLayout()
        export_layout.setSpacing(10)

        # 统一的按钮样式
        button_style = """
            QPushButton {
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 100px;
            }
            QPushButton:disabled {
                background-color: #5C6370;
                color: #ABB2BF;
            }
        """

        # 文字编辑按钮
        self.text_edit_btn_export = QPushButton("文字编辑")
        self.text_edit_btn_export.setFixedHeight(50)
        self.text_edit_btn_export.setStyleSheet(button_style + """
            QPushButton {
                background-color: #61AFEF;
            }
            QPushButton:hover {
                background-color: #528BFF;
            }
        """)
        self.text_edit_btn_export.clicked.connect(self.toggle_text_editing)
        self.text_edit_btn_export.setEnabled(False)
        print("🔘 文字编辑按钮已创建，默认禁用状态（需要先导入视频或封面）")

        # 导出设置按钮
        self.export_settings_btn_export = QPushButton("导出设置")
        self.export_settings_btn_export.setFixedHeight(50)
        self.export_settings_btn_export.setStyleSheet(button_style + """
            QPushButton {
                background-color: #98C379;
            }
            QPushButton:hover {
                background-color: #7FB069;
            }
        """)
        self.export_settings_btn_export.clicked.connect(self.open_export_settings)
        self.export_settings_btn_export.setEnabled(True)  # 默认启用

        # 导出视频按钮
        self.export_btn = QPushButton("导出视频")
        self.export_btn.setFixedHeight(50)
        self.export_btn.setStyleSheet(button_style + """
            QPushButton {
                background-color: #E06C75;
            }
            QPushButton:hover {
                background-color: #D55A65;
            }
        """)
        self.export_btn.clicked.connect(self.export_video)
        self.export_btn.setEnabled(False)

        # 添加按钮到布局（等宽分布）
        export_layout.addWidget(self.text_edit_btn_export, 1)
        export_layout.addWidget(self.export_settings_btn_export, 1)
        export_layout.addWidget(self.export_btn, 1)

        layout.addLayout(export_layout)

    # === 导出功能方法 ===
    def toggle_text_editing(self):
        """打开文字属性设置窗口 - 🏗️ 新的模块化设计"""
        try:
            print("🔘 文字编辑按钮被点击 - 打开属性窗口")

            # 导入文字属性窗口
            from .ui.text_properties_window import TextPropertiesWindow

            # 创建并显示文字属性窗口
            if not hasattr(self, 'text_properties_window') or not self.text_properties_window:
                self.text_properties_window = TextPropertiesWindow(self)

                # 连接窗口信号
                self.text_properties_window.properties_changed.connect(self.on_text_properties_changed)
                self.text_properties_window.window_closed.connect(self.on_text_properties_window_closed)

            # 显示窗口
            self.text_properties_window.show()
            self.text_properties_window.raise_()
            self.text_properties_window.activateWindow()

            # 激活文字输入功能
            self._activate_text_input_mode()

            print("✅ 文字属性窗口已打开，文字输入模式已激活")

        except Exception as e:
            print(f"❌ 打开文字属性窗口失败: {e}")
            self.logger.error(f"打开文字属性窗口失败: {e}")

    def _activate_text_input_mode(self):
        """激活文字输入模式 - 等待鼠标点击"""
        try:
            print("🔤 激活文字输入模式 - 等待鼠标点击")

            # 确保预览图存在
            if not hasattr(self, 'cover_preview') or not self.cover_preview:
                print("❌ 预览图不存在，无法激活文字输入")
                return

            # 设置文字输入状态
            self.text_input_active = True

            # 为预览图添加鼠标点击事件处理
            self._setup_preview_click_handler()

            print("✅ 文字输入模式已激活，请点击预览图选择文字位置")

        except Exception as e:
            print(f"❌ 激活文字输入模式失败: {e}")
            self.logger.error(f"激活文字输入模式失败: {e}")

    def _setup_preview_click_handler(self):
        """设置预览图点击事件处理"""
        try:
            # 保存原始的鼠标点击事件
            if not hasattr(self, '_original_mouse_press_event'):
                self._original_mouse_press_event = self.cover_preview.mousePressEvent

            # 替换鼠标点击事件
            def mouse_press_event_with_text_input(event):
                if hasattr(self, 'text_input_active') and self.text_input_active:
                    # 🚀 修复：检查是否已有包含文字的输入框
                    if hasattr(self, 'text_input') and self.text_input:
                        current_text = self.text_input.toPlainText()
                        if current_text.strip():
                            print(f"🛡️ 已有包含文字的输入框，忽略点击事件: '{current_text}'")
                            return  # 不创建新的输入框

                    # 在点击位置创建文本输入框
                    self._create_text_input_at_position(event.pos())
                else:
                    # 调用原始事件处理
                    if hasattr(self, '_original_mouse_press_event'):
                        self._original_mouse_press_event(event)

            # 应用新的事件处理
            self.cover_preview.mousePressEvent = mouse_press_event_with_text_input

            print("✅ 预览图点击事件处理已设置")

        except Exception as e:
            print(f"❌ 设置预览图点击事件失败: {e}")
            self.logger.error(f"设置预览图点击事件失败: {e}")

    def _create_text_input_at_position(self, position):
        """在指定位置创建文本输入框"""
        try:
            print(f"🎯 在位置 ({position.x()}, {position.y()}) 创建文本输入框")

            # 创建文本输入框
            self._create_text_input(position)

            print("✅ 文字输入框已在指定位置创建")

        except Exception as e:
            print(f"❌ 在指定位置创建文字输入框失败: {e}")
            self.logger.error(f"在指定位置创建文字输入框失败: {e}")

    def _apply_text_input_protection(self, text_input):
        """为文字输入框应用保护机制，防止有文字时被隐藏"""
        try:
            from PyQt6.QtWidgets import QTextEdit

            # 🚀 修复：重写焦点事件，防止失焦时文字消失
            def custom_focus_out_event(event):
                print("📝 输入框失去焦点，检查文字内容")
                try:
                    # 保存当前文本
                    current_text = text_input.toPlainText()
                    print(f"📝 当前文字内容: '{current_text}'")

                    if current_text.strip():  # 如果有文字内容，保持显示
                        print(f"📝 有文字内容，完全阻止焦点事件处理: {current_text}")
                        # 🚀 修复：完全阻止焦点事件处理，不调用任何父类方法
                        # 直接返回，不做任何处理
                        event.ignore()  # 忽略事件
                        return
                    else:
                        # 如果没有文字，才允许正常处理焦点事件
                        print("📝 没有文字内容，允许正常处理焦点事件")
                        QTextEdit.focusOutEvent(text_input, event)
                except Exception as e:
                    print(f"❌ 焦点事件处理失败: {e}")

            # 重写焦点事件
            text_input.focusOutEvent = custom_focus_out_event

            # 🚀 新增：重写hide方法，防止文本输入框被意外隐藏
            original_hide = text_input.hide
            def protected_hide():
                current_text = text_input.toPlainText()
                if current_text.strip():
                    print(f"🛡️ 阻止隐藏文本输入框，因为有文字内容: {current_text}")
                    return  # 不执行隐藏
                else:
                    print("📝 允许隐藏文本输入框，因为没有文字内容")
                    original_hide()

            text_input.hide = protected_hide

            # 🚀 新增：重写setVisible方法，防止被设置为不可见
            original_set_visible = text_input.setVisible
            def protected_set_visible(visible):
                if not visible:  # 如果试图设置为不可见
                    current_text = text_input.toPlainText()
                    if current_text.strip():
                        print(f"🛡️ 阻止设置文本输入框不可见，因为有文字内容: {current_text}")
                        return  # 不执行设置不可见
                    else:
                        print("📝 允许设置文本输入框不可见，因为没有文字内容")
                        original_set_visible(visible)
                else:
                    # 允许设置为可见
                    original_set_visible(visible)

            text_input.setVisible = protected_set_visible

            # 🚀 新增：重写setParent方法，防止被移除
            original_set_parent = text_input.setParent
            def protected_set_parent(parent):
                if parent is None:  # 如果试图移除父控件
                    current_text = text_input.toPlainText()
                    if current_text.strip():
                        print(f"🛡️ 阻止移除文本输入框父控件，因为有文字内容: {current_text}")
                        return  # 不执行移除
                    else:
                        print("📝 允许移除文本输入框父控件，因为没有文字内容")
                        original_set_parent(parent)
                else:
                    # 允许设置新的父控件
                    original_set_parent(parent)

            text_input.setParent = protected_set_parent

            print("✅ 文字输入框保护机制已应用")

        except Exception as e:
            print(f"❌ 应用文字输入框保护机制失败: {e}")
            self.logger.error(f"应用文字输入框保护机制失败: {e}")

    def _setup_text_input_mouse_events(self):
        """设置文本输入框的鼠标事件处理"""
        try:
            # 保存原始的鼠标事件
            if not hasattr(self, '_original_text_input_mouse_press'):
                self._original_text_input_mouse_press = self.text_input.mousePressEvent
            if not hasattr(self, '_original_text_input_mouse_move'):
                self._original_text_input_mouse_move = self.text_input.mouseMoveEvent
            if not hasattr(self, '_original_text_input_mouse_release'):
                self._original_text_input_mouse_release = self.text_input.mouseReleaseEvent

            # 替换鼠标事件处理
            self.text_input.mousePressEvent = self._text_input_mouse_press_event
            self.text_input.mouseMoveEvent = self._text_input_mouse_move_event
            self.text_input.mouseReleaseEvent = self._text_input_mouse_release_event

            print("✅ 文本输入框鼠标事件处理已设置")

        except Exception as e:
            print(f"❌ 设置文本输入框鼠标事件失败: {e}")
            self.logger.error(f"设置文本输入框鼠标事件失败: {e}")

    def _text_input_mouse_press_event(self, event):
        """文本输入框鼠标按下事件"""
        try:
            from PyQt6.QtCore import Qt

            if event.button() == Qt.MouseButton.RightButton:
                # 🚀 简化：右键直接开始拖动
                self.text_input_dragging = True
                self.drag_start_pos = event.pos()
                print("🖱️ 开始拖动文字输入框")
            else:
                # 🚀 修复：左键正常处理（文本编辑）
                print(f"🖱️ 左键点击，位置: {event.pos()}")
                # 确保获得焦点
                self.text_input.setFocus()
                # 直接调用父类方法确保正常的文本编辑功能
                from PyQt6.QtWidgets import QTextEdit
                QTextEdit.mousePressEvent(self.text_input, event)
                print("✅ 左键事件已传递给QTextEdit")

        except Exception as e:
            print(f"❌ 文本输入框鼠标按下事件处理失败: {e}")
            self.logger.error(f"文本输入框鼠标按下事件处理失败: {e}")

    def _text_input_mouse_move_event(self, event):
        """文本输入框鼠标移动事件"""
        try:
            from PyQt6.QtCore import Qt

            # 🚀 简化：直接检查拖动状态
            if self.text_input_dragging and self.drag_start_pos:
                # 计算移动距离
                delta = event.pos() - self.drag_start_pos

                # 获取当前位置
                current_geometry = self.text_input.geometry()
                new_x = current_geometry.x() + delta.x()
                new_y = current_geometry.y() + delta.y()

                # 边界检查
                max_x = self.cover_preview.width() - current_geometry.width()
                max_y = self.cover_preview.height() - current_geometry.height()

                new_x = max(0, min(new_x, max_x))
                new_y = max(0, min(new_y, max_y))

                # 更新位置
                self.text_input.setGeometry(
                    new_x, new_y,
                    current_geometry.width(),
                    current_geometry.height()
                )

                # 更新相对位置（基于实际图像显示区域）
                image_rect = self._get_actual_image_rect()
                if image_rect and image_rect.width() > 0 and image_rect.height() > 0:
                    relative_x_in_image = (new_x - image_rect.x()) / image_rect.width()
                    relative_y_in_image = (new_y - image_rect.y()) / image_rect.height()
                    self.text_input_relative_x = relative_x_in_image
                    self.text_input_relative_y = relative_y_in_image
                else:
                    # 备用方案
                    self.text_input_relative_x = new_x / self.cover_preview.width()
                    self.text_input_relative_y = new_y / self.cover_preview.height()

                print(f"🖱️ 拖动文字输入框到: ({new_x}, {new_y})")
            else:
                # 🚀 修复：非拖动状态，正常处理（文本选择等）
                from PyQt6.QtWidgets import QTextEdit
                QTextEdit.mouseMoveEvent(self.text_input, event)

        except Exception as e:
            print(f"❌ 文本输入框鼠标移动事件处理失败: {e}")
            self.logger.error(f"文本输入框鼠标移动事件处理失败: {e}")

    def _text_input_mouse_release_event(self, event):
        """文本输入框鼠标释放事件"""
        try:
            from PyQt6.QtCore import Qt

            if event.button() == Qt.MouseButton.RightButton and self.text_input_dragging:
                # 🚀 简化：结束拖动
                self.text_input_dragging = False
                self.drag_start_pos = None
                print("🖱️ 结束拖动文字输入框")
            else:
                # 🚀 修复：正常处理（左键释放等）
                from PyQt6.QtWidgets import QTextEdit
                QTextEdit.mouseReleaseEvent(self.text_input, event)
                print(f"✅ 鼠标释放事件已传递给QTextEdit: {event.button()}")

        except Exception as e:
            print(f"❌ 文本输入框鼠标释放事件处理失败: {e}")
            self.logger.error(f"文本输入框鼠标释放事件处理失败: {e}")

    def _get_actual_image_rect(self):
        """获取实际图像显示区域"""
        try:
            from PyQt6.QtCore import QRect

            if not hasattr(self.cover_preview, 'original_pixmap') or not self.cover_preview.original_pixmap:
                return None

            # 获取原始图像尺寸
            original_pixmap = self.cover_preview.original_pixmap
            original_width = original_pixmap.width()
            original_height = original_height = original_pixmap.height()

            # 获取预览标签尺寸
            label_width = self.cover_preview.width()
            label_height = self.cover_preview.height()

            if label_width <= 0 or label_height <= 0:
                return None

            # 计算保持宽高比的缩放尺寸
            original_aspect = original_width / original_height
            label_aspect = label_width / label_height

            if original_aspect > label_aspect:
                # 图像更宽，以宽度为准
                scaled_width = label_width
                scaled_height = int(label_width / original_aspect)
            else:
                # 图像更高，以高度为准
                scaled_height = label_height
                scaled_width = int(label_height * original_aspect)

            # 计算图像在标签中的位置（居中显示）
            x = (label_width - scaled_width) // 2
            y = (label_height - scaled_height) // 2

            return QRect(x, y, scaled_width, scaled_height)

        except Exception as e:
            print(f"❌ 获取实际图像显示区域失败: {e}")
            return None

    def _create_text_input(self, position=None):
        """创建文本输入框 - 简化版本"""
        try:
            from PyQt6.QtWidgets import QTextEdit
            from PyQt6.QtCore import Qt

            # 处理现有输入框
            if hasattr(self, 'text_input') and self.text_input:
                current_text = self.text_input.toPlainText()
                if current_text.strip():
                    # 保留有内容的输入框
                    if not hasattr(self, 'text_input_list'):
                        self.text_input_list = []
                    if self.text_input not in self.text_input_list:
                        self.text_input_list.append(self.text_input)
                else:
                    # 删除空输入框
                    self.text_input.setParent(None)
                    self.text_input.deleteLater()

            # 创建新输入框
            self.text_input = QTextEdit(self.cover_preview)

            # 基本设置
            self.text_input.setLineWrapMode(QTextEdit.LineWrapMode.NoWrap)
            self.text_input.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            self.text_input.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            self.text_input.document().setDocumentMargin(4)

            # 🚀 修复：优先使用缓存的字体属性，然后是窗口设置，最后是默认值
            if hasattr(self, '_cached_font_properties') and self._cached_font_properties:
                print("📝 使用缓存的字体属性")
                properties = self._cached_font_properties
                font_name = properties.get('font_family', 'Microsoft YaHei UI')
                font_size = properties.get('font_size', 24)
                font_style = properties.get('font_style', '正常')
            else:
                print("📝 获取文字属性窗口的默认设置")
                font_name, font_size, font_style = self._get_default_text_properties()

            fill_color, stroke_color, stroke_width = self._get_text_colors()
            print(f"🎨 创建文本输入框时的字体设置: {font_name}, {font_size}pt, {font_style}")

            # 🚀 修复：使用改进的字体设置逻辑
            from PyQt6.QtGui import QFontDatabase, QFont

            # 验证字体是否存在
            available_fonts = QFontDatabase.families()
            actual_font_name = font_name

            if font_name not in available_fonts:
                # 尝试常见的中文字体映射
                font_mapping = {
                    '微软雅黑': ['Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑'],
                    'Microsoft YaHei': ['Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑'],
                    '宋体': ['SimSun', 'NSimSun', '宋体'],
                    '黑体': ['SimHei', '黑体'],
                    '楷体': ['KaiTi', '楷体'],
                    '仿宋': ['FangSong', '仿宋']
                }

                # 查找映射字体
                found_font = None
                for mapped_fonts in font_mapping.values():
                    if font_name in mapped_fonts:
                        for candidate in mapped_fonts:
                            if candidate in available_fonts:
                                found_font = candidate
                                break
                        if found_font:
                            break

                if found_font:
                    actual_font_name = found_font
                else:
                    actual_font_name = "Microsoft YaHei UI"

                print(f"🔤 字体映射: {font_name} -> {actual_font_name}")

            # 设置字体
            font = QFont(actual_font_name)
            font.setPointSize(max(8, min(72, font_size)))

            if font_style in ["粗体", "Bold", "bold"]:
                font.setBold(True)
                font.setItalic(False)
            elif font_style in ["斜体", "Italic", "italic"]:
                font.setBold(False)
                font.setItalic(True)
            elif font_style in ["粗斜体", "Bold Italic", "bold italic"]:
                font.setBold(True)
                font.setItalic(True)
            else:
                font.setBold(False)
                font.setItalic(False)

            self.text_input.setFont(font)

            # 🚀 修复：设置输入框样式，确保字体被正确应用，应用背景透明度
            # 获取背景设置
            bg_color, bg_opacity = self._get_background_settings()

            # 计算背景颜色的RGBA值
            if bg_opacity == 0:
                background_color = "transparent"
            else:
                # 将十六进制颜色转换为RGB，然后添加透明度
                from PyQt6.QtGui import QColor
                qcolor = QColor(bg_color)
                r, g, b = qcolor.red(), qcolor.green(), qcolor.blue()
                alpha = int(255 * bg_opacity / 100)  # 将百分比转换为0-255
                background_color = f"rgba({r}, {g}, {b}, {alpha})"

            # 🚀 修改：获取字体间距和行距设置
            properties = {}
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                properties = self.text_properties_window.get_current_properties()

            letter_spacing = properties.get('letter_spacing', 0)  # 字体间距
            # 🚀 修复：移除CSS中的line-height设置，只使用QTextDocument设置行距

            style_sheet = f"""
                QTextEdit {{
                    font-family: "{actual_font_name}";
                    font-size: {font_size}pt;
                    font-weight: {"bold" if font.bold() else "normal"};
                    font-style: {"italic" if font.italic() else "normal"};
                    background-color: {background_color};
                    border: none;
                    padding: 4px 8px;
                    color: {fill_color};
                    letter-spacing: {letter_spacing}px;
                }}
                QTextEdit:focus {{
                    background-color: {background_color};
                    color: {fill_color};
                }}
            """
            self.text_input.setStyleSheet(style_sheet)

            print(f"🎨 应用文字颜色: 填充={fill_color}, 描边={stroke_color}, 宽度={stroke_width}")

            # 🚀 修复：完全禁用右键菜单，专注于拖动功能
            self.text_input.setContextMenuPolicy(Qt.ContextMenuPolicy.NoContextMenu)

            # 设置拖动相关属性
            self.text_input_dragging = False
            self.drag_start_pos = None

            # 设置鼠标事件处理
            self._setup_text_input_mouse_events()

            # 设置位置和大小
            if position:
                # 使用鼠标点击位置
                x = position.x() - 100  # 向左偏移一点，让光标在输入框中间
                y = position.y() - 20   # 向上偏移一点
                # 🚀 修改：确保不超出预览图边界，初始单行高度
                x = max(0, min(x, self.cover_preview.width() - 200))
                y = max(0, min(y, self.cover_preview.height() - 30))  # 单行高度
                self.text_input.setGeometry(x, y, 200, 30)  # 初始单行高度

                # 保存相对位置（基于实际图像显示区域）
                image_rect = self._get_actual_image_rect()
                if image_rect and image_rect.width() > 0 and image_rect.height() > 0:
                    # 相对于实际图像显示区域的位置
                    relative_x_in_image = (x - image_rect.x()) / image_rect.width()
                    relative_y_in_image = (y - image_rect.y()) / image_rect.height()
                    self.text_input_relative_x = relative_x_in_image
                    self.text_input_relative_y = relative_y_in_image
                    print(f"📍 保存文字输入框相对位置: ({self.text_input_relative_x:.3f}, {self.text_input_relative_y:.3f})")
                    print(f"📐 图像显示区域: {image_rect.x()}, {image_rect.y()}, {image_rect.width()}x{image_rect.height()}")
                else:
                    # 备用方案：相对于整个预览区域
                    self.text_input_relative_x = x / self.cover_preview.width()
                    self.text_input_relative_y = y / self.cover_preview.height()
            else:
                # 🚀 修改：默认位置，初始单行高度
                self.text_input.setGeometry(50, 50, 200, 30)
                # 保存默认相对位置（基于实际图像显示区域）
                image_rect = self._get_actual_image_rect()
                if image_rect and image_rect.width() > 0 and image_rect.height() > 0:
                    relative_x_in_image = (50 - image_rect.x()) / image_rect.width()
                    relative_y_in_image = (50 - image_rect.y()) / image_rect.height()
                    self.text_input_relative_x = relative_x_in_image
                    self.text_input_relative_y = relative_y_in_image
                else:
                    # 备用方案
                    self.text_input_relative_x = 50 / self.cover_preview.width()
                    self.text_input_relative_y = 50 / self.cover_preview.height()

            # 确保输入框在最上层
            self.text_input.raise_()

            print(f"📝 输入框位置: ({self.text_input.x()}, {self.text_input.y()})")
            print(f"📝 输入框大小: {self.text_input.width()}x{self.text_input.height()}")

            # 🚀 修改：连接QTextEdit的信号
            self.text_input.textChanged.connect(self._on_text_input_changed)
            # QTextEdit没有returnPressed信号，我们将使用键盘事件处理

            # 🚀 修复：应用文字输入框保护机制
            self._apply_text_input_protection(self.text_input)

            # 🚀 修复：设置鼠标事件处理
            self._setup_text_input_mouse_events()

            # 🚀 新增：设置文档格式（包括行距）
            self._setup_text_document_formatting()

            # 显示并聚焦
            self.text_input.show()
            self.text_input.setFocus()

            print("✅ 文本输入框已创建，包含行距设置")

        except Exception as e:
            print(f"❌ 创建文本输入框失败: {e}")
            self.logger.error(f"创建文本输入框失败: {e}")

    def _get_default_text_properties(self):
        """获取文字属性窗口的默认设置"""
        try:
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                properties = self.text_properties_window.get_current_properties()
                font_family = properties.get('font_family', 'Arial')
                font_size = properties.get('font_size', 24)
                font_style = properties.get('font_style', 'Regular')
                print(f"🔤 获取到字体属性: {font_family}, {font_size}pt, {font_style}")
                return font_family, font_size, font_style
            else:
                # 默认值
                return 'Arial', 24, 'Regular'
        except Exception as e:
            print(f"❌ 获取默认文字属性失败: {e}")
            return 'Arial', 24, 'Regular'

    def _get_text_colors(self):
        """获取文字颜色设置"""
        try:
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                # 获取填充颜色
                fill_color_style = self.text_properties_window.fill_color_btn.styleSheet()
                fill_color = self._extract_color_from_style(fill_color_style)

                # 获取描边颜色
                stroke_color_style = self.text_properties_window.stroke_color_btn.styleSheet()
                stroke_color = self._extract_color_from_style(stroke_color_style)

                # 获取描边宽度
                stroke_width = self.text_properties_window.stroke_width_spin.value()

                return fill_color, stroke_color, stroke_width
            else:
                # 默认值：白色填充，黑色描边，宽度2
                return "#ffffff", "#000000", 2

        except Exception as e:
            print(f"❌ 获取文字颜色失败: {e}")
            return "#ffffff", "#000000", 2

    def _get_background_settings(self):
        """获取背景颜色和透明度设置"""
        try:
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                # 获取背景颜色
                bg_color_style = self.text_properties_window.bg_color_btn.styleSheet()
                bg_color = self._extract_color_from_style(bg_color_style)

                # 获取背景透明度
                bg_opacity = self.text_properties_window.bg_opacity_slider.value()

                return bg_color, bg_opacity
            else:
                # 默认值：蓝色背景，透明度0
                return "#0000ff", 0

        except Exception as e:
            print(f"❌ 获取背景设置失败: {e}")
            return "#0000ff", 0

    def _extract_color_from_style(self, style_sheet):
        """从样式表中提取颜色值"""
        try:
            import re
            # 查找 background-color: #xxxxxx 或 background-color: colorname
            match = re.search(r'background-color:\s*([^;]+)', style_sheet)
            if match:
                color = match.group(1).strip()
                # 如果是颜色名称，转换为十六进制
                if not color.startswith('#'):
                    from PyQt6.QtGui import QColor
                    qcolor = QColor(color)
                    if qcolor.isValid():
                        return qcolor.name()
                return color
            return "#ffffff"  # 默认白色
        except Exception as e:
            print(f"❌ 提取颜色失败: {e}")
            return "#ffffff"

    def _on_text_input_changed(self):
        """文本输入内容变化处理 - 简化版本"""
        try:
            if not hasattr(self, 'text_input') or not self.text_input:
                return

            text = self.text_input.toPlainText()
            print(f"📝 文本变化: '{text[:20]}...' ({len(text)}字符)")

            # 调整输入框大小
            self._resize_text_input(text)

        except Exception as e:
            print(f"❌ 文本变化处理失败: {e}")

    def _resize_text_input(self, text):
        """根据文字内容调整输入框大小 - 🚀 修改：支持多行文本，禁用自动换行"""
        try:
            print(f"🔧 开始调整输入框大小，文本: '{text}'")
            if not hasattr(self, 'text_input') or not self.text_input:
                print("❌ 文本输入框不存在，无法调整大小")
                return

            # 🚀 修改：对于禁用自动换行的QTextEdit，根据实际文本行数和宽度调整
            if not text:
                # 空文本时使用默认大小
                current_geometry = self.text_input.geometry()
                self.text_input.setGeometry(
                    current_geometry.x(),
                    current_geometry.y(),
                    200,  # 默认宽度
                    30    # 默认高度，单行高度
                )
                return

            # 🚀 修复：按回车键分割的行数（真实换行）
            lines = text.split('\n')
            line_count = len(lines)

            # 获取字体度量
            font_metrics = self.text_input.fontMetrics()

            # 🚀 修复：计算每行的实际宽度，找出最宽的行
            max_width = 0
            for line in lines:
                if line:  # 非空行
                    line_width = font_metrics.horizontalAdvance(line)
                    max_width = max(max_width, line_width)
                else:  # 空行也要考虑最小宽度
                    max_width = max(max_width, 100)

            # 🚀 修复：计算尺寸，确保能容纳最长的行
            padding = 20
            new_width = max(max_width + padding, 200)  # 最小宽度200

            # 🚀 修复：计算高度，考虑行距设置
            base_line_height = font_metrics.height()

            # 获取行距设置
            properties = {}
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                properties = self.text_properties_window.get_current_properties()
            line_spacing = properties.get('line_spacing', 1.2)

            if line_count == 1:
                # 单行文字，也要考虑行距，确保输入框高度足够
                line_height = base_line_height * line_spacing
                new_height = line_height + padding
            else:
                # 多行文字，应用行距设置
                line_height = base_line_height * line_spacing
                # 🚀 修复：多行高度计算 = 第一行高度 + (行数-1) * 行距高度
                new_height = base_line_height + (line_count - 1) * line_height + padding

            # 确保最小高度
            new_height = max(new_height, 30)

            # 🚀 修复：限制最大尺寸，避免输入框过大
            max_width_limit = self.cover_preview.width() - 50
            max_height_limit = self.cover_preview.height() - 50
            new_width = min(new_width, max_width_limit)
            new_height = min(new_height, max_height_limit)

            # 调整输入框大小
            current_geometry = self.text_input.geometry()
            self.text_input.setGeometry(
                current_geometry.x(),
                current_geometry.y(),
                int(new_width),  # 🚀 修复：转换为整数
                int(new_height)  # 🚀 修复：转换为整数
            )

            print(f"📏 输入框大小调整: {line_count}行, 最大宽度{max_width}px, 新尺寸{new_width}x{new_height}")

        except Exception as e:
            print(f"❌ 调整输入框大小失败: {e}")
            self.logger.error(f"调整输入框大小失败: {e}")

    def _setup_text_document_formatting(self):
        """设置文本文档格式，包括行距 - 简化版本"""
        try:
            if not hasattr(self, 'text_input') or not self.text_input:
                return

            # 获取行距设置
            line_spacing = 1.2  # 默认值
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                properties = self.text_properties_window.get_current_properties()
                line_spacing = properties.get('line_spacing', 1.2)

            # 简单直接的行距设置
            from PyQt6.QtGui import QTextBlockFormat, QTextCursor

            document = self.text_input.document()
            cursor = QTextCursor(document)

            # 选择所有文本
            cursor.select(QTextCursor.SelectionType.Document)

            # 设置行距
            block_format = QTextBlockFormat()
            block_format.setLineHeight(line_spacing * 100, QTextBlockFormat.LineHeightTypes.ProportionalHeight)
            cursor.mergeBlockFormat(block_format)

            # 设置默认格式
            document.setDefaultBlockFormat(block_format)

            print(f"📏 行距设置: {line_spacing}倍")

        except Exception as e:
            print(f"❌ 行距设置失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_text_input_font(self):
        """更新文本输入框的字体"""
        try:
            if not hasattr(self, 'text_input') or not self.text_input:
                print("❌ 文本输入框不存在，无法更新字体")
                return

            print("🔤 开始更新文本输入框字体...")

            # 获取当前文字属性
            font_name, font_size, font_style = self._get_default_text_properties()
            print(f"🔤 将应用字体: {font_name}, {font_size}pt, {font_style}")

            # 验证字体是否存在
            from PyQt6.QtGui import QFont, QFontDatabase

            # 🚀 修复：获取所有可用字体，包括应用程序字体
            available_fonts = QFontDatabase.families()
            print(f"🔤 系统可用字体数量: {len(available_fonts)}")

            # 🚀 修复：更智能的字体匹配
            actual_font_name = font_name
            if font_name not in available_fonts:
                print(f"⚠️ 字体 '{font_name}' 不在系统中，查找相似字体...")

                # 尝试常见的中文字体映射
                font_mapping = {
                    '微软雅黑': ['Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑'],
                    'Microsoft YaHei': ['Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑'],
                    '宋体': ['SimSun', 'NSimSun', '宋体'],
                    '黑体': ['SimHei', '黑体'],
                    '楷体': ['KaiTi', '楷体'],
                    '仿宋': ['FangSong', '仿宋']
                }

                # 查找映射字体
                found_font = None
                for mapped_fonts in font_mapping.values():
                    if font_name in mapped_fonts:
                        for candidate in mapped_fonts:
                            if candidate in available_fonts:
                                found_font = candidate
                                break
                        if found_font:
                            break

                if found_font:
                    actual_font_name = found_font
                    print(f"🔤 使用映射字体: {actual_font_name}")
                else:
                    # 查找相似字体
                    similar_fonts = [f for f in available_fonts if font_name.lower() in f.lower() or f.lower() in font_name.lower()]
                    if similar_fonts:
                        actual_font_name = similar_fonts[0]
                        print(f"🔤 使用相似字体: {actual_font_name}")
                    else:
                        print(f"🔤 使用默认字体: Microsoft YaHei UI")
                        actual_font_name = "Microsoft YaHei UI"
            else:
                print(f"✅ 字体 '{font_name}' 存在于系统中")

            # 🚀 修复：创建新字体时使用实际字体名
            font = QFont(actual_font_name)
            font.setPointSize(max(8, min(72, font_size)))  # 限制字体大小范围

            # 🚀 修复：更准确的字体样式设置
            if font_style in ["粗体", "Bold", "bold"]:
                font.setBold(True)
                font.setItalic(False)
                print("🔤 设置粗体")
            elif font_style in ["斜体", "Italic", "italic"]:
                font.setBold(False)
                font.setItalic(True)
                print("🔤 设置斜体")
            elif font_style in ["粗斜体", "Bold Italic", "bold italic"]:
                font.setBold(True)
                font.setItalic(True)
                print("🔤 设置粗斜体")
            else:
                font.setBold(False)
                font.setItalic(False)
                print("🔤 设置正常样式")

            # 应用字体
            old_font = self.text_input.font()
            print(f"🔤 原字体: {old_font.family()}, {old_font.pointSize()}pt")

            # 🚀 修复：强制设置字体
            self.text_input.setFont(font)

            # 🚀 修复：确保字体真的被应用，应用背景透明度，使用正确的文字颜色
            # 获取当前文字颜色和背景设置
            fill_color, _, _ = self._get_text_colors()
            bg_color, bg_opacity = self._get_background_settings()

            # 计算背景颜色的RGBA值
            if bg_opacity == 0:
                background_color = "transparent"
            else:
                from PyQt6.QtGui import QColor
                qcolor = QColor(bg_color)
                r, g, b = qcolor.red(), qcolor.green(), qcolor.blue()
                alpha = int(255 * bg_opacity / 100)
                background_color = f"rgba({r}, {g}, {b}, {alpha})"

            # 🚀 修改：获取字体间距和行距设置
            properties = {}
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                properties = self.text_properties_window.get_current_properties()

            letter_spacing = properties.get('letter_spacing', 0)
            line_spacing = properties.get('line_spacing', 1.2)

            self.text_input.setStyleSheet(f"""
                QTextEdit {{
                    font-family: "{actual_font_name}";
                    font-size: {font_size}pt;
                    font-weight: {"bold" if font.bold() else "normal"};
                    font-style: {"italic" if font.italic() else "normal"};
                    background-color: {background_color};
                    border: none;
                    padding: 4px 8px;
                    color: {fill_color};
                    letter-spacing: {letter_spacing}px;
                }}
            """)

            # 强制刷新显示
            self.text_input.update()
            self.text_input.repaint()

            new_font = self.text_input.font()
            print(f"🔤 新字体: {new_font.family()}, {new_font.pointSize()}pt")

            # 验证字体是否真的改变了
            if old_font.family() != new_font.family() or old_font.pointSize() != new_font.pointSize():
                print("✅ 字体确实发生了变化")
            else:
                print("⚠️ 字体没有变化，但样式表已更新")

            # 重新调整大小
            self._resize_text_input(self.text_input.toPlainText())  # 🚀 修改：QTextEdit使用toPlainText()

            print("✅ 文本输入框字体更新完成")

        except Exception as e:
            print(f"❌ 更新文本输入框字体失败: {e}")
            self.logger.error(f"更新文本输入框字体失败: {e}")

    def _update_text_input_colors(self):
        """更新文本输入框颜色"""
        try:
            if not hasattr(self, 'text_input') or not self.text_input:
                return

            # 🚀 修复：获取最新的颜色和字体设置
            fill_color, stroke_color, stroke_width = self._get_text_colors()
            font_name, font_size, font_style = self._get_default_text_properties()

            # 🚀 修复：字体映射逻辑
            from PyQt6.QtGui import QFontDatabase
            available_fonts = QFontDatabase.families()
            actual_font_name = font_name

            if font_name not in available_fonts:
                font_mapping = {
                    '微软雅黑': ['Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑'],
                    'Microsoft YaHei': ['Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑'],
                    '宋体': ['SimSun', 'NSimSun', '宋体'],
                    '黑体': ['SimHei', '黑体'],
                    '楷体': ['KaiTi', '楷体'],
                    '仿宋': ['FangSong', '仿宋']
                }

                found_font = None
                for mapped_fonts in font_mapping.values():
                    if font_name in mapped_fonts:
                        for candidate in mapped_fonts:
                            if candidate in available_fonts:
                                found_font = candidate
                                break
                        if found_font:
                            break

                if found_font:
                    actual_font_name = found_font
                else:
                    actual_font_name = "Microsoft YaHei UI"

            # 🚀 修复：更新样式表，包含完整的字体信息，应用背景透明度
            font_weight = "bold" if font_style in ["粗体", "Bold", "bold"] else "normal"
            font_style_css = "italic" if font_style in ["斜体", "Italic", "italic"] else "normal"

            # 获取背景设置
            bg_color, bg_opacity = self._get_background_settings()

            # 计算背景颜色的RGBA值
            if bg_opacity == 0:
                background_color = "transparent"
            else:
                from PyQt6.QtGui import QColor
                qcolor = QColor(bg_color)
                r, g, b = qcolor.red(), qcolor.green(), qcolor.blue()
                alpha = int(255 * bg_opacity / 100)
                background_color = f"rgba({r}, {g}, {b}, {alpha})"

            # 🚀 修改：获取字体间距和行距设置
            properties = {}
            if hasattr(self, 'text_properties_window') and self.text_properties_window:
                properties = self.text_properties_window.get_current_properties()

            letter_spacing = properties.get('letter_spacing', 0)
            # 🚀 修复：移除CSS中的line-height设置，只使用QTextDocument设置行距

            style_sheet = f"""
                QTextEdit {{
                    font-family: "{actual_font_name}";
                    font-size: {font_size}pt;
                    font-weight: {font_weight};
                    font-style: {font_style_css};
                    background-color: {background_color};
                    border: none;
                    padding: 4px 8px;
                    color: {fill_color};
                    letter-spacing: {letter_spacing}px;
                }}
                QTextEdit:focus {{
                    background-color: {background_color};
                    color: {fill_color};
                }}
            """
            self.text_input.setStyleSheet(style_sheet)

            print(f"🎨 文本输入框样式已更新: 字体={actual_font_name}, 大小={font_size}pt, 样式={font_style}, 颜色={fill_color}")

        except Exception as e:
            print(f"❌ 更新文本输入框颜色失败: {e}")
            self.logger.error(f"更新文本输入框颜色失败: {e}")

    def _finish_text_input(self):
        """完成文本输入"""
        try:
            print("✅ 文本输入完成")

            # 获取输入的文字
            if hasattr(self, 'text_input') and self.text_input:
                text = self.text_input.toPlainText()  # 🚀 修改：QTextEdit使用toPlainText()
                print(f"📝 输入的文字: {text}")

                # 隐藏输入框
                self.text_input.hide()

                # 这里可以添加将文字渲染到图像的逻辑

            # 关闭文字输入模式
            self._deactivate_text_input_mode()

        except Exception as e:
            print(f"❌ 完成文本输入失败: {e}")
            self.logger.error(f"完成文本输入失败: {e}")

    def _deactivate_text_input_mode(self):
        """关闭文字输入模式"""
        try:
            print("🔚 关闭文字输入模式")

            # 恢复原始鼠标事件
            if hasattr(self, '_original_mouse_press_event'):
                self.cover_preview.mousePressEvent = self._original_mouse_press_event

            # 清除文字输入状态
            self.text_input_active = False

            # 清理输入框
            if hasattr(self, 'text_input') and self.text_input:
                self.text_input.setParent(None)
                self.text_input.deleteLater()
                self.text_input = None

            print("✅ 文字输入模式已关闭")

        except Exception as e:
            print(f"❌ 关闭文字输入模式失败: {e}")
            self.logger.error(f"关闭文字输入模式失败: {e}")

    def on_text_properties_changed(self):
        """文字属性变化处理 - 简化版本"""
        try:
            print("🔔 文字属性变化")

            if hasattr(self, 'text_input') and self.text_input:
                # 更新现有输入框
                self._update_text_input_font()
                self._update_text_input_colors()
                self._setup_text_document_formatting()
                self._resize_text_input(self.text_input.toPlainText())
                print("✅ 输入框已更新")

        except Exception as e:
            print(f"❌ 属性更新失败: {e}")

    def on_text_properties_window_closed(self):
        """文字属性窗口关闭处理 - 🏗️ 新的模块化设计"""
        try:
            print("📝 文字属性窗口已关闭")
            if hasattr(self, 'text_properties_window'):
                self.text_properties_window = None
        except Exception as e:
            print(f"❌ 处理窗口关闭失败: {e}")
            self.logger.error(f"处理窗口关闭失败: {e}")

    # 🏗️ 新设计：不再需要文字图层大小同步

    def open_export_settings(self):
        """打开导出设置 - 调用export_settings模块"""
        try:
            from .utils.export_settings import VideoExportSettings

            # 检查是否已经有导出设置窗口
            if hasattr(self, 'export_settings_window') and self.export_settings_window:
                # 如果窗口已存在，显示并置顶
                self.export_settings_window.show()
                self.export_settings_window.raise_()
                self.export_settings_window.activateWindow()
                return

            # 创建导出设置窗口
            self.export_settings_window = VideoExportSettings()

            # 连接设置更新信号
            self.export_settings_window.settings_updated.connect(self.on_export_settings_updated)

            # 显示设置窗口
            self.export_settings_window.show()

            self.logger.info("打开导出设置窗口")

        except Exception as e:
            self.logger.error(f"打开导出设置失败: {e}")
            # 如果模块加载失败，显示简单提示
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "导出设置", "导出设置模块加载失败，请检查模块是否正确安装")

    def on_export_settings_updated(self, settings):
        """处理导出设置更新"""
        try:
            self.export_settings = settings
            self.logger.info(f"导出设置已更新: {settings}")
        except Exception as e:
            self.logger.error(f"处理导出设置更新失败: {e}")

    def _create_batch_group(self):
        """创建批量处理组 - 与原版完全一致"""
        from PyQt6.QtWidgets import QGroupBox, QVBoxLayout, QHBoxLayout, QCheckBox, QPushButton

        batch_group = QGroupBox("批量处理")
        batch_layout = QVBoxLayout(batch_group)
        batch_layout.setContentsMargins(10, 15, 10, 10)
        batch_layout.setSpacing(8)

        # 批量处理开关行（水平布局）
        batch_header_layout = QHBoxLayout()

        self.batch_mode_checkbox = QCheckBox("▲ 启用批量处理模式")
        self.batch_mode_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ABB2BF;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 0px;
                height: 0px;
            }
        """)
        self.batch_mode_checkbox.toggled.connect(self.toggle_batch_mode)
        batch_header_layout.addWidget(self.batch_mode_checkbox)

        # 添加弹性空间
        batch_header_layout.addStretch()

        # 重置处理记录按钮（最简单样式）
        self.reset_batch_btn = QPushButton("重置记录")
        self.reset_batch_btn.setFixedHeight(24)
        self.reset_batch_btn.setFixedWidth(80)
        self.reset_batch_btn.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 4px;
                font-size: 12px;
                padding: 2px 6px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #999999;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        self.reset_batch_btn.clicked.connect(self.reset_batch_processing)
        batch_header_layout.addWidget(self.reset_batch_btn)

        batch_layout.addLayout(batch_header_layout)

        # 批量处理设置（初始隐藏）
        self.batch_settings_widget = self._create_batch_settings()
        self.batch_settings_widget.hide()  # 初始隐藏
        batch_layout.addWidget(self.batch_settings_widget)

        return batch_group

    def _create_batch_settings(self):
        """创建批量处理设置 - 与原版完全一致"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QCheckBox

        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # 监控文件夹
        folder_layout = QHBoxLayout()
        folder_layout.addWidget(QLabel("监控文件夹:"))
        self.watch_folder_edit = QLineEdit()
        self.watch_folder_edit.setPlaceholderText("选择要监控的文件夹...")
        self.watch_folder_edit.setReadOnly(True)
        folder_layout.addWidget(self.watch_folder_edit)

        self.browse_watch_folder_btn = QPushButton("浏览")
        self.browse_watch_folder_btn.setFixedWidth(60)
        self.browse_watch_folder_btn.clicked.connect(self.browse_watch_folder)
        folder_layout.addWidget(self.browse_watch_folder_btn)
        layout.addLayout(folder_layout)

        # 输出文件夹
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出文件夹:"))
        self.output_folder_edit = QLineEdit()
        self.output_folder_edit.setPlaceholderText("选择输出文件夹...")
        self.output_folder_edit.setReadOnly(True)
        output_layout.addWidget(self.output_folder_edit)

        self.browse_output_folder_btn = QPushButton("浏览")
        self.browse_output_folder_btn.setFixedWidth(60)
        self.browse_output_folder_btn.clicked.connect(self.browse_output_folder)
        output_layout.addWidget(self.browse_output_folder_btn)
        layout.addLayout(output_layout)

        # 批量处理状态和控制 - 与原版完全一致
        status_layout = QHBoxLayout()

        self.batch_status_label = QLabel("状态: 未启动")
        self.batch_status_label.setStyleSheet("color: #98C379; font-size: 12px;")
        status_layout.addWidget(self.batch_status_label)

        # 批量处理启动勾选框 - 与原版完全一致
        self.batch_enable_checkbox = QCheckBox("启动批量处理")
        self.batch_enable_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ABB2BF;
                font-size: 12px;
                margin-left: 20px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #5C6370;
                background-color: transparent;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #98C379;
                background-color: #98C379;
                border-radius: 3px;
            }
        """)
        self.batch_enable_checkbox.toggled.connect(self.toggle_batch_processing)
        status_layout.addWidget(self.batch_enable_checkbox)
        status_layout.addStretch()

        layout.addLayout(status_layout)

        # 删除重复的batch_status_label定义，已在上面的status_layout中定义

        # 批量处理日志显示
        from PyQt6.QtWidgets import QTextEdit
        self.batch_log_display = QTextEdit()
        self.batch_log_display.setMaximumHeight(150)
        self.batch_log_display.setReadOnly(True)
        self.batch_log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #D4D4D4;
                border: 1px solid #3C3C3C;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                padding: 4px;
            }
        """)
        layout.addWidget(self.batch_log_display)

        return widget

    # === 批量处理功能方法 ===
    # toggle_batch_mode 方法在下面完整实现，删除重复定义
    # reset_batch_processing 方法已在下面完整实现，删除重复定义

    def browse_watch_folder(self):
        """浏览监控文件夹 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            folder = QFileDialog.getExistingDirectory(self, "选择监控文件夹")
            if folder and hasattr(self, 'watch_folder_edit'):
                self.watch_folder_edit.setText(folder)
                self.watch_folder = folder
                self._check_batch_ready()
                self.logger.info(f"设置监控文件夹: {folder}")
        except Exception as e:
            self.logger.error(f"浏览监控文件夹失败: {e}")

    def browse_output_folder(self):
        """浏览输出文件夹 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            folder = QFileDialog.getExistingDirectory(self, "选择输出文件夹")
            if folder and hasattr(self, 'output_folder_edit'):
                self.output_folder_edit.setText(folder)
                self.output_folder = folder
                self._check_batch_ready()
                self.logger.info(f"设置输出文件夹: {folder}")
        except Exception as e:
            self.logger.error(f"浏览输出文件夹失败: {e}")

    def _check_batch_ready(self):
        """检查批量处理是否准备就绪 - 与原版完全一致"""
        try:
            ready = (hasattr(self, 'watch_folder') and self.watch_folder and
                    hasattr(self, 'output_folder') and self.output_folder)

            if hasattr(self, 'batch_enable_checkbox'):
                self.batch_enable_checkbox.setEnabled(ready)
                if ready:
                    self.batch_enable_checkbox.setText("启动批量处理")
                else:
                    self.batch_enable_checkbox.setText("请先设置文件夹")

        except Exception as e:
            self.logger.error(f"检查批量处理状态失败: {e}")

    # 移除复杂的验证方法，使用简单的日志记录
    def _log_ui_creation_complete(self):
        """记录UI创建完成"""
        self.logger.info("✅ UI创建完成，使用原版cover_edit布局")

    # 删除复杂的显示方法，UI已经通过原版方式正确创建

    # === 以下是从原版cover_edit迁移的功能方法 ===

    def load_video(self, file_path: str = None):
        """加载视频文件 - 与原版完全一致"""
        try:
            import cv2
            import os
            from PyQt6.QtWidgets import QFileDialog, QMessageBox
            from PyQt6.QtCore import QUrl

            if not file_path:
                file_path, _ = QFileDialog.getOpenFileName(
                    self, "选择视频文件", "",
                    "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv);;所有文件 (*)"
                )

            if not file_path:
                return False

            if not os.path.exists(file_path):
                QMessageBox.warning(self, "警告", f"文件不存在: {file_path}")
                return False

            # 释放之前的视频资源
            if hasattr(self, 'video_cap') and self.video_cap:
                self.video_cap.release()

            # 打开视频文件
            self.video_cap = cv2.VideoCapture(file_path)
            if not self.video_cap.isOpened():
                # 尝试使用不同的后端
                self.logger.info("默认后端无法打开视频，尝试其他后端...")
                self.video_cap = cv2.VideoCapture(file_path, cv2.CAP_FFMPEG)
                if not self.video_cap.isOpened():
                    QMessageBox.critical(self, "错误", "无法打开视频文件（已尝试多种后端）")
                    return False

            # 获取视频信息
            self.video_path = file_path
            self.fps = self.video_cap.get(cv2.CAP_PROP_FPS)
            self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.video_width = int(self.video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.video_height = int(self.video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.video_aspect_ratio = self.video_width / self.video_height if self.video_height > 0 else 16/9

            # 更新UI显示
            if hasattr(self, 'video_path_edit'):
                self.video_path_edit.setText(os.path.basename(file_path))
            if hasattr(self, 'resolution_label'):
                self.resolution_label.setText(f"{self.video_width}x{self.video_height}")
            if hasattr(self, 'duration_label'):
                self.duration_label.setText(f"{self.duration:.1f}秒")

            # 设置滑块范围
            if hasattr(self, 'frame_slider'):
                self.frame_slider.setRange(0, self.total_frames - 1)

            # 确保切换到截图模式
            self.switch_to_snapshot_mode()

            # 显示第一帧
            if hasattr(self, 'frame_slider'):
                self.frame_slider.setValue(0)
            self.current_frame = 0
            self.show_frame(0)

            # 加载到媒体播放器
            self.load_media_player(file_path)

            # 启用导出按钮
            if hasattr(self, 'export_btn'):
                self.export_btn.setEnabled(True)

            # 设置默认裁剪值并更新UI - 与原版一致
            self.cropped_start_frames = CoverEditConstants.DEFAULT_CROP_START
            self.cropped_end_frames = CoverEditConstants.DEFAULT_CROP_END

            # 更新输入框显示默认值
            if hasattr(self, 'crop_start_input'):
                self.crop_start_input.setText(str(CoverEditConstants.DEFAULT_CROP_START))
            if hasattr(self, 'crop_end_input'):
                self.crop_end_input.setText(str(CoverEditConstants.DEFAULT_CROP_END))

            self.logger.info(f"设置默认裁剪值: 开头{CoverEditConstants.DEFAULT_CROP_START}帧, 结尾{CoverEditConstants.DEFAULT_CROP_END}帧")

            # 自动匹配封面
            self.load_matching_cover_by_aspect_ratio()

            self.logger.info(f"成功加载视频: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"加载视频失败: {e}")
            QMessageBox.critical(self, "错误", f"加载视频时出错: {str(e)}")
            return False

    def show_frame(self, frame_number: int):
        """显示指定帧 - 与原版完全一致"""
        try:
            import cv2
            from PyQt6.QtGui import QImage, QPixmap

            if not hasattr(self, 'video_cap') or not self.video_cap or not self.video_cap.isOpened():
                return

            # 设置视频位置到指定帧
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = self.video_cap.read()

            if ret:
                # 转换BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 转换为QImage和QPixmap
                height, width, _ = frame_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 更新预览显示
                if hasattr(self, 'snapshot_preview'):
                    # 使用set_pixmap方法，与原版保持一致
                    self.snapshot_preview.set_pixmap(pixmap)

                # 更新当前帧号
                self.current_frame = frame_number

                # 更新帧数显示
                if hasattr(self, 'frame_label'):
                    self.frame_label.setText(f"帧: {frame_number}/{self.total_frames}")

        except Exception as e:
            self.logger.error(f"显示帧失败: {e}")

    def load_matching_cover_by_aspect_ratio(self):
        """根据视频宽高比自动加载匹配的Cover图片 - 与原版完全一致"""
        try:
            import os
            from PyQt6.QtGui import QPixmap

            # 检查是否有有效的视频信息
            if not hasattr(self, 'video_aspect_ratio') or not hasattr(self, 'video_width') or not hasattr(self, 'video_height'):
                self.logger.info("没有视频信息，跳过自动匹配封面")
                return

            # Cover文件夹路径 - 与原版路径一致
            cover_folder = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "Cover")

            if not os.path.exists(cover_folder):
                self.logger.info("Cover文件夹不存在")
                return

            # 支持的图片格式 - 与原版一致
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff']

            # 获取当前视频的宽高比
            current_aspect_ratio = self.video_aspect_ratio

            # 定义宽高比类型 - 与原版完全一致
            def get_aspect_ratio_type(aspect_ratio):
                if aspect_ratio > 1.5:  # 横屏 (16:9 ≈ 1.78, 4:3 ≈ 1.33)
                    return "landscape"
                elif aspect_ratio < 0.75:  # 竖屏 (9:16 ≈ 0.56, 3:4 ≈ 0.75)
                    return "portrait"
                else:  # 方形或接近方形
                    return "square"

            current_type = get_aspect_ratio_type(current_aspect_ratio)

            # 收集所有图片及其宽高比信息
            image_candidates = []
            for filename in os.listdir(cover_folder):
                if any(filename.lower().endswith(ext) for ext in image_extensions):
                    image_path = os.path.join(cover_folder, filename)
                    pixmap = QPixmap(image_path)

                    if not pixmap.isNull():
                        img_aspect_ratio = pixmap.width() / pixmap.height()
                        img_type = get_aspect_ratio_type(img_aspect_ratio)

                        # 计算与目标宽高比的差异
                        aspect_diff = abs(img_aspect_ratio - current_aspect_ratio)

                        image_candidates.append({
                            'path': image_path,
                            'filename': filename,
                            'pixmap': pixmap,
                            'aspect_ratio': img_aspect_ratio,
                            'type': img_type,
                            'aspect_diff': aspect_diff
                        })

            if not image_candidates:
                return

            # 优先选择相同类型的图片，然后按宽高比差异排序
            same_type_candidates = [img for img in image_candidates if img['type'] == current_type]

            if same_type_candidates:
                # 选择相同类型中宽高比最接近的
                best_match = min(same_type_candidates, key=lambda x: x['aspect_diff'])
            else:
                # 如果没有相同类型，选择宽高比最接近的
                best_match = min(image_candidates, key=lambda x: x['aspect_diff'])

            # 加载最匹配的图片
            original_pixmap = best_match['pixmap']

            # 缩放图片到视频分辨率
            scaled_pixmap = self._scale_to_video_resolution(original_pixmap)

            # 直接设置到封面预览
            if hasattr(self, 'cover_preview') and scaled_pixmap and not scaled_pixmap.isNull():
                self.cover_preview.setPixmap(scaled_pixmap)

                # 启用文字编辑按钮
                if hasattr(self, 'text_edit_btn_export'):
                    self.text_edit_btn_export.setEnabled(True)
                    print("✅ 文字编辑按钮已启用（自动加载封面后）")

            self.logger.info(f"自动加载匹配封面: {best_match['filename']} (类型: {current_type}, 原始: {original_pixmap.width()}x{original_pixmap.height()}, 缩放至: {self.video_width}x{self.video_height})")

        except Exception as e:
            self.logger.error(f"自动加载匹配封面失败: {e}")

    def _scale_to_video_resolution(self, pixmap):
        """将图片缩放到视频分辨率，保持宽高比 - 与原版一致"""
        try:
            from PyQt6.QtCore import Qt

            if not pixmap or pixmap.isNull():
                return pixmap

            if not hasattr(self, 'video_width') or not hasattr(self, 'video_height'):
                return pixmap

            # 缩放到视频分辨率
            scaled_pixmap = pixmap.scaled(
                self.video_width,
                self.video_height,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            return scaled_pixmap

        except Exception as e:
            self.logger.error(f"缩放图片到视频分辨率失败: {e}")
            return pixmap

    def import_cover_image(self):
        """导入封面图片 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QFileDialog, QMessageBox
            from PyQt6.QtGui import QPixmap

            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择封面图片", "",
                "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif);;所有文件 (*)"
            )

            if file_path:
                # 加载图片
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    # 更新封面预览
                    if hasattr(self, 'cover_preview'):
                        # 缩放图像以适应预览区域
                        scaled_pixmap = pixmap.scaled(
                            self.cover_preview.size(),
                            Qt.AspectRatioMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation
                        )
                        self.cover_preview.setPixmap(scaled_pixmap)

                    # 保存原始图像
                    self._cover_image = pixmap

                    # 启用文字编辑按钮
                    if hasattr(self, 'text_edit_btn_export'):
                        self.text_edit_btn_export.setEnabled(True)

                    self.logger.info(f"成功导入封面: {file_path}")
                    QMessageBox.information(self, "成功", "封面图片导入成功")
                    return True
                else:
                    QMessageBox.warning(self, "警告", "无法加载图片文件")
                    return False
            return False

        except Exception as e:
            self.logger.error(f"导入封面失败: {e}")
            QMessageBox.critical(self, "错误", f"导入封面失败: {str(e)}")
            return False

    # 删除复杂的显示方法，UI已经通过原版方式正确创建

    def _connect_signals(self):
        """连接UI信号 - 与原版完全一致"""
        try:
            # 连接按钮信号
            if hasattr(self, 'load_video_btn'):
                self.load_video_btn.clicked.connect(self.load_video)
            if hasattr(self, 'import_cover_btn'):
                self.import_cover_btn.clicked.connect(self.import_cover_image)
            if hasattr(self, 'export_btn'):
                self.export_btn.clicked.connect(self.export_video)
            if hasattr(self, 'snapshot_btn'):
                self.snapshot_btn.clicked.connect(self.capture_current_frame)

            # 连接模式切换按钮
            if hasattr(self, 'play_mode_btn'):
                self.play_mode_btn.clicked.connect(self.switch_to_play_mode)
            if hasattr(self, 'snapshot_mode_btn'):
                self.snapshot_mode_btn.clicked.connect(self.switch_to_snapshot_mode)
            if hasattr(self, 'play_pause_btn'):
                self.play_pause_btn.clicked.connect(self.toggle_playback)

            # 连接滑块信号
            if hasattr(self, 'frame_slider'):
                self.frame_slider.valueChanged.connect(self.on_frame_slider_changed)

            # 连接批量处理信号
            if hasattr(self, 'batch_mode_checkbox'):
                self.batch_mode_checkbox.toggled.connect(self.toggle_batch_mode)
            if hasattr(self, 'browse_watch_folder_btn'):
                self.browse_watch_folder_btn.clicked.connect(self.browse_watch_folder)
            if hasattr(self, 'browse_output_folder_btn'):
                self.browse_output_folder_btn.clicked.connect(self.browse_output_folder)
            if hasattr(self, 'start_batch_btn'):
                self.start_batch_btn.clicked.connect(self.start_batch_processing)
            if hasattr(self, 'reset_batch_btn'):
                self.reset_batch_btn.clicked.connect(self.reset_batch_processing)

            self.logger.info("UI信号连接完成")

        except Exception as e:
            self.logger.error(f"连接UI信号失败: {e}")

            # 删除重复的代码

    def _setup_media_player(self):
        """设置媒体播放器 - 与原模块完全一致"""
        try:
            from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput

            # 初始化媒体播放器
            self.media_player = QMediaPlayer()
            self.audio_output = QAudioOutput()
            self.media_player.setAudioOutput(self.audio_output)

            # 设置初始音量
            self.audio_output.setVolume(0.7)  # 中等音量

            # 连接媒体播放器信号
            self.media_player.positionChanged.connect(self.update_playback_position)
            self.media_player.durationChanged.connect(self.update_playback_duration)
            self.media_player.errorOccurred.connect(self.handle_media_error)
            self.media_player.playbackStateChanged.connect(self.on_playback_state_changed)

            self.logger.info("媒体播放器设置完成")

        except Exception as e:
            self.logger.error(f"设置媒体播放器失败: {e}")

    def update_playback_position(self, position):
        """更新播放位置 - 与原版完全一致"""
        try:
            if hasattr(self, 'playback_slider') and self.media_player and self.media_player.duration() > 0:
                # 检查是否超出裁剪结束位置
                end_frames = getattr(self, 'cropped_end_frames', 0)
                end_time_ms = int(((self.total_frames - end_frames) / self.fps) * 1000)

                if position >= end_time_ms:
                    # 到达裁剪结束位置，暂停播放
                    self.media_player.pause()
                    # 设置到结束位置
                    self.media_player.setPosition(end_time_ms)
                    position = end_time_ms

                    # 更新滑块位置
                    self.playback_slider.setValue(position)

                # 更新时间标签（显示裁剪后的时间）
                if hasattr(self, 'time_label'):
                    start_frames = getattr(self, 'cropped_start_frames', 0)
                    start_time_ms = int((start_frames / self.fps) * 1000)

                    # 计算相对于裁剪开始的时间
                    relative_position = max(0, position - start_time_ms)
                    current_time = relative_position / 1000

                    # 计算裁剪后的总时长
                    cropped_duration_ms = end_time_ms - start_time_ms
                    total_time = cropped_duration_ms / 1000

                    self.time_label.setText(f"{self.format_time(current_time)} / {self.format_time(total_time)}")

                # 更新当前帧位置
                if hasattr(self, 'fps') and self.fps > 0:
                    self.current_frame = int((position / 1000) * self.fps)

        except Exception as e:
            self.logger.error(f"更新播放位置失败: {e}")

    def update_playback_duration(self, duration):
        """更新播放时长 - 与原版完全一致"""
        try:
            # 设置播放滑块范围到裁剪范围
            if hasattr(self, 'playback_slider') and self.playback_slider:
                # 计算裁剪后的播放范围
                start_time_ms = int((getattr(self, 'cropped_start_frames', 0) / self.fps) * 1000)
                end_time_ms = int(((self.total_frames - getattr(self, 'cropped_end_frames', 0)) / self.fps) * 1000)

                self.playback_slider.setRange(start_time_ms, end_time_ms)

                # 设置播放位置到开始位置
                if hasattr(self, 'media_player') and self.media_player:
                    self.media_player.setPosition(start_time_ms)

        except Exception as e:
            self.logger.error(f"更新播放时长失败: {e}")

    def handle_media_error(self, error):
        """处理媒体播放错误"""
        self.logger.error(f"媒体播放错误: {error}")

    def on_playback_state_changed(self, state):
        """播放状态变化处理"""
        from PyQt6.QtMultimedia import QMediaPlayer
        if hasattr(self, 'play_pause_btn') and self.play_pause_btn:
            if state == QMediaPlayer.PlaybackState.PlayingState:
                self.play_pause_btn.setText("暂停")
            else:
                self.play_pause_btn.setText("播放")

    # 删除复杂的组件复制方法，UI已经通过原版方式正确创建


    
    def _connect_signals(self):
        """连接信号"""
        try:
            # 🚀 修复：连接所有必要的信号

            # 1. 连接核心处理器的信号
            if hasattr(self.video_handler, 'video_loaded'):
                self.video_handler.video_loaded.connect(self.video_loaded.emit)
            if hasattr(self.export_coordinator, 'export_finished'):
                self.export_coordinator.export_finished.connect(self.export_finished.emit)
            if hasattr(self.batch_processor, 'batch_status_changed'):
                self.batch_processor.batch_status_changed.connect(self.batch_status_changed.emit)

            # 2. 连接批量处理器的信号
            if hasattr(self.batch_processor, 'file_found'):
                self.batch_processor.file_found.connect(self._on_batch_file_found)
            if hasattr(self.batch_processor, 'processing_started'):
                self.batch_processor.processing_started.connect(self._on_batch_processing_started)
            if hasattr(self.batch_processor, 'processing_finished'):
                self.batch_processor.processing_finished.connect(self._on_batch_processing_finished)

            # 🚀 修复：连接UI控件信号
            self._connect_ui_signals()

        except Exception as e:
            self.logger.error(f"连接信号失败: {e}")
            import traceback
            self.logger.error(f"信号连接详细错误: {traceback.format_exc()}")

    def _connect_ui_signals(self):
        """连接UI控件信号"""
        # 调用之前定义的信号连接方法
        pass

    def _on_batch_file_found(self, file_path):
        """批量处理发现新文件"""
        self.logger.info(f"发现新文件: {file_path}")

    def _on_batch_processing_started(self, file_path):
        """批量处理开始处理文件"""
        self.logger.info(f"开始处理: {file_path}")

    def _on_batch_processing_finished(self, file_path, success):
        """批量处理完成文件处理"""
        status = "成功" if success else "失败"
        self.logger.info(f"处理完成: {file_path} - {status}")
    
    # ========================================================================
    # 对外接口 - 委托给相应的处理器
    # ========================================================================

    # === 功能方法实现 ===

    def import_cover_image(self):
        """导入封面图像"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择封面图片", "",
                "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif);;所有文件 (*)"
            )
            if file_path:
                self.logger.info(f"导入封面: {file_path}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"导入封面失败: {e}")
            return False

    def capture_current_frame(self):
        """截取当前帧 - 与原版完全一致"""
        try:
            import cv2
            from PyQt6.QtWidgets import QMessageBox
            from PyQt6.QtGui import QImage, QPixmap

            if not hasattr(self, 'video_cap') or not self.video_cap or not self.video_cap.isOpened():
                QMessageBox.warning(self, "警告", "请先加载视频文件")
                return False

            # 获取当前帧位置
            current_frame_pos = getattr(self, 'current_frame', 0)

            # 如果在播放模式，从媒体播放器获取当前位置
            if hasattr(self, 'play_mode_btn') and self.play_mode_btn.isChecked() and hasattr(self, 'media_player') and self.media_player.duration() > 0:
                current_time_ms = self.media_player.position()
                current_time_s = current_time_ms / 1000.0
                if hasattr(self, 'fps') and self.fps > 0:
                    current_frame_pos = int(current_time_s * self.fps)

            # 获取当前帧
            self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame_pos)
            ret, frame = self.video_cap.read()

            if ret:
                # 转换BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 转换为QImage和QPixmap
                height, width, _ = frame_rgb.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 添加为封面图层 - 与原版完全一致
                self.add_cover_layer(
                    pixmap,
                    layer_type="captured",
                    source_info=f"截取帧: {current_frame_pos}"
                )

                QMessageBox.information(self, "成功", f"成功截取第 {current_frame_pos} 帧")
                self.logger.info(f"截取当前帧成功: 第{current_frame_pos}帧")
                return True
            else:
                QMessageBox.warning(self, "警告", "无法读取当前帧")
                return False

        except Exception as e:
            self.logger.error(f"截取当前帧失败: {e}")
            QMessageBox.critical(self, "错误", f"截取帧失败: {str(e)}")
            return False

    def add_cover_layer(self, pixmap, layer_type="manual", source_info=""):
        """添加封面图层 - 与原版完全一致"""
        try:
            if pixmap and not pixmap.isNull():
                # 确保图层列表存在
                if not hasattr(self, 'cover_layers'):
                    self.cover_layers = []
                if not hasattr(self, 'layer_counter'):
                    self.layer_counter = 0

                layer_info = {
                    'id': self.layer_counter,
                    'pixmap': pixmap,
                    'type': layer_type,
                    'source': source_info,
                    'visible': True,
                    'opacity': 1.0
                }
                self.cover_layers.append(layer_info)
                self.layer_counter += 1
                self.current_layer_index = len(self.cover_layers) - 1

                self.logger.info(f"添加图层: {source_info} (总图层数: {len(self.cover_layers)})")

                # 更新封面预览显示
                self.update_composite_cover()

        except Exception as e:
            self.logger.error(f"添加图层失败: {e}")

    def update_composite_cover(self):
        """更新合成封面显示"""
        try:
            if not hasattr(self, 'cover_layers') or not self.cover_layers:
                return

            # 获取最新的图层
            latest_layer = self.cover_layers[-1]
            pixmap = latest_layer['pixmap']

            # 更新封面预览
            if hasattr(self, 'cover_preview') and self.cover_preview:
                # 缩放图像以适应预览区域
                scaled_pixmap = pixmap.scaled(
                    self.cover_preview.size(),
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )
                self.cover_preview.setPixmap(scaled_pixmap)

            # 保存当前封面图像
            self._cover_image = pixmap

            # 启用相关按钮
            if hasattr(self, 'text_edit_btn_export'):
                self.text_edit_btn_export.setEnabled(True)
            if hasattr(self, 'export_btn'):
                self.export_btn.setEnabled(True)

        except Exception as e:
            self.logger.error(f"更新合成封面失败: {e}")

    # 🏗️ 新设计：文字功能已改为独立窗口模式，此方法已移除

    def export_video(self):
        """导出视频 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QFileDialog, QMessageBox

            if not hasattr(self, 'video_path') or not self.video_path:
                QMessageBox.warning(self, "警告", "请先加载视频文件")
                return False

            # 获取导出设置
            export_settings = self._get_export_settings()
            output_format = export_settings.get('output_format', 'MP4').upper()

            # 根据用户选择的格式设置文件对话框
            format_filters = {
                'MP4': "MP4文件 (*.mp4)",
                'AVI': "AVI文件 (*.avi)",
                'MOV': "MOV文件 (*.mov)",
                'MKV': "MKV文件 (*.mkv)",
                'WMV': "WMV文件 (*.wmv)",
                'WEBM': "WEBM文件 (*.webm)",
                'FLV': "FLV文件 (*.flv)",
                'M4V': "M4V文件 (*.m4v)",
                '3GP': "3GP文件 (*.3gp)",
                'OGV': "OGV文件 (*.ogv)",
                'MPEG': "MPEG文件 (*.mpeg)"
            }

            filter_str = format_filters.get(output_format, "MP4文件 (*.mp4)")
            all_filters = ";;".join(format_filters.values()) + ";;所有文件 (*)"

            # 选择输出文件
            output_path, _ = QFileDialog.getSaveFileName(
                self, "保存视频", "",
                f"{filter_str};;{all_filters}"
            )

            if not output_path:
                return False

            # 确保文件扩展名正确
            if not output_path.lower().endswith(f'.{output_format.lower()}'):
                output_path += f'.{output_format.lower()}'

            # 获取FFmpeg路径
            ffmpeg_path = self._get_ffmpeg_path()
            if not ffmpeg_path:
                QMessageBox.critical(self, "错误", "未找到FFmpeg，无法导出视频")
                return False

            # 获取导出参数
            fps = getattr(self, 'fps', 30.0)
            start_frame = getattr(self, 'cropped_start_frames', 0)
            end_frame = self.total_frames - getattr(self, 'cropped_end_frames', 0)
            encoder = export_settings.get('encoder', 'libx264')

            # 使用基础封面图像或合成封面
            cover_image = None
            if hasattr(self, '_cover_image') and self._cover_image:
                cover_image = self._cover_image
            elif hasattr(self, 'base_image') and self.base_image:
                cover_image = self.base_image

            # 创建导出线程
            from .workers.export_worker import VideoExportWorker
            self.export_thread = VideoExportWorker(
                video_path=self.video_path,
                output_path=output_path,
                cover_image=cover_image,
                fps=fps,
                start_frame=start_frame,
                end_frame=end_frame,
                ffmpeg_path=ffmpeg_path,
                export_settings=export_settings,
                encoder=encoder
            )

            # 连接信号
            self.export_thread.progress_updated.connect(self._update_export_progress)
            self.export_thread.export_finished.connect(self._on_export_finished)

            # 禁用导出按钮
            if hasattr(self, 'export_btn'):
                self.export_btn.setEnabled(False)
                self.export_btn.setText("导出中...")

            # 启动导出
            self.export_thread.start()
            self.logger.info(f"开始导出视频: {output_path}")

            return True

        except Exception as e:
            self.logger.error(f"导出视频失败: {e}")
            QMessageBox.critical(self, "错误", f"启动导出失败: {str(e)}")

            # 重新启用导出按钮
            if hasattr(self, 'export_btn'):
                self.export_btn.setEnabled(True)
                self.export_btn.setText("导出视频")

            return False

    def _get_export_settings(self):
        """获取导出设置 - 与原版完全一致"""
        try:
            # 默认导出设置
            default_settings = {
                'output_format': 'MP4',
                'encoder': 'libx264',
                'width': getattr(self, 'video_width', 1440),
                'height': getattr(self, 'video_height', 2560),
                'fps': getattr(self, 'fps', 30.0),
                'quality': '高质量',
                'bitrate': 8000
            }

            # 如果有导出设置模块，使用其设置
            if hasattr(self, 'export_settings') and self.export_settings:
                default_settings.update(self.export_settings)

            return default_settings

        except Exception as e:
            self.logger.error(f"获取导出设置失败: {e}")
            return {
                'output_format': 'MP4',
                'encoder': 'libx264',
                'width': 1440,
                'height': 2560,
                'fps': 30.0,
                'quality': '高质量',
                'bitrate': 8000
            }

    def _get_ffmpeg_path(self):
        """获取FFmpeg路径 - 增强版本，支持更多路径检测"""
        try:
            import shutil
            import os

            # 首先检查系统PATH中的ffmpeg
            ffmpeg_path = shutil.which('ffmpeg')
            if ffmpeg_path:
                self.logger.info(f"在系统PATH中找到FFmpeg: {ffmpeg_path}")
                return ffmpeg_path

            # 获取当前工作目录和项目根目录
            current_dir = os.getcwd()
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

            # 检查常见安装位置
            common_paths = [
                # 标准安装位置
                r"C:\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
                r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",

                # 项目相关路径
                os.path.join(project_root, "ffmpeg", "bin", "ffmpeg.exe"),
                os.path.join(project_root, "bin", "ffmpeg.exe"),
                os.path.join(current_dir, "ffmpeg", "bin", "ffmpeg.exe"),
                os.path.join(current_dir, "bin", "ffmpeg.exe"),

                # 相对路径
                "./ffmpeg/bin/ffmpeg.exe",
                "./bin/ffmpeg.exe",
                "./ffmpeg.exe",
                "ffmpeg.exe",

                # 用户目录
                os.path.join(os.path.expanduser("~"), "ffmpeg", "bin", "ffmpeg.exe"),
                os.path.join(os.path.expanduser("~"), "bin", "ffmpeg.exe"),

                # 常见的便携版路径
                r"D:\ffmpeg\bin\ffmpeg.exe",
                r"E:\ffmpeg\bin\ffmpeg.exe",
                r"F:\ffmpeg\bin\ffmpeg.exe"
            ]

            self.logger.info(f"开始搜索FFmpeg，检查 {len(common_paths)} 个可能的路径...")

            for i, path in enumerate(common_paths, 1):
                self.logger.debug(f"检查路径 {i}/{len(common_paths)}: {path}")
                if os.path.exists(path):
                    self.logger.info(f"找到FFmpeg: {path}")
                    return path

            # 如果还没找到，尝试在当前目录及其子目录中搜索
            self.logger.info("在标准路径中未找到FFmpeg，开始深度搜索...")

            search_dirs = [current_dir, project_root]
            for search_dir in search_dirs:
                if os.path.exists(search_dir):
                    for root, dirs, files in os.walk(search_dir):
                        # 限制搜索深度，避免搜索太深
                        level = root.replace(search_dir, '').count(os.sep)
                        if level < 3:  # 最多搜索3层深度
                            if 'ffmpeg.exe' in files:
                                found_path = os.path.join(root, 'ffmpeg.exe')
                                self.logger.info(f"深度搜索找到FFmpeg: {found_path}")
                                return found_path

            self.logger.warning("未找到FFmpeg，请确保FFmpeg已安装并在以下位置之一:")
            for path in common_paths[:10]:  # 只显示前10个主要路径
                self.logger.warning(f"  - {path}")

            return None

        except Exception as e:
            self.logger.error(f"获取FFmpeg路径失败: {e}")
            return None

    def _update_export_progress(self, progress: int):
        """更新导出进度 - 与原版完全一致"""
        try:
            # 显示并更新进度条
            if hasattr(self, 'export_progress'):
                self.export_progress.setVisible(True)
                self.export_progress.setValue(progress)
            self.logger.info(f"导出进度: {progress}%")
        except Exception as e:
            self.logger.error(f"更新导出进度失败: {e}")

    def _on_export_finished(self, output_path: str, success: bool, error_msg: str):
        """导出完成处理 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            # 重新启用导出按钮
            if hasattr(self, 'export_btn'):
                self.export_btn.setEnabled(True)
                self.export_btn.setText("导出视频")

            # 隐藏进度条
            if hasattr(self, 'export_progress'):
                self.export_progress.setVisible(False)
                self.export_progress.setValue(0)

            if success:
                self.logger.info(f"导出成功: {output_path}")
                QMessageBox.information(self, "导出完成", f"视频已成功导出到:\n{output_path}")
            else:
                self.logger.error(f"导出失败: {error_msg}")
                QMessageBox.critical(self, "导出失败", f"导出视频时出错:\n{error_msg}")

        except Exception as e:
            self.logger.error(f"导出完成处理失败: {e}")

    # ========================================================================
    # 播放控制方法 - 与原模块完全一致
    # ========================================================================

    def toggle_playback(self):
        """切换播放/暂停状态 - 与原模块完全一致"""
        try:
            if not self.media_player or self.media_player.source().isEmpty():
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "警告", "请先加载视频文件")
                return

            from PyQt6.QtMultimedia import QMediaPlayer

            if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                # 当前正在播放，暂停
                self.media_player.pause()
                if hasattr(self, 'play_pause_btn') and self.play_pause_btn:
                    self.play_pause_btn.setText("播放")
            else:
                # 当前暂停或停止，开始播放
                self.media_player.play()
                if hasattr(self, 'play_pause_btn') and self.play_pause_btn:
                    self.play_pause_btn.setText("暂停")

        except Exception as e:
            self.logger.error(f"播放控制错误: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"播放控制失败: {str(e)}")

    def switch_to_play_mode(self):
        """切换到播放模式 - 与原模块完全一致"""
        if hasattr(self, 'play_mode_btn') and self.play_mode_btn and self.play_mode_btn.isChecked():
            # 确保截图模式按钮取消选中
            if hasattr(self, 'snapshot_mode_btn') and self.snapshot_mode_btn:
                self.snapshot_mode_btn.setChecked(False)

            # 切换到播放模式预览
            if hasattr(self, 'preview_stack') and self.preview_stack:
                self.preview_stack.setCurrentIndex(1)
            if hasattr(self, 'control_stack') and self.control_stack:
                self.control_stack.setCurrentIndex(0)  # 显示播放控制面板

            # 加载视频到播放器
            if hasattr(self, '_video_path') and self._video_path:
                self.load_media_player(self._video_path)

    def switch_to_snapshot_mode(self):
        """切换到截图模式 - 与原模块完全一致"""
        if hasattr(self, 'snapshot_mode_btn') and self.snapshot_mode_btn and self.snapshot_mode_btn.isChecked():
            # 确保播放模式按钮取消选中
            if hasattr(self, 'play_mode_btn') and self.play_mode_btn:
                self.play_mode_btn.setChecked(False)

            # 切换到截图模式预览
            if hasattr(self, 'preview_stack') and self.preview_stack:
                self.preview_stack.setCurrentIndex(0)
            if hasattr(self, 'control_stack') and self.control_stack:
                self.control_stack.setCurrentIndex(1)  # 显示截图控制面板

    def load_media_player(self, file_path: str):
        """加载媒体播放器 - 与原版完全一致"""
        try:
            if self.media_player and file_path:
                from PyQt6.QtCore import QUrl
                url = QUrl.fromLocalFile(file_path)
                self.media_player.setSource(url)

                # 设置视频输出到video_widget
                if hasattr(self, 'video_widget') and self.video_widget:
                    self.media_player.setVideoOutput(self.video_widget)

                # 更新播放范围
                if hasattr(self, 'cropped_start_frames') and hasattr(self, 'cropped_end_frames'):
                    self.update_media_player_range()

                # 设置到第一帧位置（避免黑屏）
                if hasattr(self, 'fps') and self.fps > 0:
                    # 计算第一帧的时间（毫秒）
                    start_frame = getattr(self, 'cropped_start_frames', 0)
                    first_frame_time_ms = int((start_frame / self.fps) * 1000)

                    # 设置播放器位置到第一帧
                    self.media_player.setPosition(first_frame_time_ms)

                self.logger.info(f"媒体播放器加载视频: {file_path}")

        except Exception as e:
            self.logger.error(f"加载媒体播放器失败: {e}")

    # ========================================================================
    # 导出和批量处理方法
    # ========================================================================

    def show_export_settings(self):
        """显示导出设置对话框"""
        try:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self, "导出设置", "导出设置功能正在开发中...")
        except Exception as e:
            self.logger.error(f"显示导出设置失败: {e}")

    def reset_batch_processing(self):
        """重置批量处理记录 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                self,
                "确认重置",
                "是否要清空所有处理记录？\n这将允许重新处理之前已处理的文件。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 清空处理记录
                if hasattr(self, 'processed_files'):
                    self.processed_files.clear()
                if hasattr(self, 'failed_files'):
                    self.failed_files.clear()
                if hasattr(self, 'processing_queue'):
                    self.processing_queue.clear()
                if hasattr(self, 'retry_count'):
                    self.retry_count.clear()

                # 重置状态
                if hasattr(self, 'current_processing'):
                    self.current_processing = False
                if hasattr(self, 'scan_in_progress'):
                    self.scan_in_progress = False

                # 清空日志
                if hasattr(self, 'batch_log_display') and self.batch_log_display:
                    self.batch_log_display.clear()

                # 重置批量处理器状态
                if hasattr(self, 'batch_processor') and self.batch_processor:
                    self.batch_processor.reset_processing()

                # 更新UI状态
                if hasattr(self, 'batch_status_label') and self.batch_status_label:
                    self.batch_status_label.setText("状态: 已重置")

                self.add_batch_log("🔄 处理记录已重置", "success")
                self.add_batch_log("现在可以重新处理之前的文件", "info")

                QMessageBox.information(self, "重置完成", "处理记录已清空，可以重新处理文件了。")

        except Exception as e:
            self.logger.error(f"重置批量处理失败: {e}")
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"重置失败: {str(e)}")

    def toggle_batch_mode(self, checked):
        """切换批量处理模式 - 与原版完全一致"""
        try:
            if hasattr(self, 'batch_settings_widget') and self.batch_settings_widget:
                if checked:
                    # 显示批量处理设置面板
                    self.batch_settings_widget.show()
                    self.batch_settings_shown = True
                    # 更新箭头为向下箭头，表示面板已展开
                    if hasattr(self, 'batch_mode_checkbox'):
                        self.batch_mode_checkbox.setText("▼ 启用批量处理模式")
                    self.add_batch_log("📋 批量处理面板已显示", "info")
                else:
                    # 隐藏批量处理设置面板
                    self.batch_settings_widget.hide()
                    self.batch_settings_shown = False
                    # 更新箭头为向上箭头，表示面板已收起
                    if hasattr(self, 'batch_mode_checkbox'):
                        self.batch_mode_checkbox.setText("▲ 启用批量处理模式")
                    # 如果关闭批量模式，也停止批量处理
                    if hasattr(self, 'batch_enable_checkbox') and self.batch_enable_checkbox:
                        self.batch_enable_checkbox.setChecked(False)
                    # 同时隐藏日志面板
                    if hasattr(self, 'batch_log_display'):
                        self.batch_log_display.setVisible(False)
                        self.batch_log_visible = False
                    self.add_batch_log("📋 批量处理面板已隐藏", "info")

        except Exception as e:
            self.logger.error(f"切换批量模式失败: {e}")

    def toggle_batch_processing(self, checked):
        """切换批量处理启动状态 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QMessageBox
            from PyQt6.QtCore import QTimer

            if checked:
                # 检查必要条件
                if not hasattr(self, 'watch_folder') or not self.watch_folder:
                    QMessageBox.warning(self, "警告", "请先设置监控文件夹")
                    if hasattr(self, 'batch_enable_checkbox'):
                        self.batch_enable_checkbox.setChecked(False)
                    return

                if not hasattr(self, 'output_folder') or not self.output_folder:
                    QMessageBox.warning(self, "警告", "请先设置输出文件夹")
                    if hasattr(self, 'batch_enable_checkbox'):
                        self.batch_enable_checkbox.setChecked(False)
                    return

                # 启动批量处理
                self.batch_mode_enabled = True
                self._setup_file_watcher()
                if hasattr(self, 'batch_status_label'):
                    self.batch_status_label.setText("状态: 监控已启动")
                self.add_batch_log("🚀 批量处理已启动", "success")

                # 扫描现有文件
                QTimer.singleShot(1000, self._scan_existing_files)
            else:
                # 停止批量处理
                self._stop_batch_processing()

        except Exception as e:
            self.logger.error(f"切换批量处理状态失败: {e}")
            QMessageBox.critical(self, "错误", f"切换批量处理失败: {str(e)}")
            if hasattr(self, 'batch_enable_checkbox'):
                self.batch_enable_checkbox.setChecked(False)

    def start_batch_processing(self):
        """开始批量处理 - 与原版完全一致"""
        try:
            from PyQt6.QtWidgets import QMessageBox
            import os

            if not hasattr(self, 'watch_folder') or not self.watch_folder:
                QMessageBox.warning(self, "警告", "请先设置监控文件夹")
                return False

            if not hasattr(self, 'output_folder') or not self.output_folder:
                QMessageBox.warning(self, "警告", "请先设置输出文件夹")
                return False

            if not os.path.exists(self.watch_folder):
                QMessageBox.warning(self, "警告", f"监控文件夹不存在: {self.watch_folder}")
                return False

            if not os.path.exists(self.output_folder):
                try:
                    os.makedirs(self.output_folder, exist_ok=True)
                except Exception as e:
                    QMessageBox.warning(self, "警告", f"无法创建输出文件夹: {e}")
                    return False

            # 初始化批量处理状态
            self.batch_mode_enabled = True
            self.current_processing = False
            self.processing_queue = []
            self.processed_files = set()
            self.failed_files = set()
            self.retry_count = {}

            # 设置文件监控
            self._setup_file_watcher()

            # 更新状态
            if hasattr(self, 'batch_status_label'):
                self.batch_status_label.setText("状态: 监控已启动")

            self.add_batch_log("🚀 批量处理已启动", "success")
            self.add_batch_log(f"📁 监控文件夹: {self.watch_folder}", "info")
            self.add_batch_log(f"📁 输出文件夹: {self.output_folder}", "info")

            self.logger.info("批量处理已启动")
            return True

        except Exception as e:
            self.logger.error(f"开始批量处理失败: {e}")
            self.add_batch_log(f"❌ 启动失败: {str(e)}", "error")
            return False

    def stop_batch_processing(self):
        """停止批量处理 - 与原版完全一致"""
        try:
            self._stop_batch_processing()
            return True

        except Exception as e:
            self.logger.error(f"停止批量处理失败: {e}")
            return False

    def _stop_batch_processing(self):
        """内部停止批量处理方法 - 与原版完全一致"""
        try:
            # 停止文件监控
            if hasattr(self, 'file_watcher') and self.file_watcher:
                self.file_watcher.removePaths(self.file_watcher.directories())
                self.file_watcher = None

            # 停止当前导出
            if hasattr(self, 'export_thread') and self.export_thread:
                if self.export_thread.isRunning():
                    self.export_thread.terminate()
                    self.export_thread.wait(3000)  # 等待3秒

            # 重置状态
            self.batch_mode_enabled = False
            self.current_processing = False

            # 更新UI
            if hasattr(self, 'batch_status_label'):
                self.batch_status_label.setText("状态: 已停止")

            if hasattr(self, 'batch_enable_checkbox'):
                self.batch_enable_checkbox.setChecked(False)

            self.add_batch_log("⏹️ 批量处理已停止", "warning")
            self.logger.info("批量处理已停止")

        except Exception as e:
            self.logger.error(f"停止批量处理失败: {e}")
            self.add_batch_log(f"❌ 停止失败: {str(e)}", "error")

    def _setup_file_watcher(self):
        """设置文件监控 - 与原版完全一致"""
        try:
            from PyQt6.QtCore import QFileSystemWatcher

            if hasattr(self, 'file_watcher') and self.file_watcher:
                self.file_watcher.removePaths(self.file_watcher.directories())

            self.file_watcher = QFileSystemWatcher()
            self.file_watcher.addPath(self.watch_folder)
            self.file_watcher.directoryChanged.connect(self._on_directory_changed)

            self.logger.info(f"文件监控已设置: {self.watch_folder}")

        except Exception as e:
            self.logger.error(f"设置文件监控失败: {e}")

    def _on_directory_changed(self, path: str):
        """目录变化处理 - 与原版完全一致"""
        try:
            from PyQt6.QtCore import QTimer

            if not self.batch_mode_enabled:
                return

            if hasattr(self, 'scan_in_progress') and self.scan_in_progress:
                return

            self.scan_in_progress = True
            self.add_batch_log("📁 检测到文件夹变化，准备扫描...", "info")

            # 延迟扫描，避免频繁触发
            QTimer.singleShot(2000, self._scan_new_files)

        except Exception as e:
            self.logger.error(f"目录变化处理失败: {e}")
            self.scan_in_progress = False

    def _scan_existing_files(self):
        """扫描现有文件 - 与原版完全一致"""
        try:
            import os
            from .utils.constants import CoverEditConstants

            if not self.watch_folder or not os.path.exists(self.watch_folder):
                return

            potential_files = []

            # 扫描支持的视频文件
            for filename in os.listdir(self.watch_folder):
                if any(filename.lower().endswith(ext) for ext in CoverEditConstants.SUPPORTED_VIDEO_FORMATS):
                    file_path = os.path.join(self.watch_folder, filename)

                    # 跳过已处理的文件
                    if file_path in self.processed_files:
                        continue

                    # 跳过输出文件
                    if "_processed" in filename or "_cover_" in filename:
                        continue

                    potential_files.append(file_path)

            if potential_files:
                self.add_batch_log(f"📋 发现 {len(potential_files)} 个待处理文件", "info")
                for file_path in potential_files:
                    if file_path not in self.processing_queue:
                        self.processing_queue.append(file_path)

                # 开始处理
                if not self.current_processing:
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(1000, self._process_next_file)
            else:
                self.add_batch_log("📂 没有发现待处理文件", "info")

        except Exception as e:
            self.logger.error(f"扫描现有文件失败: {e}")
            self.add_batch_log(f"❌ 扫描失败: {str(e)}", "error")

    def _scan_new_files(self):
        """扫描新文件 - 与原版完全一致"""
        try:
            import os
            from .utils.constants import CoverEditConstants

            if not self.watch_folder or not os.path.exists(self.watch_folder):
                self.scan_in_progress = False
                return

            new_files = []

            # 扫描支持的视频文件
            for filename in os.listdir(self.watch_folder):
                if any(filename.lower().endswith(ext) for ext in CoverEditConstants.SUPPORTED_VIDEO_FORMATS):
                    file_path = os.path.join(self.watch_folder, filename)

                    # 只处理新文件
                    if (file_path not in self.processed_files and
                        file_path not in self.processing_queue and
                        "_processed" not in filename and
                        "_cover_" not in filename):
                        new_files.append(file_path)

            if new_files:
                self.add_batch_log(f"📋 发现 {len(new_files)} 个新文件", "info")
                for file_path in new_files:
                    self.processing_queue.append(file_path)

                # 开始处理
                if not self.current_processing:
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(500, self._process_next_file)

            self.scan_in_progress = False

        except Exception as e:
            self.logger.error(f"扫描新文件失败: {e}")
            self.add_batch_log(f"❌ 新文件扫描失败: {str(e)}", "error")
            self.scan_in_progress = False

    def add_batch_log(self, message: str, level: str = "info"):
        """添加批量处理日志 - 与原版完全一致"""
        try:
            if not hasattr(self, 'batch_log_display') or not self.batch_log_display:
                return

            import datetime

            timestamp = datetime.datetime.now().strftime("%H:%M:%S")

            # 颜色映射
            color_map = {
                "info": "#ABB2BF",
                "success": "#98C379",
                "warning": "#E5C07B",
                "error": "#E06C75",
                "processing": "#61AFEF"
            }

            color = color_map.get(level, "#ABB2BF")
            formatted_message = f'<span style="color: {color}">[{timestamp}] {message}</span>'

            # 添加到日志显示
            self.batch_log_display.append(formatted_message)

            # 自动滚动到底部
            scrollbar = self.batch_log_display.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # 限制日志行数，避免内存占用过多
            document = self.batch_log_display.document()
            if document.blockCount() > 200:  # 最多保留200行
                cursor = self.batch_log_display.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                cursor.select(cursor.SelectionType.BlockUnderCursor)
                cursor.removeSelectedText()

        except Exception as e:
            self.logger.error(f"添加批量日志失败: {e}")

    def _process_next_file(self):
        """处理下一个文件 - 与原版完全一致"""
        try:
            if not self.batch_mode_enabled or self.current_processing:
                return

            if not self.processing_queue:
                self.add_batch_log("📋 处理队列为空", "info")
                return

            # 获取下一个文件
            file_path = self.processing_queue.pop(0)
            self.current_processing = True

            self.add_batch_log(f"🔄 开始处理: {os.path.basename(file_path)}", "processing")

            # 加载视频
            if self.load_video(file_path):
                # 自动匹配封面
                self.load_matching_cover_by_aspect_ratio()

                # 生成输出路径
                output_path = self._generate_batch_output_path(file_path)
                if output_path:
                    # 开始导出
                    self._start_batch_export(output_path, file_path)
                else:
                    self.add_batch_log(f"❌ 无法生成输出路径: {os.path.basename(file_path)}", "error")
                    self.current_processing = False
                    self._schedule_next_processing()
            else:
                self.add_batch_log(f"❌ 加载视频失败: {os.path.basename(file_path)}", "error")
                self.failed_files.add(file_path)
                self.current_processing = False
                self._schedule_next_processing()

        except Exception as e:
            self.logger.error(f"处理下一个文件失败: {e}")
            self.add_batch_log(f"❌ 处理失败: {str(e)}", "error")
            self.current_processing = False
            self._schedule_next_processing()

    def _generate_batch_output_path(self, input_path: str) -> str:
        """生成批量输出路径 - 与原版完全一致"""
        try:
            import os

            filename = os.path.basename(input_path)
            name, ext = os.path.splitext(filename)

            # 生成输出文件名
            output_filename = f"{name}_processed{ext}"
            output_path = os.path.join(self.output_folder, output_filename)

            # 如果文件已存在，添加数字后缀
            counter = 1
            while os.path.exists(output_path):
                output_filename = f"{name}_processed_{counter}{ext}"
                output_path = os.path.join(self.output_folder, output_filename)
                counter += 1

            return output_path

        except Exception as e:
            self.logger.error(f"生成输出路径失败: {e}")
            return None

    def _start_batch_export(self, output_path: str, original_path: str):
        """开始批量导出 - 与原版完全一致"""
        try:
            # 获取导出参数
            export_settings = self._get_export_settings()
            fps = getattr(self, 'fps', 30.0)
            start_frame = getattr(self, 'cropped_start_frames', 0)
            end_frame = self.total_frames - getattr(self, 'cropped_end_frames', 0)
            encoder = export_settings.get('encoder', 'libx264')

            # 获取FFmpeg路径
            ffmpeg_path = self._get_ffmpeg_path()
            if not ffmpeg_path:
                self.add_batch_log(f"❌ 未找到FFmpeg: {os.path.basename(original_path)}", "error")
                self.failed_files.add(original_path)
                self.current_processing = False
                self._schedule_next_processing()
                return

            # 使用封面图像
            cover_image = None
            if hasattr(self, '_cover_image') and self._cover_image:
                cover_image = self._cover_image
            elif hasattr(self, 'base_image') and self.base_image:
                cover_image = self.base_image

            # 创建导出线程
            from .workers.export_worker import VideoExportWorker
            self.export_thread = VideoExportWorker(
                video_path=self.video_path,
                output_path=output_path,
                cover_image=cover_image,
                fps=fps,
                start_frame=start_frame,
                end_frame=end_frame,
                ffmpeg_path=ffmpeg_path,
                export_settings=export_settings,
                encoder=encoder
            )

            # 连接信号
            self.export_thread.progress_updated.connect(self._update_export_progress)
            self.export_thread.export_finished.connect(
                lambda output_path, success, error_msg: self._on_batch_export_finished(
                    output_path, success, error_msg, original_path
                )
            )

            # 启动导出
            self.export_thread.start()

        except Exception as e:
            self.logger.error(f"启动批量导出失败: {e}")
            self.add_batch_log(f"❌ 启动导出失败: {str(e)}", "error")
            self.failed_files.add(original_path)
            self.current_processing = False
            self._schedule_next_processing()

    def _on_batch_export_finished(self, output_path: str, success: bool, error_msg: str, original_path: str):
        """批量导出完成处理 - 与原版完全一致"""
        try:
            self.current_processing = False

            if success:
                self.processed_files.add(original_path)
                self.add_batch_log(f"✅ 处理成功: {os.path.basename(output_path)}", "success")

                # 隐藏进度条
                if hasattr(self, 'export_progress'):
                    self.export_progress.setVisible(False)
                    self.export_progress.setValue(0)
            else:
                self.failed_files.add(original_path)
                self.add_batch_log(f"❌ 处理失败: {os.path.basename(original_path)} - {error_msg}", "error")

            # 处理下一个文件
            self._schedule_next_processing()

        except Exception as e:
            self.logger.error(f"批量导出完成处理失败: {e}")
            self.add_batch_log(f"❌ 处理完成回调失败: {str(e)}", "error")
            self.current_processing = False
            self._schedule_next_processing()

    def _schedule_next_processing(self):
        """安排下一个文件的处理 - 与原版完全一致"""
        try:
            from PyQt6.QtCore import QTimer

            if self.processing_queue:
                queue_size = len(self.processing_queue)
                self.add_batch_log(f"📋 队列剩余 {queue_size} 个文件", "info")

                # 根据队列大小调整延迟
                delay = 1000 if queue_size > 10 else 2000
                QTimer.singleShot(delay, self._process_next_file)
            else:
                if hasattr(self, 'batch_status_label'):
                    self.batch_status_label.setText("状态: 监控中...")
                self.add_batch_log("✨ 队列处理完成，继续监控新文件", "success")

        except Exception as e:
            self.logger.error(f"安排下一个处理失败: {e}")
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(3000, self._process_next_file)



    # 文字编辑相关方法已移除，使用模块化架构委托给text_handler处理

    # on_frame_slider_changed 方法已在上面定义，删除重复定义
    
    # ========================================================================
    # 属性访问器 - 提供对子模块状态的访问
    # ========================================================================

    @property
    def video_path(self):
        """当前视频路径"""
        # 🚀 修复：优先从video_handler获取，回退到内部属性
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'video_path'):
            return getattr(self.video_handler, 'video_path', self._video_path)
        return getattr(self, '_video_path', "")

    @video_path.setter
    def video_path(self, value):
        """设置视频路径"""
        self._video_path = value
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'video_path'):
            self.video_handler.video_path = value

    @property
    def current_frame(self):
        """当前帧号"""
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'current_frame'):
            return getattr(self.video_handler, 'current_frame', self._current_frame)
        return getattr(self, '_current_frame', 0)

    @current_frame.setter
    def current_frame(self, value):
        """设置当前帧号"""
        self._current_frame = value
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'current_frame'):
            self.video_handler.current_frame = value

    @property
    def total_frames(self):
        """总帧数"""
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'total_frames'):
            return getattr(self.video_handler, 'total_frames', self._total_frames)
        return getattr(self, '_total_frames', 0)

    @total_frames.setter
    def total_frames(self, value):
        """设置总帧数"""
        self._total_frames = value
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'total_frames'):
            self.video_handler.total_frames = value

    @property
    def fps(self):
        """帧率"""
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'fps'):
            return getattr(self.video_handler, 'fps', self._fps)
        return getattr(self, '_fps', 30.0)

    @fps.setter
    def fps(self, value):
        """设置帧率"""
        self._fps = value
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'fps'):
            self.video_handler.fps = value

    @property
    def video_duration(self):
        """视频时长"""
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'video_duration'):
            return getattr(self.video_handler, 'video_duration', self._video_duration)
        return getattr(self, '_video_duration', 0.0)

    @video_duration.setter
    def video_duration(self, value):
        """设置视频时长"""
        self._video_duration = value
        if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'video_duration'):
            self.video_handler.video_duration = value

    @property
    def cover_image(self):
        """封面图像"""
        if hasattr(self, 'image_handler') and hasattr(self.image_handler, 'cover_image'):
            return getattr(self.image_handler, 'cover_image', self._cover_image)
        return getattr(self, '_cover_image', None)

    @cover_image.setter
    def cover_image(self, value):
        """设置封面图像"""
        self._cover_image = value
        if hasattr(self, 'image_handler') and hasattr(self.image_handler, 'cover_image'):
            self.image_handler.cover_image = value

    @property
    def is_batch_processing(self):
        """是否正在批量处理"""
        if hasattr(self, 'batch_processor') and hasattr(self.batch_processor, 'is_processing'):
            return getattr(self.batch_processor, 'is_processing', False)
        return False
    
    # ========================================================================
    # 兼容性方法 - 保持与原模块的兼容性
    # ========================================================================

    # add_batch_log 方法已在上面定义，删除重复定义

    # switch_to_play_mode 和 switch_to_snapshot_mode 方法已在上面定义，删除重复定义

    def toggle_batch_log(self):
        """切换批量日志显示"""
        if hasattr(self.batch_processor, 'toggle_batch_log'):
            return self.batch_processor.toggle_batch_log()
        else:
            self.logger.info("切换批量日志显示")

    def toggle_batch_log_display(self):
        """切换批量处理日志显示/隐藏（Ctrl+1快捷键）- 与原版完全一致"""
        try:
            from PyQt6.QtCore import QTimer

            self.logger.info("📋 Ctrl+1 快捷键被触发")

            # 检查批量处理面板是否存在
            if not hasattr(self, 'batch_log_display'):
                self.logger.info("📋 批量处理日志面板不存在，尝试创建...")
                # 如果日志面板不存在，可能需要先启用批量处理
                if hasattr(self, 'batch_mode_checkbox'):
                    self.batch_mode_checkbox.setChecked(True)
                    self.toggle_batch_mode(True)
                return

            # 检查批量处理面板是否显示
            if not hasattr(self, 'batch_settings_shown') or not self.batch_settings_shown:
                self.logger.info("📋 批量处理面板未显示，自动启用批量处理模式")
                # 自动启用批量处理模式
                if hasattr(self, 'batch_mode_checkbox'):
                    self.batch_mode_checkbox.setChecked(True)
                    self.toggle_batch_mode(True)
                # 延迟切换日志显示
                QTimer.singleShot(100, self.toggle_batch_log_display)
                return

            # 切换日志显示状态
            self.batch_log_visible = not self.batch_log_visible
            self.batch_log_display.setVisible(self.batch_log_visible)

            if self.batch_log_visible:
                self.logger.info("📋 批量处理日志已显示")
                self.add_batch_log("📋 日志面板已显示 (Ctrl+1 隐藏)", "info")
            else:
                self.logger.info("📋 批量处理日志已隐藏")

        except Exception as e:
            self.logger.error(f"切换日志显示失败: {e}")
            import traceback
            traceback.print_exc()

    def snapshot_current_frame(self):
        """截取当前帧（兼容性方法）"""
        return self.capture_current_frame()



    def process_next_file(self):
        """处理下一个文件（兼容性方法）"""
        if hasattr(self.batch_processor, 'process_next_file'):
            return self.batch_processor.process_next_file()
        else:
            return False

    def update_crop_settings(self):
        """更新裁剪设置（兼容性方法）"""
        if hasattr(self.video_handler, 'update_crop_settings'):
            return self.video_handler.update_crop_settings()

    def update_duration_label(self):
        """更新时长标签（兼容性方法）"""
        if hasattr(self.video_handler, 'update_duration_label'):
            return self.video_handler.update_duration_label()

    # on_frame_slider_changed 方法已在上面定义，删除重复定义

    def resizeEvent(self, event):
        """重写调整大小事件（兼容性方法）"""
        super().resizeEvent(event)
        if hasattr(self.video_handler, 'on_resize'):
            self.video_handler.on_resize(event)

    # toggle_batch_mode, browse_watch_folder, browse_output_folder 方法已在上面定义，删除重复定义

    def load_video_file(self, file_path: str):
        """加载视频文件（批量处理接口）"""
        return self.load_video(file_path)
    
    # ========================================================================
    # 生命周期管理
    # ========================================================================
    
    def closeEvent(self, event):
        """模块关闭时清理资源"""
        try:
            self.logger.info("开始清理模块资源...")

            # 停止所有处理
            if hasattr(self, 'batch_processor') and hasattr(self.batch_processor, 'stop_processing'):
                self.batch_processor.stop_processing()
            if hasattr(self, 'export_coordinator') and hasattr(self.export_coordinator, 'cancel_export'):
                self.export_coordinator.cancel_export()

            # 清理资源
            if hasattr(self, 'video_handler') and hasattr(self.video_handler, 'cleanup'):
                self.video_handler.cleanup()
            if hasattr(self, 'image_handler') and hasattr(self.image_handler, 'cleanup'):
                self.image_handler.cleanup()

            self.logger.info("模块资源清理完成")
            event.accept()

        except Exception as e:
            self.logger.error(f"模块关闭清理失败: {e}")
            event.accept()
    
    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, 'logger'):
                self.logger.info("封面编辑模块析构")
        except:
            pass


# 为了保持兼容性，创建别名
CoverEditModule = CoverEditOptimizedModule
