#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cover Edit 模块日志工具
统一的日志管理，替换原代码中的print语句
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional
from pathlib import Path

class CoverEditLogger:
    """封面编辑模块专用日志器"""
    
    _loggers = {}  # 类级别的日志器缓存
    
    def __init__(self, name: str, log_level: int = logging.INFO):
        """
        初始化日志器
        
        Args:
            name: 日志器名称，通常是模块名
            log_level: 日志级别
        """
        self.name = name
        self.logger_name = f"CoverEdit.{name}"
        
        # 如果已经存在相同名称的日志器，直接使用
        if self.logger_name in self._loggers:
            self.logger = self._loggers[self.logger_name]
        else:
            self.logger = self._create_logger(log_level)
            self._loggers[self.logger_name] = self.logger
    
    def _create_logger(self, log_level: int) -> logging.Logger:
        """创建并配置日志器"""
        logger = logging.getLogger(self.logger_name)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        logger.setLevel(log_level)
        
        # 创建文件处理器
        file_handler = self._create_file_handler()
        if file_handler:
            logger.addHandler(file_handler)
        
        # 创建控制台处理器
        console_handler = self._create_console_handler()
        if console_handler:
            logger.addHandler(console_handler)
        
        return logger
    
    def _create_file_handler(self) -> Optional[logging.FileHandler]:
        """创建文件处理器"""
        try:
            # 确保日志目录存在
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            # 创建日志文件名（按日期分割）
            today = datetime.now().strftime("%Y%m%d")
            log_file = log_dir / f"cover_edit_{today}.log"
            
            # 创建文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 设置详细的文件日志格式
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            
            return file_handler
            
        except Exception as e:
            print(f"创建文件日志处理器失败: {e}")
            return None
    
    def _create_console_handler(self) -> Optional[logging.StreamHandler]:
        """创建控制台处理器"""
        try:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            
            # 设置简洁的控制台日志格式
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(console_formatter)
            
            return console_handler
            
        except Exception as e:
            print(f"创建控制台日志处理器失败: {e}")
            return None
    
    def debug(self, msg: str, *args, **kwargs):
        """调试级别日志"""
        self.logger.debug(msg, *args, **kwargs)
    
    def info(self, msg: str, *args, **kwargs):
        """信息级别日志"""
        self.logger.info(msg, *args, **kwargs)
    
    def warning(self, msg: str, *args, **kwargs):
        """警告级别日志"""
        self.logger.warning(msg, *args, **kwargs)
    
    def error(self, msg: str, *args, **kwargs):
        """错误级别日志"""
        self.logger.error(msg, *args, **kwargs)
    
    def critical(self, msg: str, *args, **kwargs):
        """严重错误级别日志"""
        self.logger.critical(msg, *args, **kwargs)
    
    def exception(self, msg: str, *args, **kwargs):
        """异常日志（自动包含异常堆栈）"""
        self.logger.exception(msg, *args, **kwargs)

class PerformanceLogger:
    """性能监控日志器"""
    
    def __init__(self, logger: CoverEditLogger):
        self.logger = logger
        self.start_times = {}
    
    def start_timing(self, operation: str):
        """开始计时"""
        import time
        self.start_times[operation] = time.time()
        self.logger.debug(f"开始执行: {operation}")
    
    def end_timing(self, operation: str, threshold_ms: float = 100.0):
        """结束计时并记录"""
        import time
        
        if operation not in self.start_times:
            self.logger.warning(f"未找到操作 {operation} 的开始时间")
            return
        
        end_time = time.time()
        start_time = self.start_times.pop(operation)
        duration_ms = (end_time - start_time) * 1000
        
        if duration_ms > threshold_ms:
            self.logger.warning(f"操作 {operation} 耗时过长: {duration_ms:.2f}ms")
        else:
            self.logger.debug(f"操作 {operation} 完成: {duration_ms:.2f}ms")
        
        return duration_ms

class MemoryLogger:
    """内存使用监控日志器"""
    
    def __init__(self, logger: CoverEditLogger):
        self.logger = logger
        self.peak_memory = 0
    
    def log_memory_usage(self, operation: str = ""):
        """记录当前内存使用情况"""
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
            
            if memory_mb > self.peak_memory:
                self.peak_memory = memory_mb
            
            # 根据内存使用量选择日志级别
            if memory_mb > 1000:  # 超过1GB
                self.logger.critical(f"内存使用严重过高 {operation}: {memory_mb:.1f}MB (峰值: {self.peak_memory:.1f}MB)")
            elif memory_mb > 500:  # 超过500MB
                self.logger.warning(f"内存使用过高 {operation}: {memory_mb:.1f}MB (峰值: {self.peak_memory:.1f}MB)")
            else:
                self.logger.debug(f"内存使用 {operation}: {memory_mb:.1f}MB (峰值: {self.peak_memory:.1f}MB)")
            
            return memory_mb
            
        except ImportError:
            self.logger.warning("psutil未安装，无法监控内存使用")
            return None
        except Exception as e:
            self.logger.error(f"获取内存信息失败: {e}")
            return None

# ============================================================================
# 便捷函数
# ============================================================================

def get_logger(name: str, log_level: int = logging.INFO) -> CoverEditLogger:
    """获取日志器的便捷函数"""
    return CoverEditLogger(name, log_level)

def setup_global_logging(log_level: int = logging.INFO):
    """设置全局日志配置"""
    # 设置根日志器级别
    logging.getLogger().setLevel(log_level)
    
    # 禁用一些第三方库的详细日志
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    print(f"全局日志配置完成，日志级别: {logging.getLevelName(log_level)}")

# ============================================================================
# 日志装饰器
# ============================================================================

def log_function_call(logger: CoverEditLogger):
    """记录函数调用的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger.debug(f"调用函数: {func.__name__}")
            try:
                result = func(*args, **kwargs)
                logger.debug(f"函数 {func.__name__} 执行成功")
                return result
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        return wrapper
    return decorator

def log_performance(logger: CoverEditLogger, threshold_ms: float = 100.0):
    """记录函数性能的装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            perf_logger = PerformanceLogger(logger)
            perf_logger.start_timing(func.__name__)
            try:
                result = func(*args, **kwargs)
                perf_logger.end_timing(func.__name__, threshold_ms)
                return result
            except Exception as e:
                perf_logger.end_timing(func.__name__, threshold_ms)
                raise
        return wrapper
    return decorator

# ============================================================================
# 使用示例
# ============================================================================

if __name__ == "__main__":
    # 设置全局日志
    setup_global_logging(logging.DEBUG)
    
    # 创建模块日志器
    logger = get_logger("TestModule")
    
    # 测试各种日志级别
    logger.debug("这是调试信息")
    logger.info("这是普通信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    
    # 测试性能监控
    perf_logger = PerformanceLogger(logger)
    perf_logger.start_timing("测试操作")
    
    import time
    time.sleep(0.1)  # 模拟耗时操作
    
    perf_logger.end_timing("测试操作", 50.0)
    
    # 测试内存监控
    mem_logger = MemoryLogger(logger)
    mem_logger.log_memory_usage("测试内存监控")
