#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面构建器
负责构建主要的界面布局，确保与原模块完全一致
"""

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QSplitter
from PyQt6.QtCore import Qt

# 导入常量
from cover_edit.utils.constants import CoverEditConstants

class MainWidgetBuilder:
    """主界面构建器 - 确保与原模块布局完全一致"""
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
    
    def build_main_layout(self):
        """
        构建主界面布局 - 与原模块的_setup_ui()完全一致
        🔒 保证：与原模块的_setup_ui()方法产生完全相同的布局
        """
        try:
            self.logger.debug("开始构建主界面布局")

            # 🔒 创建主布局 - 与原模块完全一致
            main_layout = QHBoxLayout(self.parent)
            main_layout.setContentsMargins(10, 10, 10, 10)

            # 🔒 创建分割器 - 与原模块完全一致
            self.parent.splitter = self._create_splitter()

            # 🔒 创建左右面板 - 与原模块完全一致
            left_panel = self._create_left_panel()
            right_panel = self._create_right_panel()

            # 🔒 添加面板到分割器 - 与原模块完全一致
            self.parent.splitter.addWidget(left_panel)
            self.parent.splitter.addWidget(right_panel)

            # 🔒 将组件引用保存到父组件 - 确保外部可以访问
            self.parent.splitter = self.parent.splitter
            self.parent.left_panel = left_panel
            self.parent.right_panel = right_panel

            # 🔒 设置1:1分配 - 使用具体像素值 - 与原模块完全一致
            self.parent.splitter.setSizes([500, 500])  # 左右栏1:1分配

            main_layout.addWidget(self.parent.splitter)

            self.logger.debug("主界面布局构建完成")

        except Exception as e:
            self.logger.error(f"构建主界面布局失败: {e}")
            raise

    def _create_splitter(self) -> QSplitter:
        """创建分割器 - 与原模块完全一致"""
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setStyleSheet(CoverEditConstants.SPLITTER_STYLE)
        # 设置拉伸因子为相等
        splitter.setStretchFactor(0, 1)  # 左侧面板
        splitter.setStretchFactor(1, 1)  # 右侧面板
        return splitter

    def _create_left_panel(self) -> QWidget:
        """创建左侧面板 - 与原模块完全一致"""
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(5, 5, 5, 5)
        left_layout.setSpacing(10)

        # 保存引用供其他构建器使用
        self.parent.left_panel = left_panel
        self.parent.left_layout = left_layout

        return left_panel

    def _create_right_panel(self) -> QWidget:
        """创建右侧面板 - 与原模块完全一致"""
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(10)

        # 保存引用供其他构建器使用
        self.parent.right_panel = right_panel
        self.parent.right_layout = right_layout

        return right_panel



    def get_left_panel_container(self):
        """获取左侧面板容器"""
        return self.parent.splitter.widget(0) if hasattr(self.parent, 'splitter') else None
    
    def get_right_panel_container(self):
        """获取右侧面板容器"""
        return self.parent.splitter.widget(1) if hasattr(self.parent, 'splitter') else None
    
    def add_left_panel(self, widget):
        """添加左侧面板"""
        if hasattr(self.parent, 'splitter'):
            self.parent.splitter.addWidget(widget)
    
    def add_right_panel(self, widget):
        """添加右侧面板"""
        if hasattr(self.parent, 'splitter'):
            self.parent.splitter.addWidget(widget)
    
    def set_splitter_sizes(self, sizes):
        """设置分割器尺寸"""
        if hasattr(self.parent, 'splitter'):
            self.parent.splitter.setSizes(sizes)
    
    def get_splitter_sizes(self):
        """获取分割器尺寸"""
        if hasattr(self.parent, 'splitter'):
            return self.parent.splitter.sizes()
        return []
