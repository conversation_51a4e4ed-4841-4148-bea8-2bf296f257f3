#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图层管理器
负责管理所有图层相关的操作，从原模块迁移而来
"""

import os
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QPixmap, QPainter

# 导入常量和工具
from cover_edit.utils.constants import CoverEditConstants
from cover_edit.utils.decorators import error_handler, performance_monitor
from cover_edit.utils.resource_manager import register_pixmap

class CoverLayer:
    """
    封面图层类
    从原模块迁移而来
    """
    def __init__(self, pixmap: QPixmap, layer_type: str = "imported", 
                 source_info: str = "", x: int = 0, y: int = 0):
        self.id = 0
        self.pixmap = pixmap
        self.type = layer_type  # imported, text, frame, etc.
        self.source = source_info
        self.visible = True
        self.opacity = 1.0
        self.x = x
        self.y = y
        self.width = pixmap.width() if pixmap else 0
        self.height = pixmap.height() if pixmap else 0
        self.locked = False
        self.blend_mode = "normal"

class LayerManager:
    """
    图层管理器
    从原模块迁移而来，负责所有图层相关的操作
    """
    
    def __init__(self, parent_widget):
        self.parent = parent_widget
        self.logger = parent_widget.logger
        
        # 初始化图层相关属性
        self._init_layer_attributes()
    
    def _init_layer_attributes(self):
        """初始化图层相关属性"""
        # 确保父组件有必要的属性
        if not hasattr(self.parent, 'cover_layers'):
            self.parent.cover_layers = []
        if not hasattr(self.parent, 'layer_counter'):
            self.parent.layer_counter = 0
        if not hasattr(self.parent, 'current_layer_index'):
            self.parent.current_layer_index = -1
        if not hasattr(self.parent, 'layer_history'):
            self.parent.layer_history = []
    
    @error_handler(show_dialog=True)
    def add_layer(self, pixmap: QPixmap, layer_type: str = "imported", 
                  source_info: str = "", x: int = 0, y: int = 0) -> bool:
        """
        添加图层
        从原模块迁移而来
        """
        try:
            if not pixmap or pixmap.isNull():
                self.logger.warning("尝试添加空的图层")
                return False
            
            # 创建图层对象
            layer = CoverLayer(pixmap, layer_type, source_info, x, y)
            layer.id = self.parent.layer_counter
            self.parent.layer_counter += 1
            
            # 添加到图层列表
            self.parent.cover_layers.append(layer)
            self.parent.current_layer_index = len(self.parent.cover_layers) - 1
            
            # 注册资源
            try:
                register_pixmap(pixmap, f"图层 {layer.id}: {source_info}")
            except Exception as e:
                self.logger.warning(f"注册图层资源失败: {e}")
            
            # 更新合成封面
            self.update_composite_cover()
            
            self.logger.info(f"添加图层: {source_info} (ID: {layer.id})")
            return True
            
        except Exception as e:
            self.logger.error(f"添加图层失败: {e}")
            return False
    
    @error_handler(show_dialog=True)
    def remove_layer(self, layer_id: int) -> bool:
        """
        删除图层
        从原模块迁移而来
        """
        try:
            # 查找图层
            layer_index = -1
            for i, layer in enumerate(self.parent.cover_layers):
                if layer.id == layer_id:
                    layer_index = i
                    break
            
            if layer_index == -1:
                self.logger.warning(f"未找到图层 ID: {layer_id}")
                return False
            
            # 删除图层
            removed_layer = self.parent.cover_layers.pop(layer_index)
            
            # 更新当前图层索引
            if self.parent.current_layer_index >= layer_index:
                self.parent.current_layer_index = max(0, self.parent.current_layer_index - 1)
            
            if not self.parent.cover_layers:
                self.parent.current_layer_index = -1
            
            # 更新合成封面
            self.update_composite_cover()
            
            self.logger.info(f"删除图层: {removed_layer.source} (ID: {removed_layer.id})")
            return True
            
        except Exception as e:
            self.logger.error(f"删除图层失败: {e}")
            return False
    
    @error_handler(show_dialog=True)
    def move_layer(self, layer_id: int, new_index: int) -> bool:
        """
        移动图层位置
        从原模块迁移而来
        """
        try:
            # 查找图层
            old_index = -1
            for i, layer in enumerate(self.parent.cover_layers):
                if layer.id == layer_id:
                    old_index = i
                    break
            
            if old_index == -1:
                self.logger.warning(f"未找到图层 ID: {layer_id}")
                return False
            
            # 检查新索引
            if new_index < 0 or new_index >= len(self.parent.cover_layers):
                self.logger.warning(f"无效的图层索引: {new_index}")
                return False
            
            # 移动图层
            layer = self.parent.cover_layers.pop(old_index)
            self.parent.cover_layers.insert(new_index, layer)
            
            # 更新当前图层索引
            if self.parent.current_layer_index == old_index:
                self.parent.current_layer_index = new_index
            elif old_index < self.parent.current_layer_index <= new_index:
                self.parent.current_layer_index -= 1
            elif new_index <= self.parent.current_layer_index < old_index:
                self.parent.current_layer_index += 1
            
            # 更新合成封面
            self.update_composite_cover()
            
            self.logger.info(f"移动图层: {layer.source} 从 {old_index} 到 {new_index}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动图层失败: {e}")
            return False
    
    @error_handler(show_dialog=True)
    def set_layer_visibility(self, layer_id: int, visible: bool) -> bool:
        """
        设置图层可见性
        从原模块迁移而来
        """
        try:
            # 查找图层
            for layer in self.parent.cover_layers:
                if layer.id == layer_id:
                    layer.visible = visible
                    # 更新合成封面
                    self.update_composite_cover()
                    self.logger.info(f"设置图层可见性: {layer.source} -> {visible}")
                    return True
            
            self.logger.warning(f"未找到图层 ID: {layer_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"设置图层可见性失败: {e}")
            return False
    
    @error_handler(show_dialog=True)
    def set_layer_opacity(self, layer_id: int, opacity: float) -> bool:
        """
        设置图层透明度
        从原模块迁移而来
        """
        try:
            # 限制透明度范围
            opacity = max(0.0, min(1.0, opacity))
            
            # 查找图层
            for layer in self.parent.cover_layers:
                if layer.id == layer_id:
                    layer.opacity = opacity
                    # 更新合成封面
                    self.update_composite_cover()
                    self.logger.info(f"设置图层透明度: {layer.source} -> {opacity}")
                    return True
            
            self.logger.warning(f"未找到图层 ID: {layer_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"设置图层透明度失败: {e}")
            return False
    
    @performance_monitor(threshold_ms=300.0)
    def update_composite_cover(self):
        """
        更新合成封面
        从原模块迁移而来
        """
        try:
            if not hasattr(self.parent, 'cover_layers') or not self.parent.cover_layers:
                # 没有图层时显示默认状态
                if hasattr(self.parent, 'cover_preview'):
                    self.parent.cover_preview.setText("请先导入视频\n系统将自动匹配合适的封面")
                return
            
            # 创建合成图像
            composite_pixmap = self.create_composite_image()
            
            if composite_pixmap and hasattr(self.parent, 'cover_preview'):
                self.parent.cover_preview.setPixmap(composite_pixmap)
            
        except Exception as e:
            self.logger.error(f"更新合成封面失败: {e}")
    
    @performance_monitor(threshold_ms=500.0)
    def create_composite_image(self) -> Optional[QPixmap]:
        """
        创建合成图像
        从原模块迁移而来
        """
        try:
            if not hasattr(self.parent, 'cover_layers') or not self.parent.cover_layers:
                return None
            
            # 获取基础图像尺寸
            base_layer = None
            for layer in self.parent.cover_layers:
                if layer.visible and layer.pixmap and not layer.pixmap.isNull():
                    base_layer = layer
                    break
            
            if not base_layer:
                return None
            
            # 创建合成画布
            composite_pixmap = QPixmap(base_layer.pixmap.size())
            composite_pixmap.fill(Qt.GlobalColor.transparent)
            
            # 创建画笔进行合成
            painter = QPainter(composite_pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
            
            # 绘制所有可见图层
            for layer in self.parent.cover_layers:
                if layer.visible and layer.pixmap and not layer.pixmap.isNull():
                    painter.setOpacity(layer.opacity)
                    painter.drawPixmap(layer.x, layer.y, layer.pixmap)
            
            painter.end()
            
            # 注册合成图像资源
            try:
                register_pixmap(composite_pixmap, f"合成封面 {composite_pixmap.width()}x{composite_pixmap.height()}")
            except Exception as e:
                self.logger.warning(f"注册合成图像资源失败: {e}")
            
            return composite_pixmap
            
        except Exception as e:
            self.logger.error(f"创建合成图像失败: {e}")
            return None
    
    def clear_all_layers(self):
        """
        清除所有图层
        从原模块迁移而来
        """
        try:
            self.parent.cover_layers.clear()
            self.parent.layer_counter = 0
            self.parent.current_layer_index = -1
            
            # 更新合成封面
            self.update_composite_cover()
            
            self.logger.info("清除所有图层")
            
        except Exception as e:
            self.logger.error(f"清除所有图层失败: {e}")
    
    def get_layer_count(self) -> int:
        """获取图层数量"""
        return len(self.parent.cover_layers) if hasattr(self.parent, 'cover_layers') else 0
    
    def get_layer_info(self, layer_id: int) -> Optional[Dict[str, Any]]:
        """
        获取图层信息
        从原模块迁移而来
        """
        try:
            for layer in self.parent.cover_layers:
                if layer.id == layer_id:
                    return {
                        'id': layer.id,
                        'type': layer.type,
                        'source': layer.source,
                        'visible': layer.visible,
                        'opacity': layer.opacity,
                        'x': layer.x,
                        'y': layer.y,
                        'width': layer.width,
                        'height': layer.height,
                        'locked': layer.locked,
                        'blend_mode': layer.blend_mode
                    }
            return None
            
        except Exception as e:
            self.logger.error(f"获取图层信息失败: {e}")
            return None
    
    def get_all_layers_info(self) -> List[Dict[str, Any]]:
        """
        获取所有图层信息
        从原模块迁移而来
        """
        try:
            layers_info = []
            for layer in self.parent.cover_layers:
                info = self.get_layer_info(layer.id)
                if info:
                    layers_info.append(info)
            return layers_info
            
        except Exception as e:
            self.logger.error(f"获取所有图层信息失败: {e}")
            return []
