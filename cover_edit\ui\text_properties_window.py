#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字属性窗口模块 - 优化版 (约300行)
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QLabel, QPushButton, QSlider, QComboBox, QSpinBox,
                            QColorDialog, QWidget)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFontDatabase


class TextPropertiesWindow(QDialog):
    """文字属性设置窗口 - 优化版"""

    properties_changed = pyqtSignal()
    window_closed = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_module = parent

        self.setWindowTitle("文字属性设置")
        self.setFixedSize(350, 300)
        self.setModal(False)

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化UI界面 - 保持原始布局"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)  # 减少边距
        layout.setSpacing(6)  # 减少间距

        # 字体设置组
        font_group = self.create_font_group()
        layout.addWidget(font_group)

        # 颜色设置组（包含描边宽度和背景透明度）
        color_group = self.create_color_group()
        layout.addWidget(color_group)

        # 按钮组
        button_group = self.create_button_group()
        layout.addWidget(button_group)

    def create_font_group(self):
        """创建字体设置组 - 保持原始布局"""
        group = QGroupBox("字体设置")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # 字体和样式在一行
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体:"))
        self.font_combo = QComboBox()

        # 使用fonts文件夹中的字体
        font_list = self._load_fonts_folder()
        self.font_combo.addItems(font_list)
        # 设置默认字体（如果存在）
        if "微软雅黑" in font_list:
            self.font_combo.setCurrentText("微软雅黑")
        elif font_list:
            self.font_combo.setCurrentText(font_list[0])
        self.font_combo.setFixedWidth(170)  # 增加宽度以显示长字体名
        font_layout.addWidget(self.font_combo)

        font_layout.addWidget(QLabel("样式:"))
        self.font_style_combo = QComboBox()
        self.font_style_combo.addItems(["正常", "粗体", "斜体"])
        self.font_style_combo.setFixedWidth(80)
        font_layout.addWidget(self.font_style_combo)
        font_layout.addStretch()
        layout.addLayout(font_layout)

        # 字体大小
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("大小:"))
        self.font_size_slider = QSlider(Qt.Orientation.Horizontal)
        self.font_size_slider.setRange(12, 120)
        self.font_size_slider.setValue(24)
        self.font_size_label = QLabel("24px")
        size_layout.addWidget(self.font_size_slider)
        size_layout.addWidget(self.font_size_label)
        layout.addLayout(size_layout)

        # 字体间距和行距设置
        spacing_layout = QHBoxLayout()

        # 字体间距
        spacing_layout.addWidget(QLabel("间距:"))
        self.letter_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.letter_spacing_slider.setRange(-50, 50)  # -5.0px 到 5.0px
        self.letter_spacing_slider.setValue(0)  # 滑块默认0px
        self.letter_spacing_slider.setFixedWidth(100)
        spacing_layout.addWidget(self.letter_spacing_slider)

        self.letter_spacing_label = QLabel("0px")
        self.letter_spacing_label.setFixedWidth(35)
        spacing_layout.addWidget(self.letter_spacing_label)

        # 行距
        spacing_layout.addWidget(QLabel("行距:"))
        self.line_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.line_spacing_slider.setRange(80, 300)  # 0.8 到 3.0 倍行距
        self.line_spacing_slider.setValue(120)  # 默认1.2倍行距
        self.line_spacing_slider.setFixedWidth(100)
        spacing_layout.addWidget(self.line_spacing_slider)

        self.line_spacing_label = QLabel("1.2")
        self.line_spacing_label.setFixedWidth(35)
        spacing_layout.addWidget(self.line_spacing_label)

        spacing_layout.addStretch()
        layout.addLayout(spacing_layout)

        # 文字对齐设置
        alignment_layout = QHBoxLayout()
        alignment_layout.addWidget(QLabel("对齐:"))
        self.alignment_combo = QComboBox()
        self.alignment_combo.addItems(["左对齐", "居中对齐", "右对齐"])
        self.alignment_combo.setCurrentText("居中对齐")  # 默认居中
        self.alignment_combo.currentTextChanged.connect(self.on_alignment_changed)
        self.alignment_combo.setFixedWidth(100)
        alignment_layout.addWidget(self.alignment_combo)
        alignment_layout.addStretch()
        layout.addLayout(alignment_layout)

        return group

    def create_color_group(self):
        """创建颜色设置组 - 保持原始布局"""
        group = QGroupBox("颜色设置")
        layout = QVBoxLayout(group)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # 填充颜色
        fill_layout = QHBoxLayout()
        fill_layout.addWidget(QLabel("填充颜色:"))
        self.fill_color_btn = QPushButton()
        self.fill_color_btn.setFixedSize(60, 25)
        self.fill_color_btn.setStyleSheet("background-color: white; border: 1px solid #ccc;")
        fill_layout.addWidget(self.fill_color_btn)
        fill_layout.addStretch()
        layout.addLayout(fill_layout)

        # 描边颜色和宽度
        stroke_layout = QHBoxLayout()
        stroke_layout.addWidget(QLabel("描边颜色:"))
        self.stroke_color_btn = QPushButton()
        self.stroke_color_btn.setFixedSize(60, 25)
        self.stroke_color_btn.setStyleSheet("background-color: black; border: 1px solid #ccc;")
        stroke_layout.addWidget(self.stroke_color_btn)

        stroke_layout.addWidget(QLabel("宽度:"))
        self.stroke_width_spin = QSpinBox()
        self.stroke_width_spin.setRange(0, 10)
        self.stroke_width_spin.setValue(2)
        self.stroke_width_spin.setFixedWidth(50)
        stroke_layout.addWidget(self.stroke_width_spin)
        stroke_layout.addStretch()
        layout.addLayout(stroke_layout)

        # 背景颜色和透明度
        bg_layout = QHBoxLayout()
        bg_layout.addWidget(QLabel("背景颜色:"))
        self.bg_color_btn = QPushButton()
        self.bg_color_btn.setFixedSize(60, 25)
        self.bg_color_btn.setStyleSheet("background-color: blue; border: 1px solid #ccc;")
        bg_layout.addWidget(self.bg_color_btn)

        # 透明度标签和滑块
        bg_layout.addWidget(QLabel("透明度:"))
        self.bg_opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.bg_opacity_slider.setRange(0, 100)
        self.bg_opacity_slider.setValue(0)  # 默认透明度为0
        self.bg_opacity_slider.setFixedWidth(120)
        bg_layout.addWidget(self.bg_opacity_slider)

        self.bg_opacity_label = QLabel("0%")  # 默认显示0%
        self.bg_opacity_label.setFixedWidth(35)
        bg_layout.addWidget(self.bg_opacity_label)
        bg_layout.addStretch()
        layout.addLayout(bg_layout)

        return group

    def create_button_group(self):
        """创建按钮组 - 保持原始布局"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 0)
        layout.setSpacing(5)

        # 使用说明
        tip_label = QLabel("💡 提示：选中文字后调整属性，只对选中部分生效；未选中时对整个文本生效")
        tip_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                padding: 5px;
                background-color: #f0f0f0;
                border-radius: 3px;
                margin: 2px 0px;
            }
        """)
        tip_label.setWordWrap(True)
        layout.addWidget(tip_label)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        self.apply_btn = QPushButton("应用")
        self.apply_btn.setFixedHeight(30)
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setFixedHeight(30)
        self.close_btn = QPushButton("关闭")
        self.close_btn.setFixedHeight(30)

        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

        return widget

    def setup_connections(self):
        """设置信号连接 - 保持原始方法名"""
        # 字体属性变化
        self.font_combo.currentTextChanged.connect(self.on_font_changed)
        self.font_style_combo.currentTextChanged.connect(self.on_font_style_changed)
        self.font_size_slider.valueChanged.connect(self.on_font_size_changed)

        # 字体间距和行距变化
        self.letter_spacing_slider.valueChanged.connect(self.on_letter_spacing_changed)
        self.line_spacing_slider.valueChanged.connect(self.on_line_spacing_changed)

        # 颜色按钮
        self.fill_color_btn.clicked.connect(self.choose_fill_color)
        self.stroke_color_btn.clicked.connect(self.choose_stroke_color)
        self.bg_color_btn.clicked.connect(self.choose_bg_color)

        # 效果属性变化
        self.stroke_width_spin.valueChanged.connect(self.on_property_changed)
        self.bg_opacity_slider.valueChanged.connect(self.on_bg_opacity_changed)

        # 按钮
        self.apply_btn.clicked.connect(self.apply_properties)
        self.reset_btn.clicked.connect(self.reset_properties)
        self.close_btn.clicked.connect(self.close_window)

    def on_font_changed(self, font_name):
        """字体选择变化 - 保持原始方法名"""
        print(f"📝 字体选择变化: {font_name}")
        self.on_property_changed()

    def on_font_style_changed(self, style_name):
        """字体样式变化 - 保持原始方法名"""
        print(f"📝 字体样式选择变化: {style_name}")
        self.on_property_changed()

    def on_font_size_changed(self, value):
        """字体大小变化 - 保持原始方法名"""
        print(f"📝 字体大小变化: {value}px")
        self.font_size_label.setText(f"{value}px")
        self.on_property_changed()

    def on_letter_spacing_changed(self, value):
        """字体间距变化 - 保持原始方法名"""
        spacing_px = value / 10.0  # 转换为像素值，范围 -5.0px 到 5.0px
        print(f"📝 字体间距变化: {spacing_px}px")
        self.letter_spacing_label.setText(f"{spacing_px:.1f}px")
        self.on_property_changed()

    def on_line_spacing_changed(self, value):
        """行距变化 - 保持原始方法名"""
        line_height = value / 100.0  # 转换为倍数，范围 0.8 到 3.0
        print(f"📝 行距变化: {line_height:.1f}")
        self.line_spacing_label.setText(f"{line_height:.1f}")
        self.on_property_changed()

    def on_alignment_changed(self, text):
        """文字对齐变化"""
        print(f"📝 对齐方式变化: {text}")
        self.on_property_changed()

    def on_bg_opacity_changed(self, value):
        """背景透明度变化 - 保持原始方法名"""
        self.bg_opacity_label.setText(f"{value}%")
        self.on_property_changed()

    def on_property_changed(self):
        """属性变化时发出信号 - 保持原始方法名"""
        print("🎨 发出文字属性变化信号...")
        self.properties_changed.emit()
        print("🎨 文字属性已变化，信号已发出")

    def choose_fill_color(self):
        """选择填充颜色 - 保持原始方法名"""
        from PyQt6.QtGui import QColor
        color = QColorDialog.getColor(QColor(255, 255, 255), self, "选择填充颜色")
        if color.isValid():
            self.fill_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            self.on_property_changed()

    def choose_stroke_color(self):
        """选择描边颜色 - 保持原始方法名"""
        from PyQt6.QtGui import QColor
        color = QColorDialog.getColor(QColor(0, 0, 0), self, "选择描边颜色")
        if color.isValid():
            self.stroke_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            self.on_property_changed()

    def choose_bg_color(self):
        """选择背景颜色 - 保持原始方法名"""
        from PyQt6.QtGui import QColor
        color = QColorDialog.getColor(QColor(0, 0, 255), self, "选择背景颜色")
        if color.isValid():
            self.bg_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #ccc;")
            self.on_property_changed()

    def apply_properties(self):
        """应用属性 - 保持原始方法名"""
        print("✅ 应用文字属性")
        self.properties_changed.emit()

    def reset_properties(self):
        """重置属性 - 保持原始方法名"""
        # 设置默认字体（优先使用微软雅黑，如果不存在则使用第一个可用字体）
        if self.font_combo.findText("微软雅黑") >= 0:
            self.font_combo.setCurrentText("微软雅黑")
        elif self.font_combo.count() > 0:
            self.font_combo.setCurrentIndex(0)

        self.font_style_combo.setCurrentText("正常")
        self.font_size_slider.setValue(24)
        self.stroke_width_spin.setValue(2)
        self.bg_opacity_slider.setValue(0)  # 重置时透明度为0

        # 重置字体间距和行距
        self.letter_spacing_slider.setValue(0)  # 滑块重置为0px
        self.line_spacing_slider.setValue(120)  # 默认1.2倍行距

        self.fill_color_btn.setStyleSheet("background-color: white; border: 1px solid #ccc;")
        self.stroke_color_btn.setStyleSheet("background-color: black; border: 1px solid #ccc;")
        self.bg_color_btn.setStyleSheet("background-color: blue; border: 1px solid #ccc;")

        print("🔄 文字属性已重置")
        self.on_property_changed()

    def close_window(self):
        """关闭窗口 - 保持原始方法名"""
        self.window_closed.emit()
        self.close()
        print("❌ 文字属性窗口已关闭")

    def get_current_properties(self):
        """获取当前属性设置"""
        display_font_name = self.font_combo.currentText()
        actual_font_name = self.get_actual_font_family(display_font_name)

        return {
            'font_family': actual_font_name,  # 返回实际的字体族名称
            'display_font_name': display_font_name,  # 保留显示名称
            'font_style': self.font_style_combo.currentText(),
            'font_size': self.font_size_slider.value(),
            'letter_spacing': self.letter_spacing_slider.value() / 10.0,
            'line_spacing': self.line_spacing_slider.value() / 100.0,
            'stroke_width': self.stroke_width_spin.value(),
            'bg_opacity': self.bg_opacity_slider.value(),
            'text_alignment': self.alignment_combo.currentText()
        }

    def _load_fonts_folder(self):
        """加载fonts文件夹中的字体，使用中文文件名作为显示名"""
        try:
            import os
            from PyQt6.QtGui import QFontDatabase

            # 获取项目根目录下的fonts文件夹
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            fonts_folder = os.path.join(project_root, "resources", "fonts")

            print(f"🔤 加载字体文件夹: {fonts_folder}")

            font_list = []
            self.font_file_mapping = {}  # 存储显示名到文件路径的映射

            if not os.path.exists(fonts_folder):
                print(f"❌ fonts文件夹不存在: {fonts_folder}")
                return ["Arial", "Microsoft YaHei", "SimSun"]

            # 支持的字体文件扩展名
            font_extensions = ['.ttf', '.ttc', '.otf', '.TTF', '.TTC', '.OTF']

            # 扫描fonts文件夹中的字体文件
            for filename in sorted(os.listdir(fonts_folder)):
                if any(filename.endswith(ext) for ext in font_extensions):
                    font_path = os.path.join(fonts_folder, filename)
                    try:
                        # 加载字体文件
                        font_id = QFontDatabase.addApplicationFont(font_path)
                        if font_id != -1:
                            # 使用文件名（去掉扩展名）作为显示名
                            display_name = os.path.splitext(filename)[0]

                            # 获取实际的字体族名称（用于后续字体应用）
                            font_families = QFontDatabase.applicationFontFamilies(font_id)

                            if font_families:
                                # 存储映射关系：显示名 -> 实际字体族名
                                self.font_file_mapping[display_name] = {
                                    'family': font_families[0],  # 使用第一个字体族
                                    'file_path': font_path,
                                    'font_id': font_id
                                }
                                font_list.append(display_name)
                                print(f"✅ 成功加载字体: {display_name} -> {font_families[0]}")
                            else:
                                print(f"⚠️ 字体文件无字体族: {filename}")
                        else:
                            print(f"❌ 无法加载字体文件: {filename}")
                    except Exception as e:
                        print(f"❌ 加载字体文件失败 {filename}: {e}")

            if font_list:
                print(f"🔤 总共加载了 {len(font_list)} 个字体")
                print(f"🔤 字体列表: {font_list[:5]}..." if len(font_list) > 5 else f"🔤 字体列表: {font_list}")
            else:
                print("⚠️ 没有找到可用的字体文件，使用默认字体")
                font_list = ["Arial", "Microsoft YaHei", "SimSun"]
                self.font_file_mapping = {}

            return font_list

        except Exception as e:
            print(f"❌ 加载fonts文件夹字体失败: {e}")
            return ["Arial", "Microsoft YaHei", "SimSun"]

    def get_actual_font_family(self, display_name):
        """根据显示名获取实际的字体族名称"""
        if hasattr(self, 'font_file_mapping') and display_name in self.font_file_mapping:
            return self.font_file_mapping[display_name]['family']
        return display_name  # 如果没有映射，直接返回显示名

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.window_closed.emit()
        super().closeEvent(event)



