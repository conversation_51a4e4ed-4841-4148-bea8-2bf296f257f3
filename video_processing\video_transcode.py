import os
import re
import subprocess
import time
from PyQt6.QtWidgets import (QVBoxLayout, QLabel, QGroupBox, QHBoxLayout, 
                             QLineEdit, QPushButton, QComboBox, QTextEdit, 
                             QFileDialog, QSizePolicy, QCheckBox, QProgressBar,
                             QWidget)
from PyQt6.QtGui import QFont, QColor, QLinearGradient, QPainter, QBrush
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QRect, QProcess, QTimer  # 修复：添加QTimer
import winreg
import ctypes
from core.base_module import BaseModule
from core.path_manager import get_path, set_path

class GradientProgressBar(QProgressBar):
    """自定义渐变进度条（带平滑动画）"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QProgressBar {
                background-color: #1E2025;
                border: 1px solid #444;
                border-radius: 5px;
                text-align: center;
                height: 25px;
                color: white;
                font-weight: bold;
            }
        """)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景
        bg_rect = QRect(0, 0, self.width(), self.height())
        painter.fillRect(bg_rect, QColor("#1E2025"))
        
        # 计算进度宽度
        progress = self.value()
        progress_width = int(progress * self.width() / 100)
        
        if progress_width > 0:
            # 创建渐变效果 - 从深绿到亮绿
            gradient = QLinearGradient(0, 0, progress_width, 0)
            
            # 根据进度调整渐变颜色
            if progress < 30:
                # 低进度时使用深绿色系
                gradient.setColorAt(0.0, QColor("#2E7D32"))  # 深绿
                gradient.setColorAt(1.0, QColor("#388E3C"))  # 中绿
            elif progress < 70:
                # 中等进度时使用绿色系
                gradient.setColorAt(0.0, QColor("#388E3C"))  # 中绿
                gradient.setColorAt(0.5, QColor("#43A047"))  # 绿
                gradient.setColorAt(1.0, QColor("#4CAF50"))  # 亮绿
            else:
                # 高进度时使用亮绿色系
                gradient.setColorAt(0.0, QColor("#4CAF50"))  # 亮绿
                gradient.setColorAt(0.7, QColor("#66BB6A"))  # 更亮绿
                gradient.setColorAt(1.0, QColor("#81C784"))  # 最亮绿
            
            # 绘制进度条
            progress_rect = QRect(0, 0, progress_width, self.height())
            painter.fillRect(progress_rect, QBrush(gradient))
        
        # 绘制边框
        painter.setPen(QColor("#444"))
        painter.drawRoundedRect(0, 0, self.width()-1, self.height()-1, 4, 4)
        
        # 绘制进度文本
        painter.setPen(QColor("white"))
        text = f"{progress}%"
        painter.drawText(bg_rect, Qt.AlignmentFlag.AlignCenter, text)
    
    def setValueSmoothly(self, value):
        """平滑过渡动画"""
        self.target_value = value
        if not hasattr(self, '_timer'):
            self._timer = QTimer(self)
            self._timer.timeout.connect(self.update_value)
            self._timer.setInterval(50)  # 每50ms更新一次
        
        # 如果目标值比当前值小，直接设置
        if value < self.value():
            self.setValue(value)
            return
        
        self._timer.start()

    def update_value(self):
        current = self.value()
        if current == self.target_value:
            self._timer.stop()
            return
            
        # 计算步长（动态调整速度）
        diff = self.target_value - current
        step = max(1, min(5, abs(diff) // 5))
        
        # 更新值
        new_value = current + step
        if new_value > self.target_value:
            new_value = self.target_value
            
        self.setValue(new_value)
        
        if new_value >= self.target_value:
            self._timer.stop()

class TranscodeThread(QThread):
    """真实的FFmpeg转码线程（使用ffprobe改进）"""
    update_log = pyqtSignal(str)
    update_progress = pyqtSignal(int)
    finished = pyqtSignal()
    
    def __init__(self, file_path, output_format, output_path, use_gpu,
                 resolution, codec, quality, ffmpeg_path, ffprobe_path):
        super().__init__()
        self.file_path = file_path
        self.output_format = output_format
        self.output_path = output_path
        self.use_gpu = use_gpu
        self.resolution = resolution
        self.codec = codec
        self.quality = quality
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.process = None
        self._is_running = True
        self.duration_seconds = 0
        
        # 编码器映射表
        self.codec_map = {
            "H.264 (兼容性好)": "h264",
            "H.265 (高效压缩)": "hevc",
            "AV1 (最新标准)": "av1",
            "VP9 (Web优化)": "vp9",
            "MPEG-2 (DVD质量)": "mpeg2video",
            "MPEG-4 (通用编码)": "mpeg4",
            "WMV (Windows媒体)": "wmv2",
            "Theora (开源编码)": "theora",
            "原始编码": "copy"
        }
        
        # 质量预设映射
        self.quality_map = {
            "超高质量 (文件最大)": "slow",
            "高质量 (推荐)": "medium",
            "中等质量": "fast",
            "低质量 (文件最小)": "veryfast",
            "恒定质量 (CRF 18)": "crf18",
            "快速编码 (低CPU占用)": "superfast",
            "慢速编码 (高压缩率)": "veryslow"
        }
        
        # 硬件加速编码器映射
        self.hw_encoder_map = {
            "h264": {
                "nvenc": "h264_nvenc",
                "qsv": "h264_qsv",
                "amf": "h264_amf"
            },
            "hevc": {
                "nvenc": "hevc_nvenc",
                "qsv": "hevc_qsv",
                "amf": "hevc_amf"
            },
            "av1": {
                "nvenc": "av1_nvenc",
                "qsv": "av1_qsv",
                "amf": "av1_amf"
            },
            "vp9": {
                "nvenc": "vp9_nvenc",
                "qsv": "vp9_qsv",
                "amf": "vp9_amf"
            }
        }
    
    def detect_hardware_accelerator(self):
        """检测可用的硬件加速方案"""
        try:
            # 检测NVIDIA GPU
            cuda_path = os.environ.get("CUDA_PATH", None)
            if cuda_path and os.path.exists(cuda_path):
                return "nvenc"
            
            # 检测Intel GPU
            try:
                # 尝试通过注册表检测Intel GPU
                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
                )
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        if subkey_name.startswith('0'):
                            subkey = winreg.OpenKey(key, subkey_name)
                            provider, _ = winreg.QueryValueEx(subkey, "ProviderName")
                            if "Intel" in provider:
                                return "qsv"
                    except:
                        continue
                winreg.CloseKey(key)
            except:
                pass
            
            # 检测AMD GPU
            try:
                # 尝试通过注册表检测AMD GPU
                key = winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
                )
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        if subkey_name.startswith('0'):
                            subkey = winreg.OpenKey(key, subkey_name)
                            provider, _ = winreg.QueryValueEx(subkey, "ProviderName")
                            if "AMD" in provider or "Advanced Micro Devices" in provider:
                                return "amf"
                    except:
                        continue
                winreg.CloseKey(key)
            except:
                pass
            
        except Exception as e:
            self.update_log.emit(f"> 硬件检测错误: {str(e)}")
        
        return None
    
    def get_video_duration(self):
        """使用ffprobe获取准确的视频时长（秒）"""
        try:
            cmd = [
                self.ffprobe_path,
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                self.file_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 解析输出获取时长（浮点数秒）
            duration = float(result.stdout.strip())
            return duration
        except Exception as e:
            self.update_log.emit(f"> 获取时长失败: {str(e)}")
            if hasattr(result, 'stderr'):
                self.update_log.emit(f"> 错误输出: {result.stderr}")
            return 0
    
    def get_video_rotation(self):
        """使用ffprobe获取视频旋转信息"""
        try:
            # 方法1：直接获取旋转元数据
            cmd = [
                self.ffprobe_path,
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "side_data=rotation",
                "-of", "default=noprint_wrappers=1:nokey=1",
                self.file_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 解析旋转角度
            rotation = result.stdout.strip()
            if rotation:
                return int(rotation)
            
            # 方法2：检查宽高比判断朝向
            cmd = [
                self.ffprobe_path,
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height",
                "-of", "csv=p=0",
                self.file_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 解析宽高
            width, height = map(int, result.stdout.strip().split(','))
            
            # 如果高度大于宽度，认为是竖屏视频
            return 90 if height > width else 0
        except Exception as e:
            self.update_log.emit(f"> 获取旋转信息失败: {str(e)}")
            return 0
    
    def build_ffmpeg_command(self):
        """构建FFmpeg命令行参数（改进旋转逻辑）"""
        # 确保输出目录存在
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)
        
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(self.file_path))[0]
        output_file = os.path.join(
            self.output_path, 
            f"{base_name}_transcoded.{self.output_format.lower()}"
        )
        
        # 基本命令
        cmd = [self.ffmpeg_path, "-y", "-i", self.file_path]
        
        # 视频编码设置
        video_codec = self.codec_map.get(self.codec, "libx264")
        
        # GPU加速支持
        hw_accel_type = None
        if self.use_gpu:
            hw_accel_type = self.detect_hardware_accelerator()
            
            if hw_accel_type and video_codec in self.hw_encoder_map:
                if hw_accel_type in self.hw_encoder_map[video_codec]:
                    video_codec = self.hw_encoder_map[video_codec][hw_accel_type]
                    self.update_log.emit(f"> 使用硬件加速: {video_codec}")
        
        cmd.extend(["-c:v", video_codec])
        
        # 质量预设
        quality_setting = self.quality_map.get(self.quality, "medium")
        if quality_setting == "crf18":
            cmd.extend(["-crf", "18"])
        elif quality_setting.startswith("crf"):
            # 提取CRF值
            crf_value = re.search(r"crf(\d+)", quality_setting)
            if crf_value:
                cmd.extend(["-crf", crf_value.group(1)])
            else:
                cmd.extend(["-crf", "23"])
        else:
            cmd.extend(["-preset", quality_setting])
        
        # 分辨率设置
        resolution_map = {
            "2160p (4K UHD, 3840×2160)": "3840:2160",
            "1440p (2K QHD, 2560×1440)": "2560:1440",
            "1080p (Full HD, 1920×1080)": "1920:1080",
            "720p (HD, 1280×720)": "1280:720",
            "2160×3840 (9:16 竖屏)": "2160:3840",
            "1440×2560 (9:16 竖屏)": "1440:2560",
            "1080×1920 (9:16 竖屏)": "1080:1920",
            "720×1280 (9:16 竖屏)": "720:1280",
        }
        
        vf_options = []
        if self.resolution != "原始分辨率":
            resolution = resolution_map.get(self.resolution, "1920:1080")
            vf_options.append(f"scale={resolution}")
            
            # 智能旋转逻辑：仅当原始视频是横屏且输出是竖屏时才旋转
            is_vertical_output = "竖屏" in self.resolution
            if is_vertical_output:
                original_rotation = self.get_video_rotation()
                # 原始视频是横屏（0度）时才需要旋转
                if original_rotation == 0:
                    vf_options.append("transpose=1")  # 顺时针旋转90度
                    self.update_log.emit("> 添加旋转滤镜: 横屏转竖屏")
                else:
                    self.update_log.emit("> 原始视频已是竖屏，跳过旋转")
        
        # 添加滤镜参数
        if vf_options:
            cmd.extend(["-vf", ",".join(vf_options)])
        
        # 音频设置（保持原样）
        cmd.extend(["-c:a", "copy"])
        
        # 输出文件
        cmd.append(output_file)
        
        return cmd, output_file
    
    def run(self):
        try:
            # 获取视频时长
            self.duration_seconds = self.get_video_duration()
            if self.duration_seconds <= 0:
                self.update_log.emit("> 错误: 无法获取视频时长，进度显示可能不准确")
                self.duration_seconds = 1  # 防止除零错误
            
            # 构建FFmpeg命令
            cmd, output_file = self.build_ffmpeg_command()
            
            # 记录执行的命令
            self.update_log.emit(f"> 执行命令: {' '.join(cmd)}")
            self.update_log.emit(f"> 视频总时长: {self.format_duration(self.duration_seconds)}")
            self.update_log.emit(f"> 输出文件: {output_file}")
            
            # 创建进程
            self.process = QProcess()
            self.process.setProcessChannelMode(QProcess.ProcessChannelMode.MergedChannels)
            
            # 连接信号
            self.process.readyReadStandardOutput.connect(self.handle_output)
            self.process.readyReadStandardError.connect(self.handle_output)
            self.process.finished.connect(self.on_process_finished)
            self.process.errorOccurred.connect(self.on_process_error)
            
            # 启动进程
            self.process.start(cmd[0], cmd[1:])
            
            # 等待进程完成
            while not self.process.waitForFinished(500) and self._is_running:
                pass
            
            # 确保进度100%
            self.update_progress.emit(100)
            
        except Exception as e:
            self.update_log.emit(f"> 转码错误: {str(e)}")
            self.finished.emit()
    
    def format_duration(self, seconds):
        """将秒数格式化为HH:MM:SS"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def handle_output(self):
        """处理FFmpeg输出并解析进度（改进版）"""
        if not self.process:
            return
        
        # 读取所有可用输出
        output = self.process.readAllStandardError().data().decode("utf-8", errors="ignore")
        if not output:
            output = self.process.readAllStandardOutput().data().decode("utf-8", errors="ignore")
        
        if not output:
            return
        
        # 发送日志
        self.update_log.emit(output)
        
        # 解析进度 - 使用更可靠的正则表达式
        time_match = re.search(r"time=(\d+):(\d+):(\d+)\.\d+", output)
        if time_match and self.duration_seconds > 0:
            hours = int(time_match.group(1))
            minutes = int(time_match.group(2))
            seconds = int(time_match.group(3))
            current_seconds = hours * 3600 + minutes * 60 + seconds
            
            # 计算进度百分比（添加边界保护）
            progress = min(99, max(0, int((current_seconds / self.duration_seconds) * 100)))
            self.update_progress.emit(progress)
        elif self.duration_seconds <= 0:
            # 无法获取时长时的备选方案
            if not hasattr(self, 'fallback_progress'):
                self.fallback_progress = 0
            self.fallback_progress = min(self.fallback_progress + 1, 50)  # 缓慢增加到50%
            self.update_progress.emit(self.fallback_progress)
    
    def on_process_finished(self, exit_code, exit_status):
        """转码完成处理"""
        if exit_code == 0 and exit_status == QProcess.ExitStatus.NormalExit:
            self.update_progress.emit(100)
            self.update_log.emit("> 转码成功完成!")
        else:
            error = self.process.errorString()
            self.update_log.emit(f"> 转码失败，错误码: {exit_code}, 状态: {exit_status}, 错误: {error}")
        
        self.finished.emit()
    
    def on_process_error(self, error):
        """进程错误处理"""
        self.update_log.emit(f"> 进程错误: {error.name}")
    
    def stop(self):
        """停止转码进程"""
        self._is_running = False
        if self.process and self.process.state() == QProcess.ProcessState.Running:
            self.process.terminate()
            self.process.waitForFinished(1000)

class VideoTranscodeModule(BaseModule):
    def init_ui(self):
        try:
            self.logger.info("初始化视频转码模块")
            
            # 设置FFmpeg和FFprobe路径
            base_dir = os.path.dirname(os.path.abspath(__file__))
            tools_dir = os.path.join(base_dir, "..", "tools")

            # 根据平台设置FFmpeg可执行文件名
            if os.name == 'nt':  # Windows
                self.ffmpeg_path = os.path.join(tools_dir, "ffmpeg.exe")
                self.ffprobe_path = os.path.join(tools_dir, "ffprobe.exe")
            else:  # Linux/Mac
                self.ffmpeg_path = os.path.join(tools_dir, "ffmpeg")
                self.ffprobe_path = os.path.join(tools_dir, "ffprobe")
            
            self.logger.info(f"FFmpeg路径: {self.ffmpeg_path}")
            self.logger.info(f"FFprobe路径: {self.ffprobe_path}")
            
            # 主布局
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(15)
            self.setStyleSheet(self.get_style())
            
            # 创建顶部容器用于标题居中
            title_container = QWidget()
            title_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            title_layout = QHBoxLayout(title_container)
            title_layout.setContentsMargins(0, 0, 0, 0)
            
            # 标题 - 居中显示
            title = QLabel(self.get_title())
            title.setStyleSheet(f"color: {self.get_color()}; font-size: 24px; font-weight: bold;")
            title.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_layout.addWidget(title)
            
            main_layout.addWidget(title_container)
            
            # 文件选择区域 - 包含输入和输出路径
            file_group = QGroupBox("选择视频文件")
            file_layout = QVBoxLayout(file_group)
            file_layout.setContentsMargins(15, 20, 15, 15)
            
            # 输入文件路径
            input_layout = QHBoxLayout()
            self.path_input = QLineEdit()
            self.path_input.setPlaceholderText("选择视频文件...")
            self.path_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            
            browse_btn = QPushButton("浏览...")
            browse_btn.setMinimumSize(100, 40)
            browse_btn.clicked.connect(self.select_file)
            
            input_layout.addWidget(self.path_input)
            input_layout.addWidget(browse_btn)
            file_layout.addLayout(input_layout)
            
            # 输出路径
            output_layout = QHBoxLayout()
            self.output_path_input = QLineEdit()
            self.output_path_input.setPlaceholderText("选择输出目录...")
            self.output_path_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            
            output_browse_btn = QPushButton("浏览...")
            output_browse_btn.setMinimumSize(100, 40)
            output_browse_btn.clicked.connect(self.select_output_directory)
            
            output_layout.addWidget(self.output_path_input)
            output_layout.addWidget(output_browse_btn)
            file_layout.addLayout(output_layout)
            
            # GPU加速选项
            gpu_layout = QHBoxLayout()
            
            # GPU加速勾选框
            self.gpu_checkbox = QCheckBox("GPU加速")
            self.gpu_checkbox.setChecked(True)
            
            # GPU信息标签
            self.gpu_info_label = QLabel()
            self.gpu_info_label.setStyleSheet("color: #888888; font-size: 12px;")
            
            gpu_layout.addWidget(self.gpu_checkbox)
            gpu_layout.addStretch()
            gpu_layout.addWidget(self.gpu_info_label)
            file_layout.addLayout(gpu_layout)
            
            main_layout.addWidget(file_group)
            
            # 转码设置组 - 使用新的两行布局
            format_group = QGroupBox("转码设置")
            format_layout = QVBoxLayout(format_group)
            format_layout.setContentsMargins(15, 20, 15, 15)
            
            # 第一行：输出格式 + 分辨率
            row1_layout = QHBoxLayout()
            
            # 输出格式（左侧）
            format_box = QGroupBox("输出格式")
            format_box_layout = QVBoxLayout(format_box)
            format_box_layout.setContentsMargins(10, 15, 10, 10)
            self.format_combo = QComboBox()
            
            # 添加所有支持的格式
            self.format_combo.addItems([
                "MP4", "MKV", "MOV", "AVI", "WMV", "M4V", 
                "MPEG", "VOB", "WEBM", "OGV", "3GP", "FLV", 
                "F4V", "SWF"
            ])
            format_box_layout.addWidget(self.format_combo)
            row1_layout.addWidget(format_box, 1)  # 设置拉伸因子为1
            
            # 分辨率（右侧）
            resolution_box = QGroupBox("分辨率")
            resolution_box_layout = QVBoxLayout(resolution_box)
            resolution_box_layout.setContentsMargins(10, 15, 10, 10)
            self.resolution_combo = QComboBox()
            
            # 添加竖屏分辨率选项
            self.resolution_combo.addItems([
                "原始分辨率", 
                "2160p (4K UHD, 3840×2160)",
                "1440p (2K QHD, 2560×1440)",
                "1080p (Full HD, 1920×1080)", 
                "720p (HD, 1280×720)", 
                # 9:16 竖屏分辨率
                "2160×3840 (9:16 竖屏)",
                "1440×2560 (9:16 竖屏)",
                "1080×1920 (9:16 竖屏)",
                "720×1280 (9:16 竖屏)",
            ])
            resolution_box_layout.addWidget(self.resolution_combo)
            row1_layout.addWidget(resolution_box, 1)  # 设置拉伸因子为1
            
            format_layout.addLayout(row1_layout)
            
            # 第二行：视频编码 + 质量预设
            row2_layout = QHBoxLayout()
            
            # 视频编码（左侧）
            codec_box = QGroupBox("视频编码")
            codec_box_layout = QVBoxLayout(codec_box)
            codec_box_layout.setContentsMargins(10, 15, 10, 10)
            self.codec_combo = QComboBox()
            self.codec_combo.addItems([
                "H.264 (兼容性好)", 
                "H.265 (高效压缩)", 
                "AV1 (最新标准)", 
                "VP9 (Web优化)", 
                "MPEG-2 (DVD质量)",
                "MPEG-4 (通用编码)",
                "WMV (Windows媒体)",
                "Theora (开源编码)",
                "原始编码"
            ])
            codec_box_layout.addWidget(self.codec_combo)
            row2_layout.addWidget(codec_box, 1)  # 设置拉伸因子为1
            
            # 质量预设（右侧）
            quality_box = QGroupBox("质量预设")
            quality_box_layout = QVBoxLayout(quality_box)
            quality_box_layout.setContentsMargins(10, 15, 10, 10)
            self.quality_combo = QComboBox()
            self.quality_combo.addItems([
                "超高质量 (文件最大)", 
                "高质量 (推荐)", 
                "中等质量", 
                "低质量 (文件最小)", 
                "恒定质量 (CRF 18)",
                "快速编码 (低CPU占用)",
                "慢速编码 (高压缩率)"
            ])
            quality_box_layout.addWidget(self.quality_combo)
            row2_layout.addWidget(quality_box, 1)  # 设置拉伸因子为1
            
            format_layout.addLayout(row2_layout)
            
            main_layout.addWidget(format_group)
            
            # 添加进度条
            progress_container = QWidget()
            progress_layout = QVBoxLayout(progress_container)
            progress_layout.setContentsMargins(0, 10, 0, 10)
            
            # 进度条标签
            progress_label = QLabel("转码进度")
            progress_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #CCCCCC;")
            progress_layout.addWidget(progress_label)
            
            # 自定义渐变进度条
            self.progress_bar = GradientProgressBar()
            self.progress_bar.setRange(0, 100)
            self.progress_bar.setValue(0)
            progress_layout.addWidget(self.progress_bar)
            
            main_layout.addWidget(progress_container)
            
            # 操作按钮 - 居中显示
            btn_container = QWidget()
            btn_layout = QHBoxLayout(btn_container)
            btn_layout.setContentsMargins(0, 0, 0, 0)
            btn_layout.addStretch()
            
            self.start_btn = QPushButton("开始转码")
            self.start_btn.setMinimumSize(160, 45)
            self.start_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {self.get_color()};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 16px;
                }}
                QPushButton:hover {{
                    background-color: #88B36F;
                }}
                QPushButton:disabled {{
                    background-color: #444;
                    color: #888;
                }}
            """)
            self.start_btn.clicked.connect(self.start_transcode)
            
            btn_layout.addWidget(self.start_btn)
            btn_layout.addStretch()
            
            main_layout.addWidget(btn_container)
            
            # 日志区域
            log_group = QGroupBox("处理日志")
            log_layout = QVBoxLayout(log_group)
            
            self.log_output = QTextEdit()
            self.log_output.setReadOnly(True)
            self.log_output.setStyleSheet("""
                QTextEdit {
                    background-color: #1E2025;
                    color: #CCCCCC;
                    border: 1px solid #444;
                    border-radius: 4px;
                    font-family: Consolas, 'Courier New', monospace;
                }
            """)
            log_layout.addWidget(self.log_output)
            
            main_layout.addWidget(log_group, 1)  # 设置拉伸因子
            
            # 初始化转码线程
            self.transcode_thread = None
            
            # 检测GPU信息
            self.detect_gpu()
            
            # 显示FFmpeg版本
            self.show_ffmpeg_version()
            
            self.logger.info("视频转码模块初始化完成")
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}", exc_info=True)
            # 显示错误UI
            layout = QVBoxLayout(self)
            error_label = QLabel(f"初始化错误: {str(e)}")
            error_label.setStyleSheet("color: red; font-size: 16px;")
            layout.addWidget(error_label)
    
    def show_ffmpeg_version(self):
        """显示FFmpeg版本信息"""
        try:
            # 检查FFmpeg是否存在
            if not os.path.exists(self.ffmpeg_path):
                self.log_output.append("> 错误: 未找到FFmpeg可执行文件")
                self.log_output.append(f"> 路径: {self.ffmpeg_path}")
                return
            
            # 获取FFmpeg版本
            result = subprocess.run(
                [self.ffmpeg_path, "-version"],
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # 提取版本信息
            version_line = result.stdout.split('\n')[0] if result.stdout else "未知版本"
            self.log_output.append(f"> FFmpeg版本: {version_line}")
            
            # 显示FFprobe版本
            if os.path.exists(self.ffprobe_path):
                result = subprocess.run(
                    [self.ffprobe_path, "-version"],
                    capture_output=True,
                    text=True,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                probe_line = result.stdout.split('\n')[0] if result.stdout else "未知版本"
                self.log_output.append(f"> FFprobe版本: {probe_line}")
            
        except Exception as e:
            self.log_output.append(f"> 获取FFmpeg版本失败: {str(e)}")
    
    def detect_gpu(self):
        """优化的GPU检测方法 - 使用Windows注册表"""
        gpu_info = ""
        gpu_detected = False
        gpus = []
        
        try:
            # 打开显卡注册表项
            key_path = r"SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}"
            
            # 尝试访问注册表
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_READ)
                index = 0
                while True:
                    try:
                        # 枚举所有子键
                        subkey_name = winreg.EnumKey(key, index)
                        
                        # 跳过0000配置项，只检查0001、0002等
                        if subkey_name.startswith('0'):
                            try:
                                # 打开子键并读取显卡名称
                                subkey_path = f"{key_path}\\{subkey_name}"
                                subkey = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, subkey_path)
                                
                                try:
                                    # 尝试获取显卡名称
                                    driver_desc, _ = winreg.QueryValueEx(subkey, "DriverDesc")
                                    gpus.append(driver_desc)
                                except OSError:
                                    # 没有DriverDesc值，尝试获取ProviderName
                                    try:
                                        provider, _ = winreg.QueryValueEx(subkey, "ProviderName")
                                        gpus.append(f"{provider} 显卡")
                                    except:
                                        pass
                                finally:
                                    winreg.CloseKey(subkey)
                            except OSError:
                                # 无法打开子键，跳过
                                pass
                        index += 1
                    except OSError:
                        # 没有更多子键
                        break
            except OSError as e:
                self.log_output.append(f"> 注册表访问错误: {e}")
            finally:
                # 确保关闭注册表键
                if 'key' in locals():
                    winreg.CloseKey(key)
            
            # 如果找到GPU
            if gpus:
                # 去重，避免相同显卡多次出现
                unique_gpus = []
                for gpu in gpus:
                    if gpu not in unique_gpus:
                        unique_gpus.append(gpu)
                
                gpu_info = "检测到GPU: " + ", ".join(unique_gpus)
                gpu_detected = True
        
        except Exception as e:
            self.log_output.append(f"> GPU检测错误: {str(e)}")
        
        # 如果未检测到GPU
        if not gpus:
            gpu_info = "未检测到GPU"
        
        # 更新UI显示GPU信息
        self.gpu_info_label.setText(gpu_info)
        self.log_output.append(f"> {gpu_info}")
        
        # 如果没有检测到GPU，禁用GPU加速选项
        if not gpu_detected:
            self.gpu_checkbox.setChecked(False)
            self.gpu_checkbox.setEnabled(False)
            self.log_output.append("> GPU加速功能已禁用")
        else:
            self.gpu_checkbox.setEnabled(True)
            self.log_output.append("> GPU加速功能已启用")
    
    def get_title(self):
        return "视频转码"
    
    def get_color(self):
        return "#98C379"
    
    def select_file(self):
        """打开文件选择对话框"""
        # 构建支持所有格式的过滤器（添加TS格式支持）
        video_formats = [
            "MP4 (*.mp4)", "MKV (*.mkv)", "MOV (*.mov)", "AVI (*.avi)",
            "WMV (*.wmv)", "M4V (*.m4v)", "MPEG (*.mpeg *.mpg)",
            "VOB (*.vob)", "WEBM (*.webm)", "OGV (*.ogv)",
            "3GP (*.3gp)", "FLV (*.flv)", "F4V (*.f4v)", "SWF (*.swf)",
            "TS (*.ts)", "MTS (*.mts)", "M2TS (*.m2ts)"
        ]
        
        # 将所有格式组合成一个过滤器字符串
        all_formats = ";".join([f.split("(")[-1].rstrip(")") for f in video_formats])
        filter_str = "视频文件 (" + all_formats + ");;所有文件 (*)"
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择视频文件",
            get_path('video_transcode_input'),
            filter_str
        )

        if file_path:
            set_path('video_transcode_input', file_path)  # 保存路径
            self.path_input.setText(file_path)
            self.log_output.append(f"> 已选择文件: {file_path}")
    
    def select_output_directory(self):
        """打开目录选择对话框"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "选择输出目录",
            get_path('video_transcode_output'),
            QFileDialog.Option.ShowDirsOnly
        )

        if dir_path:
            set_path('video_transcode_output', dir_path)  # 保存路径
            self.output_path_input.setText(dir_path)
            self.log_output.append(f"> 已设置输出目录: {dir_path}")
    
    def start_transcode(self):
        """开始转码处理"""
        # 重置进度条
        self.progress_bar.setValue(0)
        
        file_path = self.path_input.text().strip()
        if not file_path:
            self.log_output.append("> 错误: 请先选择视频文件")
            return
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            self.log_output.append(f"> 错误: 文件不存在: {file_path}")
            return
        
        # 获取输出路径
        output_dir = self.output_path_input.text().strip()
        output_format = self.format_combo.currentText()
        
        # 获取转码参数
        resolution = self.resolution_combo.currentText()
        codec = self.codec_combo.currentText()
        quality = self.quality_combo.currentText()
        
        # 默认输出目录为当前目录
        if not output_dir:
            output_dir = os.path.dirname(file_path) or "."
        
        # 禁用开始按钮
        self.start_btn.setEnabled(False)
        self.log_output.append("=" * 50)
        self.log_output.append(f"> 开始处理: {file_path}")
        self.log_output.append(f"> 输出目录: {output_dir}")
        self.log_output.append(f"> GPU加速: {'是' if self.gpu_checkbox.isChecked() else '否'}")
        self.log_output.append(f"> 输出格式: {output_format}")
        self.log_output.append(f"> 分辨率: {resolution}")
        self.log_output.append(f"> 视频编码: {codec}")
        self.log_output.append(f"> 质量预设: {quality}")
        
        # 创建并启动转码线程
        self.transcode_thread = TranscodeThread(
            file_path, 
            output_format, 
            output_dir, 
            self.gpu_checkbox.isChecked(),
            resolution, 
            codec, 
            quality,
            self.ffmpeg_path,
            self.ffprobe_path  # 新增FFprobe路径
        )
        
        # 连接信号
        self.transcode_thread.update_log.connect(self.log_output.append)
        self.transcode_thread.update_progress.connect(self.progress_bar.setValueSmoothly)  # 平滑更新
        self.transcode_thread.finished.connect(self.on_transcode_finished)
        
        # 启动线程
        self.transcode_thread.start()
    
    def on_transcode_finished(self):
        """转码完成后的处理"""
        self.start_btn.setEnabled(True)
        # 确保进度条显示100%
        self.progress_bar.setValue(100)
        # 自动滚动到底部
        self.log_output.verticalScrollBar().setValue(
            self.log_output.verticalScrollBar().maximum()
        )
    
    def closeEvent(self, event):
        """窗口关闭时停止转码线程"""
        if self.transcode_thread and self.transcode_thread.isRunning():
            self.transcode_thread.stop()
            self.transcode_thread.wait(2000)  # 等待2秒
        super().closeEvent(event)
